import { TelegramIcon, TwitterIcon, WebsiteIcon } from '@/assets/icons';
import { TCoin } from '@/types';

export const SocialCoin = ({ coin }: { coin: TCoin }) => {
  return (
    <div className="flex justify-end gap-3">
      {coin?.socials?.telegram && (
        <a
          href={coin?.socials?.telegram}
          target="_blank"
          onClick={(e) => e.stopPropagation()}
        >
          <TelegramIcon className="hover:text-white-1000 text-white-500" />
        </a>
      )}

      {coin?.socials?.x && (
        <a
          href={coin?.socials?.x}
          target="_blank"
          onClick={(e) => e.stopPropagation()}
        >
          <TwitterIcon className="hover:text-white-1000 text-white-500" />
        </a>
      )}

      {coin?.socials?.website && (
        <a
          href={coin?.socials?.website}
          target="_blank"
          onClick={(e) => e.stopPropagation()}
        >
          <WebsiteIcon className="hover:text-white-1000 text-white-500" />
        </a>
      )}
    </div>
  );
};
