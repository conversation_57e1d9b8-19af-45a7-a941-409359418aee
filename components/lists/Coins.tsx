'use client';
import {
  CheckboxCheckedIcon,
  CheckboxIcon,
  ChevronDownIcon,
  DownArrIcon,
  GridIcon,
  KingIcon,
  SearchIcon,
} from '@/assets/icons';
import { HyperImg, LightningImg } from '@/assets/images';
import { SocialCoin } from '@/components/lists/SocialCoin';
import { useDebounce } from '@/hooks';
import {
  AppBroadcast,
  BROADCAST_EVENTS,
  TBroadcastEvent,
} from '@/libs/broadcast';
import { useMediaQuery } from '@/node_modules/react-responsive';
import rf from '@/services/RequestFactory';
import { TCoin } from '@/types';
import clsx from 'clsx';
import Image from 'next/image';
import Link from 'next/link';
import Tooltip from 'rc-tooltip';
import { useEffect, useState } from 'react';
import { AppNumber } from '../AppNumber';
import { AppTimeDisplay } from '../AppTimeDisplay';
import { EBondingDex } from '@/enums/dex';

const DEFAULT_LIMIT = 12;

const SORT_BY = [
  {
    label: 'Create Time',
    value: 'createdAt',
  },
  {
    label: 'Marketcap',
    value: 'mcapUsd',
  },
  {
    label: 'Last Trade',
    value: 'lastTrade',
  },
  {
    label: 'Last Reply',
    value: 'lastReply',
  },
];

const GradientRectangleFrame = ({
  width,
  height,
  strokeWidth,
  progress,
}: {
  width: number;
  height: number;
  strokeWidth: number;
  progress: number;
}) => {
  const perimeter = (width + height) * 2 - strokeWidth * 8;
  const progressLength = (progress / 100) * perimeter;

  return (
    <svg width={width} height={height}>
      <defs>
        <linearGradient id="borderGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#f6c500" />
          <stop offset="100%" stopColor="#33cc33" />
        </linearGradient>
      </defs>

      <rect
        x={strokeWidth / 2}
        y={strokeWidth / 2}
        width={width - strokeWidth}
        height={height - strokeWidth}
        stroke="#444" // Màu nền viền mờ
        strokeWidth={strokeWidth}
        fill="none"
        rx="4"
      />

      <rect
        x={strokeWidth / 2}
        y={strokeWidth / 2}
        width={width - strokeWidth}
        height={height - strokeWidth}
        stroke="url(#borderGradient)"
        strokeWidth={strokeWidth}
        fill="none"
        rx="4"
        strokeDasharray={perimeter}
        strokeDashoffset={perimeter - progressLength}
        strokeLinecap="round"
      />
    </svg>
  );
};

const CoinItem = ({ coin }: { coin: TCoin }) => {
  const isMobile = useMediaQuery({ query: '(max-width: 992px)' });
  return (
    <div className="cursor-pointer hover:bg-brand-900 rounded-[4px] p-3 border border-white-100 transition-all duration-300">
      <div className="flex gap-3 mb-3">
        <div className="relative">
          <div>
            <GradientRectangleFrame
              progress={coin?.bondingCurve}
              width={isMobile ? 80 : 126}
              height={isMobile ? 80 : 126}
              strokeWidth={2}
            />
          </div>
          <Image
            src={coin?.logoUri}
            alt={coin?.symbol}
            className="absolute object-contain tablet:h-[122px] h-[76px] rounded-[3px] left-[2px] top-[2px] right-[2px] bottom-[2px]"
            width={isMobile ? 76 : 122}
            height={isMobile ? 76 : 122}
          />
        </div>

        <div className="flex-1 w-full">
          <div className="flex justify-between items-center pb-[6px] border-b w-full mb-[6px] border-white-100">
            <div className="flex gap-1">
              <div className="body-md-semibold-14 text-brand-500">
                {coin?.name} (${coin?.symbol})
              </div>
              {coin?.isKing && <KingIcon />}
              <Image
                src={LightningImg}
                width={16}
                height={16}
                alt="lightning"
                className="w-4 h-4"
              />
              <div className="body-sm-regular-12">
                <AppTimeDisplay
                  timestamp={new Date(coin?.createdAt).getTime()}
                  isAgo
                  suffix=""
                  classNameWrapper="text-white-0"
                />
              </div>
            </div>
            <SocialCoin coin={coin} />
          </div>

          <div className="truncate line-clamp-[5] body-sm-light-12 text-white-500 whitespace-break-spaces">
            {coin?.description}
          </div>
        </div>
      </div>

      <div className="border border-white-100 px-[10px] py-1 grid grid-cols-2 rounded-[2px] bg-white-50">
        <div className="flex flex-col gap-1 pr-3 border-r border-white-100">
          <div className="flex gap-1">
            <div className="body-sm-light-12 text-white-500">Creator:</div>
            <Link
              href={`/profile/${coin?.creatorAddress}`}
              onClick={(e) => e.stopPropagation()}
            >
              <div className="body-sm-regular-12">
                {coin?.creatorAddress?.slice(0, 6)}
              </div>
            </Link>
          </div>
          <div className="flex gap-1">
            <div className="body-sm-light-12 text-white-500">Hype reward:</div>
            <div className="body-sm-regular-12 flex items-center gap-1">
              {coin?.hypeReward || 0}{' '}
              <Image src={HyperImg} width={14} height={14} alt="hyperliquid" />
            </div>
          </div>
        </div>
        <div className="flex flex-col gap-1 pl-3">
          <div className="flex gap-1">
            <div className="body-sm-light-12 text-white-500">Market cap:</div>
            <div className="text-brand-500">
              <AppNumber
                value={coin?.mcapUsd}
                isForUSD
                className="body-sm-regular-12"
              />
            </div>
          </div>
          <div className="flex gap-1">
            <div className="body-sm-light-12 text-white-500">Holders:</div>
            <div className="body-sm-regular-12 text-brand-500">--</div>
          </div>
        </div>
      </div>
    </div>
  );
};

const CoinList = () => {
  const [coins, setCoins] = useState<any[]>([]);
  const [params, setParams] = useState<any>({
    sortBy: 'createdAt',
    page: 1,
    limit: DEFAULT_LIMIT,
  });
  const [search, setSearch] = useState<string>('');
  const [isListed, setIsListed] = useState<boolean>(false);
  const [totalPage, setTotalPage] = useState<number>(0);

  const debounceSearch = useDebounce(search, 1000);

  useEffect(() => {
    const fetchCoins = async () => {
      const paramFilter = {
        ...params,
        search,
      } as any;

      if (isListed) {
        paramFilter.dex = EBondingDex.HYPERSWAP;
      }
      const result = await rf
        .getRequest('CoinRequest')
        .getCoin({ ...paramFilter });
      setCoins(result?.docs);
      setTotalPage(result?.totalPages);
    };
    fetchCoins().then();
  }, [params, debounceSearch, isListed]);

  const sortBySelected = SORT_BY.find((x) => x?.value === params?.sortBy);

  const handleWhenCoinCreated = async (event: TBroadcastEvent) => {
    const data = JSON.parse(event?.detail);
    if (!!data?.tokenAddress) {
      const coinDetail = await rf
        .getRequest('CoinRequest')
        .getCoinDetails(data?.tokenAddress);
      setCoins((prev) => [coinDetail, ...prev?.slice(0, -1)]);
    }
  };

  useEffect(() => {
    AppBroadcast.on(BROADCAST_EVENTS.COIN_CREATED, handleWhenCoinCreated);
    return () => {
      AppBroadcast.remove(BROADCAST_EVENTS.COIN_CREATED, handleWhenCoinCreated);
    };
  }, []);

  return (
    <div className="w-full">
      <div className="flex px-4 tablet:flex-row flex-col gap-4 items-center justify-between pb-3 max-w-[1376px] mx-auto border-b border-white-100 tablet:mb-8 mb-3">
        <div className="flex gap-4 justify-between items-center w-full tablet:w-auto">
          <div className="tablet:bg-white-50 flex items-center gap-[4px] tablet:p-2 p-1 tablet:h-[40px] tablet:rounded-[6px] tablet:min-w-[500px] w-[280px] border-b tablet:border border-white-100">
            <SearchIcon
              width={16}
              height={16}
              viewBox="0 0 20 20"
              className="text-white-300 w-16px h-16px"
            />
            <input
              placeholder="Search token"
              className="placeholder:text-white-300 tablet:body-md-light-14 body-xs-regular-10 bg-transparent outline-none flex-1 truncate"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
            />
          </div>
          <div className="p-[6px] h-max bg-brand-900 rounded-[4px] cursor-pointer tablet:hidden block">
            <GridIcon />
          </div>
        </div>

        <div className="flex gap-4 items-center justify-between tablet:justify-end w-full tablet:w-auto">
          <div
            className="flex gap-2 items-center cursor-pointer"
            onClick={() => setIsListed(!isListed)}
          >
            {isListed ? <CheckboxCheckedIcon /> : <CheckboxIcon />}
            <div className="tablet:body-md-regular-14 body-sm-regular-12">
              Listed on Hyperswap
            </div>
          </div>

          <Tooltip
            placement="bottom"
            styles={{
              body: {
                width: 160,
                padding: 0,
                backgroundColor: '#08090c',
                border: '1px solid #FFFFFF33',
              },
            }}
            overlay={
              <div className="p-2">
                {SORT_BY.map((item) => {
                  const isActive = item?.value === params?.sortBy;
                  return (
                    <div
                      key={item?.value}
                      className={clsx(
                        'p-2 hover:bg-white-100 rounded-[4px] body-sm-regular-12 cursor-pointer',
                        isActive ? 'text-blue-500 ' : '',
                      )}
                      onClick={() =>
                        setParams((prev: any) => ({
                          ...prev,
                          sortBy: item?.value,
                        }))
                      }
                    >
                      {item?.label}
                    </div>
                  );
                })}
              </div>
            }
            showArrow={false}
          >
            <div className="cursor-pointer bg-white-50 min-w-[160px] border justify-between border-white-150 rounded-[4px] p-2 flex items-center gap-1 body-sm-regular-12">
              <div>
                Sort:{' '}
                <span className="text-white-0">{sortBySelected?.label}</span>
              </div>
              <DownArrIcon />
            </div>
          </Tooltip>

          <div className="p-[6px] bg-brand-900 rounded-[4px] cursor-pointer tablet:block hidden">
            <GridIcon />
          </div>
        </div>
      </div>

      <div className="max-w-[1376px] mx-auto tablet:mb-[80px] px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 tablet:gap-8 md:gap-4 gap-2.5">
          {coins?.map((item: TCoin) => (
            <Link
              key={item?.id || item?.tokenAddress}
              href={`/coins/${item?.tokenAddress}`}
            >
              <CoinItem coin={item} />
            </Link>
          ))}
        </div>
      </div>

      {!!coins.length && (
        <div className="flex items-center py-4 bg-black-900 gap-4 justify-center tablet:fixed tablet:bottom-[68px] bottom-[61px] left-0 right-0 tablet:border-t border-white-100">
          <div
            className={clsx(
              'p-2 rounded-[4px] flex gap-1 items-center body-sm-regular-12 border border-white-150 transition-colors',
              params?.page < 2
                ? 'cursor-not-allowed text-white-500'
                : 'cursor-pointer',
            )}
            onClick={() => {
              if (params?.page < 2) return;
              setParams((prev: any) => ({ ...prev, page: prev?.page - 1 }));
            }}
          >
            <ChevronDownIcon className="rotate-90" />
            Back
          </div>

          <div className="body-md-light-14">{params?.page}</div>
          <div
            className={clsx(
              'p-2 rounded-[4px] flex gap-1 items-center body-sm-regular-12 border border-white-150 transition-colors',
              params?.page === totalPage
                ? 'cursor-not-allowed text-white-500'
                : 'cursor-pointer',
            )}
            onClick={() => {
              if (params?.page === totalPage) return;
              setParams((prev: any) => ({ ...prev, page: prev?.page + 1 }));
            }}
          >
            Next
            <ChevronDownIcon className="rotate-[270deg]" />
          </div>
        </div>
      )}
    </div>
  );
};

export default CoinList;
