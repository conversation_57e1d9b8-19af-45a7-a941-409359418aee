import { TCoin } from '@/types';
// import Image from 'next/image';
import React from 'react';
import AppAddress from './AppAddress';

interface AppCreatorProps {
  coin: TCoin | null;
}

const AppCreator: React.FC<AppCreatorProps> = ({ coin }) => {
  if (!coin) return null;

  return (
    <div className="flex items-center gap-2">
      {/*{coin?.creator?.username && coin?.creator?.avatar ? (*/}
      {/*  <div className="flex gap-[6px]">*/}
      {/*    {isDetail && (*/}
      {/*      <span className="text-body-sm text-white-700 font-normal">*/}
      {/*        Created by*/}
      {/*      </span>*/}
      {/*    )}*/}
      {/*    <div className="flex items-center">*/}
      {/*      {coin?.creator?.avatar && (*/}
      {/*        <Image*/}
      {/*          src={coin?.creator?.avatar || ''}*/}
      {/*          alt=""*/}
      {/*          width={16}*/}
      {/*          height={16}*/}
      {/*          className="w-16px h-16px rounded-full object-cover mr-1"*/}
      {/*        />*/}
      {/*      )}*/}
      {/*      <div*/}
      {/*        onClick={(e) => {*/}
      {/*          e.stopPropagation();*/}
      {/*          e.preventDefault();*/}
      {/*          if (coin?.creator?.linkX) {*/}
      {/*            window.open(coin?.creator?.linkX, '_blank');*/}
      {/*          }*/}
      {/*        }}*/}
      {/*        className="text-body-sm cursor-pointer"*/}
      {/*      >*/}
      {/*        {coin?.creator?.username ? (*/}
      {/*          coin?.creator?.username*/}
      {/*        ) : (*/}
      {/*          <AppAddress address={coin?.creatorAddress} isCopy />*/}
      {/*        )}*/}
      {/*      </div>*/}
      {/*    </div>*/}
      {/*  </div>*/}
      {/*) : (*/}
        <div className="flex gap-[6px]">
          <span className="text-body-sm">
            <AppAddress address={coin?.creatorAddress} isCopy />
          </span>
        </div>
      {/*)}*/}
    </div>
  );
};

export default AppCreator;
