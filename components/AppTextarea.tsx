import clsx from 'clsx';
import {
  DetailedHTMLProps,
  forwardRef,
  InputHTMLAttributes,
  ReactNode,
} from 'react';

interface IProps
  extends DetailedHTMLProps<
    InputHTMLAttributes<HTMLTextAreaElement>,
    HTMLTextAreaElement
  > {
  rootClassName?: string;
  icon?: ReactNode;
  rows?: number;
  label?: string;
  required?: boolean;
  error?: string;
}

const AppTextarea = forwardRef(
  (props: IProps, ref: React.Ref<HTMLTextAreaElement>) => {
    const {
      rootClassName,
      className,
      icon,
      rows = 10,
      label,
      required,
      error,
      ...restProps
    } = props;
    return (
      <div>
        {label && (
          <label className="block text-xs mb-2 font-semibold text-white-700">
            {label}{' '}
            {required && <span className="text-red-500 text-[10px]">*</span>}
          </label>
        )}
        <div
          className={clsx(
            'flex items-center gap-[8px] p-[8px] rounded-[6px] border border-white-100 bg-white-100',
            rootClassName,
          )}
        >
          {!!icon && icon}
          <textarea
            rows={rows}
            ref={ref}
            className={clsx(
              'w-full bg-transparent outline-none text-sm placeholder:text-white-300 placeholder:text-xs',
              className,
            )}
            {...restProps}
          />
        </div>
        {error && <div className="text-[10px] text-red-500">{error}</div>}
      </div>
    );
  },
);

AppTextarea.displayName = 'AppTextarea';
export default AppTextarea;
