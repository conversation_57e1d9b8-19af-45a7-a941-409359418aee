'use client';

import { CopyIcon } from '@/assets/icons';
import { copyToClipboard } from '@/utils/helper';
import React from 'react';

export const AppCopy = ({
  className,
  message,
  children,
}: {
  className?: string;
  message: string;
  children?: React.ReactNode;
}) => {
  return (
    <CopyIcon
      className={`w-4 cursor-pointer ${className}`}
      onClick={() => copyToClipboard(message)}
    >
      {children}
    </CopyIcon>
  );
};

export default AppCopy;
