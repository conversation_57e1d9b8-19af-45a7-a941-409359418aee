'use client';
import React from 'react';
import { AppButton } from '@/components/AppButton';
import { useCreatorReward } from '@/hooks/useCreatorReward';
import { formatNumber } from '@/utils/format';
import { FlashIcon } from '@/assets/icons';
import clsx from 'clsx';

const CreatorReward: React.FC = () => {
  const {
    creatorPoolInfo,
    isLoading,
    isClaiming,
    isEligibleCreator,
    claimCreatorPool,
    hasRewards,
    rewardAmount,
  } = useCreatorReward();

  // Don't render if user is not eligible creator or no pool exists
  if (!isEligibleCreator || !creatorPoolInfo) {
    return null;
  }

  const handleClaimReward = async () => {
    await claimCreatorPool();
  };

  const rewardAmountFormatted = formatNumber(parseFloat(rewardAmount));
  const hasClaimableRewards = hasRewards && parseFloat(rewardAmount) > 0;

  return (
    <div className="border-b border-white-50 bg-black-100">
      <div className="p-4">
        <div className="flex items-center justify-between mb-3">
          <h3 className="body-md-medium-14 text-white-1000">Creator Rewards</h3>
          {isLoading && (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white-500"></div>
          )}
        </div>

        <div className="space-y-3">
          {/* Reward Amount Display */}
          <div className="flex items-center justify-between">
            <span className="body-sm-regular-12 text-white-500">
              Available Rewards:
            </span>
            <div className="flex items-center gap-1">
              <span
                className={clsx(
                  'body-sm-medium-12',
                  hasClaimableRewards ? 'text-green-400' : 'text-white-500',
                )}
              >
                {rewardAmountFormatted} ETH
              </span>
            </div>
          </div>

          {/* Creator Info */}
          <div className="flex items-center justify-between">
            <span className="body-sm-regular-12 text-white-500">Creator:</span>
            <span className="body-sm-regular-12 text-white-1000 font-mono">
              {creatorPoolInfo?.creator?.slice(0, 6)}...
              {creatorPoolInfo?.creator?.slice(-4)}
            </span>
          </div>

          {/* Claim Button */}
          <div className="pt-2">
            <AppButton
              className="w-full flex justify-center h-[40px] !rounded-[4px] !gap-1"
              onClick={handleClaimReward}
              disabled={!hasClaimableRewards || isClaiming || isLoading}
              isLoading={isClaiming}
              type="button"
              buttonType={
                hasClaimableRewards ? 'contained-brand' : 'outlined-grey'
              }
            >
              {isClaiming ? (
                'Claiming...'
              ) : hasClaimableRewards ? (
                <>
                  <FlashIcon /> Claim Reward
                </>
              ) : (
                'No Rewards Available'
              )}
            </AppButton>
          </div>

          {/* Helper Text */}
          {hasClaimableRewards && (
            <p className="body-xs-light-10 text-white-500 text-center">
              Click to claim your creator rewards
            </p>
          )}

          {!hasClaimableRewards && creatorPoolInfo && (
            <p className="body-xs-light-10 text-white-500 text-center">
              No rewards available at this time
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default CreatorReward;
