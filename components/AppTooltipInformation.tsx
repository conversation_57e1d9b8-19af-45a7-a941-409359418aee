import { InformationCircleIcon } from '@/assets/icons';
import Tooltip from 'rc-tooltip';
import { ReactNode } from 'react';
interface IProps {
  content: ReactNode | string;
}
const AppTooltipInformation = ({ content }: IProps) => {
  return (
    <Tooltip
      overlay={content}
      styles={{
        body: {
          maxWidth: 300,
        },
      }}
      placement="left"
    >
      <InformationCircleIcon className="text-white-500 cursor-pointer" />
    </Tooltip>
  );
};
export default AppTooltipInformation;
