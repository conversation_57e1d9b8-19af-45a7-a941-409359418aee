import { LoadingIcon } from '@/assets/icons';
import { usePrivy } from '@privy-io/react-auth';
import clsx from 'clsx';
import { ButtonHTMLAttributes, DetailedHTMLProps, ReactNode } from 'react';

interface IProps
  extends DetailedHTMLProps<
    ButtonHTMLAttributes<HTMLButtonElement>,
    HTMLButtonElement
  > {
  children: ReactNode;
  className?: string;
  classNameBtnAuth?: string;
  isLoading?: boolean;
  isButtonAuth?: boolean;
  buttonType?:
    | 'contained-brand'
    | 'outlined-brand'
    | 'outlined-grey'
    | 'contained-grey'
    | 'contained-brand-900'
    | 'contained-blue'
    | 'contained-white'
    | 'outlined-black';
}

export const AppButton = ({
  children,
  className = '',
  buttonType = 'contained-brand',
  isLoading,
  disabled,
  isButtonAuth = false,
  classNameBtnAuth = '',

  ...restProps
}: IProps) => {
  const { authenticated, login } = usePrivy();

  if (!authenticated && !!isButtonAuth) {
    return (
      <button
        className={clsx(
          'px-4 py-2 text-[14px] flex items-center justify-center font-medium rounded-lg transition-colors hover:opacity-90 disabled:opacity-50 disabled:cursor-not-allowed bg-brand-500 text-black-900',
          classNameBtnAuth,
        )}
        {...restProps}
        disabled={false}
        onClick={login}
      >
        <div className="flex items-center gap-2 justify-center font-[500]">
          Log In
          {isLoading && (
            <LoadingIcon className="animate-spin h-5 w-5 mr-2 text-white" />
          )}
        </div>
      </button>
    );
  }

  const mapClassName = {
    'contained-brand': 'bg-brand-500 text-black-900',
    'contained-brand-900': 'bg-brand-900 text-brand-500',
    'outlined-brand': 'bg-white-50 text-brand-500 border border-brand-800',
    'contained-grey': 'bg-white-100',
    'outlined-grey': 'bg-white-50 text-white-1000 border border-white-100',
    'contained-blue': 'bg-[#1DA1F2] text-white-1000 border border-white-100 ',
    'contained-white': 'bg-white-1000 text-black-900 ',
    'outlined-black': 'bg-black-900 text-white-1000 border border-white-150 ',
  };
  return (
    <button
      className={clsx(
        'px-4 py-2 text-[14px] flex items-center justify-center font-medium rounded-lg transition-colors hover:opacity-90 disabled:opacity-50 disabled:cursor-not-allowed',
        mapClassName[buttonType],
        className,
      )}
      disabled={disabled || isLoading}
      {...restProps}
    >
      <div className="flex items-center gap-2 justify-center">
        {children}
        {isLoading && (
          <LoadingIcon className="animate-spin h-5 w-5 mr-2 text-white" />
        )}
      </div>
    </button>
  );
};
