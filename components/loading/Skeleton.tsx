import React from 'react';
import clsx from 'clsx';

const Skeleton = ({
  isLoading = true,
  children = <></>,
  className = 'h-5 w-10',
}: {
  className?: string;
  children?: JSX.Element;
  isLoading?: boolean;
}) => {
  return (
    <>
      {isLoading ? (
        <div className="animate-pulse space-y-4 flex items-center">
          <div className={clsx('rounded bg-white-100', className)}></div>
        </div>
      ) : (
        children
      )}
    </>
  );
};

export default Skeleton;
