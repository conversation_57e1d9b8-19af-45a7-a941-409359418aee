import BigNumber from 'bignumber.js';
import { SymbolValueFormatterFormatOptions } from '@/libs/charting_library/charting_library';

export const formatPriceWithSubscriptZeros = (
  price: number,
  signPositive?: SymbolValueFormatterFormatOptions,
): string => {
  if (new BigNumber(price).comparedTo(1) >= 0) {
    return new BigNumber(price).toFixed(8).toString();
  }

  if (new BigNumber(price || 0).isZero()) {
    return '0';
  }

  const subscriptNumbers = [
    '₀',
    '₁',
    '₂',
    '₃',
    '₄',
    '₅',
    '₆',
    '₇',
    '₈',
    '₉',
    '₁₀',
    '₁₁',
    '₁₂',
    '₁₃',
    '₁₄',
    '₁₅',
    '₁₆',
    '₁₇',
    '₁₈',
    '₁₉',
  ];
  const parts = new BigNumber(price).toFixed(18).split('.');

  if (parts.length > 1) {
    const decimalPart = parts[1];

    // Find leading zeros in the decimal part
    const leadingZerosCount = decimalPart.match(/^0+/)?.[0].length || 0;

    if (leadingZerosCount >= 3) {
      const significantPart = decimalPart.slice(leadingZerosCount);

      // Format the leading zeros with subscript
      const leadingZerosWithSubscript =
        leadingZerosCount > 0
          ? '0.' + '0' + subscriptNumbers[leadingZerosCount] // Insert the subscript corresponding to the number of zeros
          : '0.';

      return leadingZerosWithSubscript + significantPart.slice(0, 5); // Combine whole part and decimal part with subscript zeros
    }
  }

  return new BigNumber(price).toFixed(8).toString();
};
