'use client';

import {
  Bar,
  ChartingLibraryWidgetOptions,
  GetMarksCallback,
  HistoryCallback,
  IBasicDataFeed,
  LibrarySymbolInfo,
  Mark,
  PeriodParams,
  ResolutionString,
  ResolveCallback,
  SubscribeBarsCallback,
  ThemeName,
  widget,
} from '@/libs/charting_library';
import rf from '@/services/RequestFactory';
import { TCandle, TradingType } from '@/types/pair';
import * as React from 'react';
import { useEffect, useRef, useState, useMemo, useCallback } from 'react';
import { CANDLE_TYPE, CANDLE_UNIT, SYMBOL_TYPE } from './utils/consts';
import {
  DATAFEED_CONFIGURATION,
  DISABLED_FEATURES,
  ENABLED_FEATURES,
  formatSymbolPair,
  getClientTimezone,
  TChartSetting,
} from './utils/setting';
// import { NavBar } from './parts/Navbar';
import {
  AppBroadcast,
  BROADCAST_EVENTS,
  TBroadcastEvent,
} from '@/libs/broadcast';
// import { getCirculatingSupply } from '@/utils/pair';
import retry from 'async-retry';
import { useMediaQuery } from 'react-responsive';
import {
  convertResolutionString2Unit,
  getVolume,
  isInvalidCandle,
  mappingTradeToLastCandle,
} from './utils/candle';
import {
  createSwitchPriceOrMcapButton,
  createSwitchPriceType,
  // getTitle,
  priceFormatterFactory,
} from './utils/override';
import { useCoinPageContext } from '@/app/coins/[slug]/provider';

export interface ChartContainerProps {
  symbol: ChartingLibraryWidgetOptions['symbol'];
  interval: ChartingLibraryWidgetOptions['interval'];
  libraryPath: ChartingLibraryWidgetOptions['library_path'];
  chartsStorageApiVersion: ChartingLibraryWidgetOptions['charts_storage_api_version'];
  clientId: ChartingLibraryWidgetOptions['client_id'];
  userId: ChartingLibraryWidgetOptions['user_id'];
  fullscreen: ChartingLibraryWidgetOptions['fullscreen'];
  autosize: ChartingLibraryWidgetOptions['autosize'];
  container: ChartingLibraryWidgetOptions['container'];
}

type TOtherTrades = {
  DEV: boolean;
  INSIDER: boolean;
  SNIPER: boolean;
};

const DEFAULT_RESOLUTION_STRING = '1S' as ResolutionString;

const TradingView = React.memo(() => {
  const renderId = `render-${Date.now()}-${Math.random()
    .toString(36)
    .substr(2, 9)}`;
  console.log(`🎨 [${renderId}] TradingView component render`, {
    timestamp: new Date().toISOString(),
  });

  const isMobile = useMediaQuery({ query: '(max-width: 768px)' });
  const [isChartHeaderReady, setIsChartHeaderReady] = useState(false);
  const [isChartReady, setIsChartReady] = useState(false);
  const tradingViewChart = useRef<any>();
  const lastCandleRef = useRef<Bar>({} as Bar);
  const { coinAddress, coinId, coinSymbol } = useCoinPageContext();

  console.log(`📊 [${renderId}] TradingView component state`, {
    timestamp: new Date().toISOString(),
    coinAddress,
    coinId,
    coinSymbol,
    isChartHeaderReady,
    isChartReady,
    isMobile,
    hasChart: !!tradingViewChart.current,
  });

  const [chartSetting, setChartSetting] = useState<TChartSetting>({
    type: CANDLE_TYPE.PRICE,
    unit: CANDLE_UNIT.USD,
  } as any);

  // Log chart setting changes
  const prevChartSetting = useRef<TChartSetting>();
  useEffect(() => {
    if (
      prevChartSetting.current &&
      (prevChartSetting.current.type !== chartSetting.type ||
        prevChartSetting.current.unit !== chartSetting.unit)
    ) {
      console.log(`⚙️ [SETTING-CHANGE] Chart setting updated`, {
        timestamp: new Date().toISOString(),
        previous: prevChartSetting.current,
        current: chartSetting,
        stackTrace: new Error().stack?.split('\n').slice(1, 5),
      });
    }
    prevChartSetting.current = chartSetting;
  }, [chartSetting]);

  const chartSettingRef = useRef<any>(chartSetting);
  const onTickRef = useRef<SubscribeBarsCallback>({} as SubscribeBarsCallback);
  const resolutionStringRef = useRef<ResolutionString>(
    DEFAULT_RESOLUTION_STRING,
  );

  const chartContainerRef =
    useRef<HTMLDivElement>() as React.MutableRefObject<HTMLInputElement>;

  const defaultProps: Omit<ChartContainerProps, 'container'> = {
    symbol: 'ETHUSDT',
    interval: DEFAULT_RESOLUTION_STRING,
    libraryPath: '/static/charting_library/',
    chartsStorageApiVersion: '1.1',
    clientId: 'tradingview.com',
    userId: 'public_user_id',
    fullscreen: false,
    autosize: false,
  };

  useEffect(() => {
    console.log('🔄 [EFFECT-1] coinAddress changed, resetting chart state', {
      timestamp: new Date().toISOString(),
      coinAddress,
      stackTrace: new Error().stack?.split('\n').slice(1, 3),
    });
    setIsChartReady(false);
    setIsChartHeaderReady(false);
  }, [coinAddress]);

  const getActiveChart = () => {
    try {
      return tradingViewChart.current?.activeChart();
    } catch (e) {
      console.error('getActiveChart', e);
      return null;
    }
  };

  useEffect(() => {
    console.log('🔄 [EFFECT-2] Chart ready state changed', {
      timestamp: new Date().toISOString(),
      isChartHeaderReady,
      isChartReady,
      hasChart: !!tradingViewChart?.current,
    });

    if (!isChartHeaderReady || !isChartReady) {
      return;
    }

    if (tradingViewChart?.current) {
      console.log('🔄 [EFFECT-2] Refreshing marks');
      getActiveChart()?.refreshMarks();
    }
  }, [isChartHeaderReady, isChartReady]);

  const getBars = useCallback(
    async (
      symbolInfo: LibrarySymbolInfo,
      resolution: ResolutionString,
      periodParams: PeriodParams,
      onResult: HistoryCallback,
    ) => {
      const callId = `getBars-${Date.now()}-${Math.random()
        .toString(36)
        .substr(2, 9)}`;
      console.log(`🔍 [${callId}] getBars called`, {
        timestamp: new Date().toISOString(),
        coinAddress,
        resolution,
        periodParams: {
          from: periodParams.from,
          to: periodParams.to,
          countBack: periodParams.countBack,
          firstDataRequest: periodParams.firstDataRequest,
        },
        chartSetting: chartSettingRef.current,
        stackTrace: new Error().stack?.split('\n').slice(1, 5),
      });

      if (Number(periodParams.from) <= 0 || Number(periodParams.to) <= 0) {
        console.log(
          `🚫 [${callId}] getBars early return - invalid time params`,
        );
        return onResult([], {
          noData: true,
        });
      }

      const to = periodParams.to;
      const resolutionUnit = convertResolutionString2Unit(resolution);

      try {
        if (!!coinAddress) {
          const queryBy =
            chartSettingRef.current.type === CANDLE_TYPE.PRICE
              ? chartSettingRef.current.unit === CANDLE_UNIT.TOKEN_QUOTE
                ? CANDLE_TYPE.PRICE
                : CANDLE_TYPE.PRICE_USD
              : chartSettingRef.current.unit === CANDLE_UNIT.TOKEN_QUOTE
              ? CANDLE_TYPE.MCAP
              : CANDLE_TYPE.MCAP_USD;

          const apiParams = {
            limit: Math.min(periodParams.countBack, 300),
            to: to,
            tokenAddress: coinAddress,
            queryBy: queryBy,
            resolution: resolutionUnit,
          };

          console.log(`📡 [${callId}] Making API call to /api/v1/candle`, {
            timestamp: new Date().toISOString(),
            params: apiParams,
            url: `/api/v1/candle?limit=${apiParams.limit}&to=${apiParams.to}&tokenAddress=${apiParams.tokenAddress}&queryBy=${apiParams.queryBy}&resolution=${apiParams.resolution}`,
          });

          const res = await rf
            .getRequest('CandleRequest')
            .getCandles(apiParams);

          console.log(`✅ [${callId}] API response received`, {
            timestamp: new Date().toISOString(),
            candleCount: res?.candles?.length || 0,
          });

          const listCandle =
            res?.candles.sort(
              (a: TCandle, b: TCandle) =>
                Number(a.timestamp) - Number(b.timestamp),
            ) || [];

          const validListCandle = listCandle.filter((candle: TCandle) => {
            return !isInvalidCandle(candle);
          });

          if (!validListCandle.length) {
            console.log(`📊 [${callId}] No valid candles, returning noData`);
            return onResult([], { noData: true });
          }

          const bars: any = validListCandle.map((candle: TCandle) => {
            return {
              time: +candle.timestamp * 1000,
              open: +candle.open,
              high: +candle.high,
              low: +candle.low,
              close: +candle.close,
              volume: getVolume(chartSettingRef.current, candle),
            };
          });

          const lastCandle = bars[bars.length - 1];

          if (periodParams.firstDataRequest) {
            lastCandleRef.current = lastCandle;
            console.log(
              `🎯 [${callId}] First data request - storing last candle`,
            );
          }

          console.log(
            `📊 [${callId}] Returning ${bars.length} bars to TradingView`,
          );
          onResult(bars, { noData: false });
        } else {
          console.log(`🚫 [${callId}] No coinAddress, skipping API call`);
        }
      } catch (error: any) {
        console.log(`❌ [${callId}] getBars error:`, error);
        onResult([], { noData: true });
      }
    },
    [coinAddress],
  );

  const handleWhenNewTransaction = (event: TBroadcastEvent) => {
    const transaction: any = JSON.parse(event.detail);

    if (transaction.tokenAddress !== coinAddress) {
      return;
    }

    if (
      !lastCandleRef.current ||
      !resolutionStringRef.current ||
      typeof onTickRef.current !== 'function'!
    ) {
      return;
    }

    if ([TradingType.ADD, TradingType.REMOVE].includes(transaction.tradeType)) {
      return;
    }

    const lastCandleUpdated = mappingTradeToLastCandle(
      transaction,
      lastCandleRef.current,
      resolutionStringRef.current,
      onTickRef.current,
      chartSettingRef.current,
    );

    if (lastCandleUpdated) {
      lastCandleRef.current = lastCandleUpdated;
    }

    // if (showMyTradesRef.current && isMyTrade(transaction)) {
    //   getActiveChart()?.refreshMarks();
    // }
  };

  // const isMyTrade = (transaction: TPairTransaction) => {
  //   if (!walletsRef.current?.length) {
  //     return false;
  //   }
  //   const walletAddresses = walletsRef.current?.map(
  //     (wallet: TWallet) => wallet.address,
  //   );
  //   return walletAddresses?.includes(transaction.maker.address);
  // };

  // const handleWhenRefreshData = (event: TBroadcastEvent) => {
  //   console.log('handleWhenRefreshData', event);
  //   setForceReset((prev) => prev + 1);
  // };

  useEffect(() => {
    if (!coinAddress) return;
    AppBroadcast.on(BROADCAST_EVENTS.TRADES, handleWhenNewTransaction);
    // AppBroadcast.on(BROADCAST_EVENTS.REFRESH_DATA, handleWhenRefreshData);
    return () => {
      AppBroadcast.remove(BROADCAST_EVENTS.TRADES, handleWhenNewTransaction);
      // AppBroadcast.remove(BROADCAST_EVENTS.REFRESH_DATA, handleWhenRefreshData);
    };
  }, [coinAddress]);

  const subscribeBars = useCallback(
    (
      symbolInfo: LibrarySymbolInfo,
      resolution: ResolutionString,
      onTick: SubscribeBarsCallback,
      listenerGuid: string,
      onResetCacheNeededCallback: () => void,
    ) => {
      console.log('subscribeBars', {
        symbolInfo,
        resolution,
        onTick,
        listenerGuid,
        onResetCacheNeededCallback,
      });
      resolutionStringRef.current = resolution;
      onTickRef.current = onTick;
    },
    [],
  );

  const getMarks = useCallback(
    async (
      symbolInfo: LibrarySymbolInfo,
      from: number,
      to: number,
      onDataCallback: GetMarksCallback<Mark>,
      resolution: ResolutionString,
    ) => {
      console.log('getMarks', {
        from,
        to,
        resolution,
      });
      const marks: Mark[] = [];

      return onDataCallback(marks);
    },
    [],
  );

  const datafeed: IBasicDataFeed = useMemo(() => {
    const datafeedId = `datafeed-${Date.now()}-${Math.random()
      .toString(36)
      .substr(2, 9)}`;
    console.log(`🏗️ [${datafeedId}] Creating new datafeed object`, {
      timestamp: new Date().toISOString(),
      coinSymbol,
      isMobile,
      dependencies: {
        getBars: !!getBars,
        getMarks: !!getMarks,
        subscribeBars: !!subscribeBars,
      },
      stackTrace: new Error().stack?.split('\n').slice(1, 5),
    });

    return {
      onReady: (callback: any) => {
        console.log(`🚀 [${datafeedId}] onReady called`);
        setTimeout(() => callback(DATAFEED_CONFIGURATION));
      },
      searchSymbols: () => {
        console.log(`🔍 [${datafeedId}] searchSymbols called`);
      },
      resolveSymbol: async (
        symbolName: string,
        onSymbolResolvedCallback: ResolveCallback,
      ) => {
        console.log(`🎯 [${datafeedId}] resolveSymbol called`, {
          symbolName,
          coinSymbol,
        });
        const symbolInfo: LibrarySymbolInfo = {
          ticker: formatSymbolPair(coinSymbol, isMobile),
          name: formatSymbolPair(coinSymbol, isMobile),
          description: formatSymbolPair(coinSymbol, isMobile),
          pricescale: 10 ** 10,
          volume_precision: 3,
          minmov: 1,
          minmove2: 4,
          exchange: 'printmeme.io',
          listed_exchange: '',
          session: '24x7',
          has_intraday: true,
          has_daily: true,
          format: 'price',
          has_seconds: true,
          seconds_multipliers: ['1'],
          has_weekly_and_monthly: false,
          intraday_multipliers: DATAFEED_CONFIGURATION.intraday_multipliers,
          timezone: getClientTimezone(),
          type: SYMBOL_TYPE.bitcoin,
          supported_resolutions:
            DATAFEED_CONFIGURATION.supported_resolutions as ResolutionString[],
        };
        onSymbolResolvedCallback(symbolInfo);
      },
      getBars,
      getMarks,
      subscribeBars,
      unsubscribeBars: () => {
        console.log(`🔌 [${datafeedId}] unsubscribeBars called`);
      },
    };
  }, [coinSymbol, isMobile, getBars, getMarks, subscribeBars]);

  const getInitInterval = () => {
    const params = new URLSearchParams(window.location.search);
    const resolutionParam = params.get('resolutionString');
    if (
      resolutionParam &&
      DATAFEED_CONFIGURATION.supported_resolutions.includes(resolutionParam)
    ) {
      return resolutionParam;
    }
    return (
      localStorage.getItem('tradingview.chart.lastUsedTimeBasedResolution') ||
      (defaultProps.interval as ChartingLibraryWidgetOptions['interval'])
    );
  };

  useEffect(() => {
    const effectId = `widget-effect-${Date.now()}-${Math.random()
      .toString(36)
      .substr(2, 9)}`;
    console.log(`🏗️ [${effectId}] TradingView widget useEffect triggered`, {
      timestamp: new Date().toISOString(),
      coinAddress,
      coinId,
      hasExistingChart: !!tradingViewChart.current,
      stackTrace: new Error().stack?.split('\n').slice(1, 5),
    });

    if (!coinAddress) {
      console.log(`🚫 [${effectId}] No coinAddress, skipping widget creation`);
      return;
    }

    console.log(
      `🔄 [${effectId}] Resetting chart state and creating new widget`,
    );
    setIsChartReady(false);
    setIsChartHeaderReady(false);
    const initInterval = getInitInterval();
    const widgetOptions: ChartingLibraryWidgetOptions = {
      symbol: defaultProps.symbol as string,
      theme: 'Dark' as ThemeName,
      datafeed: datafeed,
      interval: initInterval as ResolutionString,
      container: chartContainerRef.current,
      library_path: defaultProps.libraryPath as string,

      locale: 'en',
      disabled_features: DISABLED_FEATURES,
      enabled_features: ENABLED_FEATURES,
      client_id: defaultProps.clientId,
      user_id: defaultProps.userId,
      fullscreen: defaultProps.fullscreen || false,
      autosize: defaultProps.autosize || true,
      custom_css_url: '/tradingview.css?id=211120241',
      timezone: getClientTimezone(),
      time_frames: [
        {
          text: '3M',
          resolution: '60' as ResolutionString,
          description: '3 months in 1 hour intervals',
        },
        {
          text: '1M',
          resolution: '30' as ResolutionString,
          description: '1 month in 30 minutes intervals',
        },
        {
          text: '5D',
          resolution: '5' as ResolutionString,
          description: '5 days in 5 minutes intervals',
        },
        {
          text: '1D',
          resolution: '1' as ResolutionString,
          description: '1 day in 1 minute intervals',
        },
      ],
      overrides: {
        'scalesProperties.fontSize': 13,
        'mainSeriesProperties.styledText.fontSize': 15, // size text
        volumePaneSize: 'small',
        'paneProperties.legendProperties.showSeriesOHLC': false,
        'paneProperties.background': '#121318',
        'mainSeriesProperties.candleStyle.upColor': '#569781',
        'mainSeriesProperties.candleStyle.downColor': '#d13845',
        'mainSeriesProperties.candleStyle.drawWick': true,
        'mainSeriesProperties.candleStyle.drawBorder': true,
        'mainSeriesProperties.candleStyle.borderColor': '#378658',
        'mainSeriesProperties.candleStyle.borderUpColor': '#16C782',
        'mainSeriesProperties.candleStyle.borderDownColor': '#EA3943',
        'mainSeriesProperties.candleStyle.wickUpColor': '#16C782',
        'mainSeriesProperties.candleStyle.wickDownColor': '#EA3943',
        'mainSeriesProperties.volCandlesStyle.height': 400,
      },
      studies_overrides: {
        'volume.volume.transparency': 50, // Set transparency of volume bars
      },
      custom_formatters: {
        priceFormatterFactory: (symbolInfo: any, minTick: any) => {
          return priceFormatterFactory(
            symbolInfo,
            minTick,
            chartSettingRef?.current,
          );
        },
      },
    } as any;

    console.log(`🎯 [${effectId}] Creating TradingView widget with options`, {
      timestamp: new Date().toISOString(),
      symbol: widgetOptions.symbol,
      interval: widgetOptions.interval,
      datafeedId: datafeed,
    });

    const tvWidget = new widget(widgetOptions);
    tradingViewChart.current = tvWidget;

    console.log(`✅ [${effectId}] TradingView widget created, awaiting ready`);
    awaitReady().then();

    return () => {
      console.log(`🗑️ [${effectId}] Cleaning up TradingView widget`);
      tvWidget.remove();
    };
  }, [coinAddress, coinId]);

  useEffect(() => {
    const settingChangeId = `setting-change-${Date.now()}-${Math.random()
      .toString(36)
      .substr(2, 9)}`;
    console.log(`⚙️ [${settingChangeId}] Chart setting changed`, {
      timestamp: new Date().toISOString(),
      chartSetting,
      isChartReady,
      hasChart: !!tradingViewChart.current,
      stackTrace: new Error().stack?.split('\n').slice(1, 3),
    });

    if (!isChartReady || !tradingViewChart.current) {
      console.log(
        `🚫 [${settingChangeId}] Chart not ready or missing, skipping reset`,
      );
      return;
    }

    try {
      console.log(
        `🔄 [${settingChangeId}] Resetting chart data due to setting change`,
      );
      const activeChart = getActiveChart();
      if (activeChart) {
        activeChart.resetData();
        console.log(`✅ [${settingChangeId}] Chart data reset completed`);
      } else {
        console.log(`🚫 [${settingChangeId}] No active chart found`);
      }
    } catch (error) {
      console.error(
        `❌ [${settingChangeId}] Error resetting chart data:`,
        error,
      );
    }
  }, [chartSetting]);

  const awaitReady = async () => {
    if (!tradingViewChart.current) return;
    tradingViewChart.current.onChartReady(() => {
      setIsChartReady(true);
    });
    if (isMobile) {
      await Promise.race([
        tradingViewChart.current?.headerReady(),
        new Promise((_, reject) => setTimeout(() => reject('timeout'), 800)),
      ]).catch(() => {
        setIsChartHeaderReady(true);
        setIsChartReady(true);
        console.log('Header ready timeout after 800ms, continuing...');
      });
    } else {
      await tradingViewChart.current?.headerReady();
      setIsChartHeaderReady(true);
    }

    try {
      await retry(
        async () => {
          drawCustomChartHeader();
        },
        {
          retries: 50,
          minTimeout: 100,
          maxTimeout: 200,
        },
      );
    } catch (e) {
      console.error('drawCustomChartHeader', e);
    }
  };

  const drawCustomChartHeader = () => {
    if (!tradingViewChart.current) return;
    const tvWidget = tradingViewChart.current;
    createSwitchPriceType(
      tvWidget,
      chartSetting,
      // props.pair,
      (newChartSetting: TChartSetting) => {
        setChartSetting(newChartSetting);
      },
    );

    createSwitchPriceOrMcapButton(
      tvWidget,
      chartSetting,
      (newChartSetting: TChartSetting) => {
        setChartSetting(newChartSetting);
      },
    );
  };

  return (
    <div className="h-full">
      <div
        style={{
          height: `${'calc(100%)'}`,
        }}
        ref={chartContainerRef}
        className={'TVChartContainer'}
      />
    </div>
  );
});
TradingView.displayName = 'TradingView';
export default TradingView;
