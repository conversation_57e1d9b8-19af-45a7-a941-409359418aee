import { CopyIcon } from '@/assets/icons';
import { formatShortAddress } from '@/utils/format';
import { copyToClipboard } from '@/utils/helper';

interface IProps {
  address: string;
  isCopy?: boolean;
  className?: string;
  start?: number;
  end?: number;
}

const AppAddress = ({
  address,
  isCopy = false,
  className,
  start = 4,
  end = 4,
}: IProps) => {
  return (
    <div
      className="flex items-center space-x-2 w-fit cursor-pointer"
      onClick={() => {
        if (isCopy) {
          copyToClipboard(address);
        }
      }}
    >
      <span className={`text-body-sm font-medium ${className}`}>
        {formatShortAddress(address, start, end)}
      </span>
      {isCopy && !!address && (
        <CopyIcon className="h-5 w-5 text-gray-600 group-hover:text-gray-800 transition-colors duration-200" />
      )}
    </div>
  );
};

export default AppAddress;
