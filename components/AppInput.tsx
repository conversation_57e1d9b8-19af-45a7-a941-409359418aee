import clsx from 'clsx';
import {
  DetailedHTMLProps,
  forwardRef,
  InputHTMLAttributes,
  ReactNode,
} from 'react';

interface IProps
  extends DetailedHTMLProps<
    InputHTMLAttributes<HTMLInputElement>,
    HTMLInputElement
  > {
  rootClassName?: string;
  className?: string;
  icon?: ReactNode;
  iconPosition?: 'start' | 'end';
  label?: string;
  required?: boolean;
  error?: string;
}

const AppInput = forwardRef(
  (props: IProps, ref: React.Ref<HTMLInputElement>) => {
    const {
      rootClassName,
      className,
      icon,
      iconPosition = 'start',
      label,
      required,
      error,
      ...restProps
    } = props;
    return (
      <div>
        {label && (
          <label className="block text-xs mb-2 font-semibold text-white-700">
            {label}{' '}
            {required && <span className="text-red-500 text-[10px]">*</span>}
          </label>
        )}
        <div
          className={clsx(
            'flex items-center gap-[8px] p-[8px] rounded-[6px] border border-white-100 bg-white-100',
            rootClassName,
          )}
        >
          {!!icon && iconPosition === 'start' && icon}
          <input
            ref={ref}
            className={clsx(
              'w-full bg-transparent outline-none text-sm leading-[18px] placeholder:text-white-300 placeholder:text-xs',
              className,
            )}
            {...restProps}
          />
          {!!icon && iconPosition === 'end' && icon}
        </div>
        {error && <div className="text-[10px] text-red-500">{error}</div>}
      </div>
    );
  },
);

AppInput.displayName = 'AppInput';

export default AppInput;
