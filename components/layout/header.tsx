'use client';

import { LayerIcon, Logo, MenuIcon, TelegramIcon, XIcon } from '@/assets/icons';
import config from '@/config';
// import { setUserAuth } from '@/store/user.store';
// import { setAuthorizationToRequest } from '@/utils/auth';
// import { usePrivy } from '@privy-io/react-auth';
import clsx from 'clsx';
import dynamic from 'next/dynamic';
import Link from 'next/link';
import { useEffect, useState } from 'react';
// import { useDispatch } from 'react-redux';
const ButtonConnectWallet = dynamic(
  () => import('@/components/buttons/ConnectWallet'),
  { ssr: false },
);

export const Header = () => {
  const [sticky, setSticky] = useState('');
  // const { authenticated, getAccessToken, user } = usePrivy();
  // const dispatch = useDispatch();

  // useEffect(() => {
  //   if (!authenticated || !user?.id) {
  //     return;
  //   }

  //   const setup = async () => {
  //     const accessToken = await getAccessToken();
  //     setAuthorizationToRequest(accessToken);
  //     dispatch(setUserAuth({ accessToken }));
  //   };
  //   setup();
  // }, [user?.id, authenticated]);

  useEffect(() => {
    window.addEventListener('scroll', isSticky);

    return () => {
      window.removeEventListener('scroll', isSticky);
    };
  }, []);

  const isSticky = () => {
    const scrollTop = window.scrollY;
    const stickyClass = scrollTop > 20 ? '!backdrop-blur-md' : '';
    setSticky(stickyClass);
  };

  const MENU_HEADER = [
    {
      title: 'Treasury',
      href: '/treasury',
    },
    {
      title: 'Buy $PRINT',
      href: '/buy',
    },
    {
      title: 'Token Lock',
      href: '/token-lock',
    },
  ];

  return (
    <>
      <div
        className={clsx(
          'z-[99] flex flex-col justify-between bg-black-500 items-center px-[16px] h-[48px] tablet:h-[56px] md:px-[32px] w-full border-b border-white-100 backdrop-blur-md tablet:backdrop-blur-none',
          'fixed top-[44px] left-0 right-0',
          sticky,
        )}
      >
        <div className="w-full h-full flex justify-between items-center">
          <div className="flex items-center gap-[8px]">
            <div className="block tablet:hidden">
              <MenuIcon />
            </div>
            <Link href="/">
              <Logo />
            </Link>
          </div>
          <div className="hidden tablet:flex items-center gap-[56px]">
            {MENU_HEADER.map((item, index) => (
              <Link
                key={index}
                className="cursor-pointer text-white-1000 body-md-regular-14"
                href={item.href}
              >
                {item.title}
              </Link>
            ))}
          </div>

          <div className="flex items-center gap-[8px] tablet:gap-[26px]">
            <Link
              href="/docs"
              target="_blank"
              className="hidden tablet:flex body-md-regular-14 items-center gap-1"
            >
              <LayerIcon />
              Docs
            </Link>
            <a href={config.link.twitter} target="_blank">
              <XIcon />
            </a>
            <a href={config.link.telegram} target="_blank">
              <TelegramIcon />
            </a>
            <ButtonConnectWallet />
          </div>
        </div>
      </div>
    </>
  );
};
