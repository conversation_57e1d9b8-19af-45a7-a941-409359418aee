import Marquee from 'react-fast-marquee';

const TrendingItem = () => {
  return (
    <div className="body-md-regular-14 text-black-700 px-6 border-r border-black-300">
      <span className="text-[#0B0B0B] body-md-semibold-14"> LLOYD</span> Bought{' '}
      <span className="text-[#0B0B0B] body-md-semibold-14">8.090 SOL</span> of{' '}
      <span className="text-[#0B0B0B] body-md-semibold-14">TRUMP</span>
    </div>
  );
};

export const Trending = () => {
  return (
    <div className="z-[99] py-[12px] bg-[#8BD961] fixed top-0 left-0 right-0 flex gap-6">
      <Marquee
        pauseOnHover
        style={{
          height: '100%',
        }}
      >
        <div className="flex">
          <TrendingItem />
          <TrendingItem />
          <TrendingItem />
          <TrendingItem />
          <TrendingItem />
          <TrendingItem />
          <TrendingItem />
          <TrendingItem />
          <TrendingItem />
          <TrendingItem />
        </div>
      </Marquee>
    </div>
  );
};
