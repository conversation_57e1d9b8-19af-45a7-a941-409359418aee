'use client';
import clsx from 'clsx';
import { usePathname } from 'next/navigation';
import { useMediaQuery } from '@/node_modules/react-responsive';

export const Footer = () => {
  const pathName = usePathname();
  const isMobile = useMediaQuery({ query: '(max-width: 1025px)' });

  if (
    pathName.includes('/coins/') &&
    !pathName.includes('/coins/create') &&
    isMobile
  )
    return <></>;

  return (
    <div
      className={clsx(
        'z-[99] flex flex-col tablet:flex-row tablet:items-center gap-2 justify-between bg-black-900 px-[16px] py-[12px] tablet:py-[24px] tablet:px-[32px] w-full border-t border-white-100',
        'fixed bottom-0 left-0 right-0',
      )}
    >
      <div className="body-sm-regular-12 md:body-md-regular-14">
        © 2025 Print Meme. All rights reserved.
      </div>

      <div className="flex gap-3 body-sm-regular-12 md:body-md-regular-14">
        <a
          href="/privacy-policy"
          target="_blank"
          className="border-r border-white-1000 pr-3"
        >
          Privacy policy
        </a>
        <a href="/terms-of-use" target="_blank">
          Terms of use
        </a>
      </div>
    </div>
  );
};
