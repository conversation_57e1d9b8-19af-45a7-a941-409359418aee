import { HeartOutlinedIcon } from '@/assets/icons';
import AppAddress from '@/components/AppAddress';
import AppFallbackImage from '@/components/AppFallbackImage';
import { IReply } from '@/types/comment';
import { formatCommentTime } from '@/utils/format';
import { getRandomAvatarUrl } from '@/utils/helper';
import React, { useMemo, useState } from 'react';

interface ReplyProps {
  reply: IReply;
  likes: string[];
}

const ReplyComment: React.FC<ReplyProps> = ({ reply, likes }) => {
  const [likedComments, setLikedComments] = useState<string[]>(likes);
  const [likesCount, setLikesCount] = useState<number>(reply.likesCount);

  const handleLike = async (commentId: string, hasLikedFromApi: boolean) => {
    const isCurrentlyLiked = likedComments.includes(commentId);

    setLikedComments((prev) => {
      if (!isCurrentlyLiked) {
        return [...prev, commentId];
      } else {
        return prev.filter((id) => id !== commentId);
      }
    });

    setLikesCount((prev) => {
      if (hasLikedFromApi && likesCount > 0) {
        return prev - 1;
      }
      if (hasLikedFromApi && likesCount === 0) {
        return prev + 1;
      }
      if (!hasLikedFromApi && likesCount === 0) {
        return prev + 1;
      }
      if (!hasLikedFromApi && likesCount > 0) {
        if (likedComments.includes(reply.id)) return prev - 1;
        return prev + 1;
      } else {
        return prev - 1;
      }
    });
  };
  const isLiked = likedComments.includes(reply?.id);

  const avatarUrl = useMemo(() => {
    return getRandomAvatarUrl();
  }, []);

  return (
    <div className="flex items-start space-x-8px ml-0 mt-8px pt-8px border-t border-white-50">
      <div className="w-7 h-7 rounded-full overflow-hidden shrink-0">
        <AppFallbackImage
          src={avatarUrl}
          alt={` avatar`}
          width={24}
          height={24}
          className="object-cover w-24px h-24px rounded-full"
          fallbackSrc={avatarUrl}
        />
      </div>
      <div className="flex-1 min-w-0">
        <div className="flex items-center space-x-8px">
          <AppAddress address={reply?.user?.walletAddress} />
          <span className="text-body-sm font-normal text-white-500">
            {formatCommentTime(reply?.createdAt)}
          </span>
        </div>
        <p className="mt-1 text-white-700 text-sm break-words overflow-hidden">
          {reply?.content}
        </p>
        <div className="flex items-center space-x-16px mt-8px">
          <button
            onClick={() => handleLike(reply?.id, isLiked)}
            className="flex items-center space-x-4px no-underline font-normal text-sm"
          >
            <HeartOutlinedIcon
              className={`w-24px h-24px ${
                isLiked && likesCount > 0 ? 'text-red-500' : 'text-white-700'
              }`}
            />
            <span className="inline-block">{likesCount}</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default ReplyComment;
