'use client';
import {
  CommentOutlinedIcon,
  HeartOutlinedIcon,
  SendIcon,
} from '@/assets/icons';
import AppAddress from '@/components/AppAddress';
import { AppButton } from '@/components/AppButton';
import AppFallbackImage from '@/components/AppFallbackImage';
import AppInput from '@/components/AppInput';
import { useIntersectionObserver } from '@/hooks';
import { useCoinPageContext } from '@/app/coins/[slug]/provider';
import rf from '@/services/RequestFactory';
import { IComment, ICommentForm, IReplyForm } from '@/types/comment';
import { formatCommentTime } from '@/utils/format';
import { getRandomAvatarUrl } from '@/utils/helper';
import { usePrivy } from '@privy-io/react-auth';
import clsx from 'clsx';
import Image from 'next/image';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { Controller, SubmitHandler, useForm } from 'react-hook-form';
import ReplyComment from './ReplyComment';

const CommentBlock = () => {
  const { authenticated } = usePrivy();
  const isLoggedIn = !!authenticated;
  const { coinAddress } = useCoinPageContext();

  const [comments, setComments] = useState<IComment[]>([]);
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [numberPage, setNumberPage] = useState(1);
  const [totalComments, setTotalComments] = useState<number>(0);
  const [likes, setLikes] = useState<string[]>([]);
  const [replyPages, setReplyPages] = useState<Record<string, number>>({});
  const [loadingMoreReplies, setLoadingMoreReplies] = useState<
    Record<string, boolean>
  >({});
  const [hasNextPage, setHasNextPage] = useState(true);

  const {
    control: controlComment,
    handleSubmit: handleSubmitComment,
    watch: watchComment,
    reset: resetComment,
  } = useForm<ICommentForm>({
    defaultValues: {
      comment: '',
    },
  });
  const {
    control: controlReply,
    handleSubmit: handleSubmitReply,
    watch: watchReply,
    reset: resetReply,
  } = useForm<IReplyForm>({
    defaultValues: {
      reply: '',
    },
  });

  const onSubmitComment: SubmitHandler<ICommentForm> = async (data) => {
    try {
      const resNewComment = await rf
        .getRequest('CommentRequest')
        .postComment(coinAddress, {
          content: data.comment,
        });

      const newComment = resNewComment?.data || resNewComment;

      setComments((prevComments) =>
        Array.isArray(prevComments)
          ? [newComment, ...prevComments]
          : [newComment],
      );
      resetComment({ comment: '' });
    } catch (error) {
      console.error('Error sending comment:', error);
    }
  };

  const onSubmitReply = async (data: IReplyForm) => {
    try {
      if (!replyingTo) return;

      const newReply = await rf
        .getRequest('CommentRequest')
        .postReply(replyingTo, {
          content: data.reply,
        });

      setComments((prevComments) =>
        prevComments.map((comment) =>
          comment.id === replyingTo
            ? {
                ...comment,
                replies: comment?.replies
                  ? [...comment?.replies, newReply]
                  : [newReply],
                totalReplies: comment.totalReplies + 1,
              }
            : comment,
        ),
      );
      resetReply({ reply: '' });
      setReplyingTo(null);
    } catch (error) {
      console.error('Error sending reply:', error);
    }
  };

  useEffect(() => {
    const fetchComments = async (page: number) => {
      if (!coinAddress) return;
      try {
        const data = await rf.getRequest('CommentRequest').getComment({
          coinAddress,
          page,
          limit: 10,
        });

        if (page === 1) {
          setComments(data?.docs || []);
        } else {
          setComments((prevComments) => [
            ...prevComments,
            ...(data?.docs || []),
          ]);
        }

        setTotalComments(data?.totalDocs ?? 0);
        if (numberPage >= data?.totalPages) {
          setHasNextPage(false);
        }
      } catch (error) {
        console.error('Error fetching comments:', error);
      }
    };

    fetchComments(numberPage);
  }, [numberPage, coinAddress]);

  useEffect(() => {
    const fetchLikes = async () => {
      if (!isLoggedIn || !coinAddress) return;
      try {
        const dataLike = await rf.getRequest('CommentRequest').getLikes({
          coinAddress,
        });
        setLikes(dataLike?.likes || []);
      } catch (error) {
        console.error('Error fetching likes:', error);
      }
    };

    fetchLikes();
  }, []);

  const handleLoadMoreComments = useCallback(() => {
    if (hasNextPage) {
      setNumberPage((prev) => prev + 1);
    }
  }, [hasNextPage]);

  const handleLike = async (commentId: string, hasLikedFromApi: boolean) => {
    try {
      setComments((prevComments) =>
        prevComments.map((comment) => {
          if (comment.id === commentId) {
            return {
              ...comment,
              likesCount: comment.likesCount + (hasLikedFromApi ? -1 : 1),
            };
          }
          if (comment.replies) {
            return {
              ...comment,
              replies: comment.replies.map((reply) =>
                reply.id === commentId
                  ? {
                      ...reply,
                      likesCount: reply.likesCount + (hasLikedFromApi ? -1 : 1),
                    }
                  : reply,
              ),
            };
          }
          return comment;
        }),
      );

      // Use the service methods instead of direct fetch
      if (hasLikedFromApi) {
        await rf.getRequest('CommentRequest').unlikeComment(commentId);
      } else {
        await rf.getRequest('CommentRequest').likeComment(commentId);
      }
      setLikes((prevLikes) =>
        prevLikes.includes(commentId)
          ? prevLikes.filter((id) => id !== commentId)
          : [...prevLikes, commentId],
      );
    } catch (error) {
      console.error('Error updating like status:', error);
      // You might want to refresh the comments here to revert the optimistic update
    }
  };

  const handleLoadMoreReplies = async (commentId: string) => {
    try {
      setLoadingMoreReplies((prev) => ({ ...prev, [commentId]: true }));
      const currentPage = replyPages[commentId] || 1;
      const nextPage = currentPage + 1;

      const moreReplies = await rf.getRequest('CommentRequest').getReplies({
        commentId,
        page: nextPage,
        limit: 3,
      });

      setComments((prevComments) =>
        prevComments.map((comment) =>
          comment.id === commentId
            ? {
                ...comment,
                replies: [
                  ...(moreReplies.docs || []),
                  ...(comment.replies || []),
                ],
                totalReplies: moreReplies.totalDocs,
              }
            : comment,
        ),
      );

      setReplyPages((prev) => ({ ...prev, [commentId]: nextPage }));
    } catch (error) {
      console.error('Error loading more replies:', error);
    } finally {
      setLoadingMoreReplies((prev) => ({ ...prev, [commentId]: false }));
    }
  };

  const { setEl } = useIntersectionObserver({
    loadMore: handleLoadMoreComments,
  });

  const avatarUrl = useMemo(() => {
    return getRandomAvatarUrl();
  }, []);

  return (
    <div>
      <form
        onSubmit={handleSubmitComment(onSubmitComment)}
        className="flex gap-8px p-16px border-b border-white-50"
      >
        <Image
          src={avatarUrl}
          alt={'avatar'}
          width={24}
          height={24}
          className="object-cover w-24px h-24px rounded-full"
        />
        <div className="flex-1">
          <Controller
            name="comment"
            control={controlComment}
            rules={{
              required: true,
              maxLength: { value: 500, message: 'Max 500 characters' },
            }}
            render={({ field }) => (
              <AppInput
                {...field}
                placeholder="Enter your comment"
                className="placeholder-white-300 text-body-sm"
                rootClassName="p-8px h-[34px]"
                maxLength={500}
              />
            )}
          />
          <div className="text-body-xs text-white-700 mt-4px">
            {watchComment('comment')?.length || 0}/500
          </div>
        </div>
        <AppButton
          className="flex justify-center gap-4px w-[80px] h-32px !rounded-[4px]"
          type="submit"
          disabled={!isLoggedIn || watchComment('comment')?.length < 1}
          buttonType="contained-brand"
          isButtonAuth
          classNameBtnAuth="w-[90px] h-32px"
        >
          Sent <SendIcon />
        </AppButton>
      </form>

      <div className="p-16px flex flex-col gap-8px">
        <h4 className="text-action-sm font-semibold">
          Comments ({totalComments})
        </h4>
        {comments?.map((comment, index, array) => {
          const isLiked = likes.includes(comment.id);
          return (
            <div
              key={comment.id}
              className={clsx(
                'flex items-start space-x-8px p-8px border-white-50',
                index === array.length - 1 ? 'border-b-0' : 'border-b',
              )}
              ref={(el) => {
                if (index !== array.length - 1) return;
                setEl(el);
              }}
            >
              <div className="w-7 h-7 rounded-full overflow-hidden shrink-0">
                <AppFallbackImage
                  src={getRandomAvatarUrl()}
                  alt={` avatar`}
                  width={24}
                  height={24}
                  className="object-cover w-24px h-24px rounded-full"
                  fallbackSrc={getRandomAvatarUrl()}
                />
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-8px">
                  <AppAddress address={comment?.user?.walletAddress} />
                  <span className="text-body-sm font-normal text-white-500">
                    {formatCommentTime(comment?.createdAt)}
                  </span>
                </div>
                <p className="mt-4px text-sm break-words overflow-hidden">
                  {comment?.content}
                </p>
                <div className="flex items-center space-x-16px mt-8px">
                  <button
                    className={`flex items-center space-x-4px no-underline font-normal text-sm ${
                      isLiked ? 'text-red-500' : 'text-white-700'
                    }`}
                    onClick={() => handleLike(comment?.id, isLiked)}
                  >
                    <HeartOutlinedIcon
                      className={`w-24px h-24px ${
                        isLiked ? 'text-red-500' : 'text-white-700'
                      }`}
                    />
                    <span className="inline-block">{comment?.likesCount}</span>
                  </button>
                  <button
                    className="flex items-center space-x-4px hover:opacity-80 no-underline text-white-700 font-normal text-sm"
                    onClick={() =>
                      setReplyingTo(
                        replyingTo === comment?.id ? null : comment?.id,
                      )
                    }
                  >
                    <CommentOutlinedIcon className="w-24px h-24px" />
                    <span>{comment?.replies?.length}</span>
                  </button>
                </div>
                {replyingTo === comment?.id && (
                  <>
                    <form
                      onSubmit={handleSubmitReply(onSubmitReply)}
                      className="flex gap-8px pt-8px mt-8px border-t border-white-50"
                    >
                      <AppFallbackImage
                        src={getRandomAvatarUrl()}
                        alt={'avatar'}
                        width={24}
                        height={24}
                        className="object-cover w-24px h-24px rounded-full"
                        fallbackSrc={getRandomAvatarUrl()}
                      />
                      <div className="flex-1">
                        <Controller
                          name="reply"
                          control={controlReply}
                          rules={{
                            required: true,
                            maxLength: {
                              value: 500,
                              message: 'Max 500 characters',
                            },
                          }}
                          render={({ field }) => (
                            <AppInput
                              {...field}
                              placeholder="Write a reply..."
                              className="placeholder-white-300 text-body-sm"
                              rootClassName="p-8px"
                              maxLength={500}
                            />
                          )}
                        />
                        <div className="text-body-xs text-white-700 mt-4px">
                          {watchReply('reply')?.length || 0}/500
                        </div>
                      </div>

                      <AppButton
                        className="flex justify-center gap-4px w-[80px] !rounded-[4px] h-32px"
                        type="submit"
                        buttonType="contained-brand"
                        disabled={
                          !isLoggedIn || watchReply('reply')?.length < 1
                        }
                      >
                        Reply
                      </AppButton>
                    </form>
                  </>
                )}
                {comment.replies?.length > 0 &&
                  comment.replies.length < comment.totalReplies && (
                    <button
                      onClick={() => handleLoadMoreReplies(comment.id)}
                      disabled={loadingMoreReplies[comment.id]}
                      className="mt-8px ml-32px text-sm text-white-700 hover:text-white-900"
                    >
                      {loadingMoreReplies[comment.id]
                        ? 'Loading...'
                        : 'Show older replies'}
                    </button>
                  )}
                {comment?.replies?.map((reply) => (
                  <ReplyComment key={reply.id} reply={reply} likes={likes} />
                ))}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default CommentBlock;
