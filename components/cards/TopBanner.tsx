import KingOfTheHillImg from '@/assets/images/king-of-the-hill.webp';
import Image from 'next/image';

// interface IHotDao {
//   id: string;
//   imageUrl: string;
//   marketCap: string;
//   macketCapIncrease: string;
//   marketCapPercent: string;
//   name: string;
//   status: string;
//   pairId: string;
// }

export default function CardTopBanner() {
  // const [hotDao, setHotDao] = useState<IHotDao | null>(null);
  // const getDataHotDao = async () => {
  //   const result = await rf.getRequest('DaoRequest').getHotDao();
  //   setHotDao(result);
  // };
  // useEffect(() => {
  //   getDataHotDao();
  // }, []);

  // const formatValuePercent = (value: string | undefined): JSX.Element => {
  //   if (!value || value === 'NaN') {
  //     return <div>--</div>;
  //   }

  //   const numberValue = parseFloat(value);
  //   const colorClass = numberValue < 0 ? 'text-red-500' : 'text-green-500';

  //   return (
  //     <span className={colorClass}>
  //       {new BigNumber(numberValue).decimalPlaces(2).toString()}%
  //     </span>
  //   );
  // };
  return (
    <div className="flex items-center flex-col mt-6">
      <Image
        src={KingOfTheHillImg}
        width={166}
        height={32}
        alt=""
        className="mb-2"
      />
      {/* {hotDao?.pairId && (
        <Link href={`/coins/${hotDao?.id}`}>
          <div className="flex bg-gradient-to-b rounded-[4px] from-[#000000] to-[#B7DB93] bg-[length:200%_180%] overflow-hidden  max-tablet:h-[200px] max-tablet:hidden">
            <div className="overflow-hidden">
              <Image
                src={hotDao.imageUrl}
                alt="User Avatar"
                width={180}
                height={180}
                className="w-[180px] z-[10000] h-[180px] object-contain rounded max-tablet:w-[160px] max-tablet:h-[160px]"
              />
            </div>
            <div className="flex-2 p-4 text-white max-tablet:p-3 min-w-[180px]">
              <h2 className="text-[18px] font-semibold mb-4 max-tablet:mb-2">
                {hotDao.name}
              </h2>
              <p className="text-[12px] font-medium text-white-700">
                Market Cap
              </p>
              <h3 className="text-2xl my-2 flex gap-2px">
                <AppNumber value={Number(hotDao.marketCap) || 0} isForUSD />
              </h3>
              <div
                className={`text-action-sm font-medium ${
                  Number(hotDao?.marketCap || 0) > 0
                    ? 'text-green-500'
                    : 'text-red-500'
                }`}
              >
                {formatToPercent(
                  +(hotDao?.marketCap || 0) / INIT_MARKETCAP_PRESALE,
                )}
              </div>
              <button className=" bg-green-900 mt-3 gap-1 text-green-500 flex justify-center rounded w-43 h-18 text-white px-1 py-1">
                <IconDot />
                <p className="text-[10px]">{hotDao.status}</p>
              </button>
            </div>
          </div>
        </Link>
      )} */}
    </div>
  );
}
