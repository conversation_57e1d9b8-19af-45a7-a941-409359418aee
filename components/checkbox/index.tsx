import { Checkbox } from '@/assets/icons';
import clsx from 'clsx';

interface Props {
  value: boolean;
  label: string;
  disable?: boolean;
  onChange: (newValue: boolean) => void;
  className?: string;
}

const CheckBox = ({ value, label, className, disable, onChange }: Props) => {
  return (
    <div
      className={`flex items-center  ${
        disable ? 'cursor-not-allowed' : 'cursor-pointer'
      }`}
      onClick={() => {
        if (!disable) onChange(!value);
      }}
    >
      <div
        className={`w-[20px] h-[20px] ${
          value ? 'bg-[#11B6FF]' : 'bg-white-150'
        } rounded-[2px] relative`}
      >
        {value && <Checkbox className="absolute top-[4px] left-[4px]" />}
      </div>
      <span
        className={clsx(
          'text-white text-[12px] md:text-[14px] ml-[8px]',
          className,
        )}
      >
        {label}
      </span>
    </div>
  );
};

export default CheckBox;
