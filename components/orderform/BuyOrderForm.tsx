'use client';
import { useCoinPageContext } from '@/app/coins/[slug]/provider';
import { CoinTip, FlashIcon, SettingIcon, SlippageIcon } from '@/assets/icons';
import { AppButton } from '@/components/AppButton';
import config from '@/config';
import { CONTRACT_ADDRESSES, ETH_DECIMALS, LAUNCHPAD_ABI } from '@/constants';
import { useSigner } from '@/hooks/useSigner';
import { AppBroadcast, BROADCAST_EVENTS } from '@/libs/broadcast';
import { errorMsg, successMsg } from '@/libs/toast';
import { ModalSettingSlippage } from '@/modals';
import { formatNumber } from '@/utils/format';
import { adjustForAmountSlippage, etherProvider } from '@/utils/helper';
import { getSimulatedSwapQuote, swapETHForTokens } from '@/utils/swap';
import BigNumber from 'bignumber.js';
import { Contract, formatEther, parseEther } from 'ethers';
import { FC, useEffect, useState } from 'react';
import { NumericFormat } from 'react-number-format';

const BUY_AMOUNT = [0.01, 0.02, 0.5, 1];

type TBuyOrderForm = {
  ethBalance: number | string | null;
  fetchBalances: () => void;
  fetchTokenBalance: () => void;
};
const BuyOrderForm: FC<TBuyOrderForm> = ({
  ethBalance,
  fetchBalances,
  fetchTokenBalance,
}) => {
  const { coinAddress, isCurveComplete } = useCoinPageContext();

  const [amountIn, setAmountIn] = useState<string>('');
  const [isOpenModalSettingSlippage, setIsOpenModalSettingSlippage] =
    useState<boolean>(false);
  const [slippage, setSlippage] = useState<number>(5.0);
  const [isLoading, setIsLoading] = useState(false);
  const [estimatedAmountOut, setEstimateAmountOut] = useState('');
  const { signer } = useSigner();

  const contract = new Contract(
    CONTRACT_ADDRESSES.LAUNCHPAD,
    LAUNCHPAD_ABI,
    etherProvider,
  );

  useEffect(() => {
    setEstimateAmountOut('');

    if (!amountIn || !signer) {
      return;
    }

    const getEstimateAmountOut = async () => {
      try {
        if (isCurveComplete) {
          const estimatedTokens = await getSimulatedSwapQuote(
            signer,
            config.contracts.wethAddress || '',
            coinAddress,
            parseEther(amountIn).toString(),
          );

          setEstimateAmountOut(formatEther(estimatedTokens || 0));
        } else {
          const estimatedTokens = await contract.estimateBuyExactInTokens(
            coinAddress,
            parseEther(amountIn),
          );
          setEstimateAmountOut(formatEther(estimatedTokens || 0));
        }
      } catch (error: any) {
        console.error('getEstimateAmountOut error:', error);
      }
    };
    getEstimateAmountOut();
  }, [amountIn, coinAddress, isCurveComplete]);

  const onBuyCoinSuccess = (msg: string, txid?: string) => {
    setAmountIn('');
    fetchBalances();
    successMsg(msg, txid);
    AppBroadcast.dispatch(BROADCAST_EVENTS.REWARD_UPDATED, {});
    AppBroadcast.dispatch(BROADCAST_EVENTS.TRADE_SUCCESS, {});
  };

  const handleSwapBuy = async (amountInWei: string) => {
    if (!signer) {
      return;
    }

    const estimatedTokens = await getSimulatedSwapQuote(
      signer,
      config.contracts.wethAddress || '',
      coinAddress,
      amountInWei,
    );
    console.log('Using accurate quote:', estimatedTokens);
    const amountOutMin = adjustForAmountSlippage(estimatedTokens, slippage);
    console.log('Buy swap details:', {
      amountInWei,
      estimatedTokens,
      slippage,
      amountOutMin,
    });

    const { receipt: txResponse } = await swapETHForTokens(
      signer,
      coinAddress,
      amountInWei,
      amountOutMin,
      signer.address,
    );

    onBuyCoinSuccess('Swap successfully!', txResponse.hash);
  };

  const handleBondingCurveBuy = async (amountInWei: string) => {
    const contract = new Contract(
      CONTRACT_ADDRESSES.LAUNCHPAD,
      LAUNCHPAD_ABI,
      signer,
    );

    const estimatedTokens = await contract.estimateBuyExactInTokens(
      coinAddress,
      amountInWei,
    );
    const amountOutMin = adjustForAmountSlippage(estimatedTokens, slippage);
    const amountInWithFee = BigNumber(amountInWei)
      .plus(BigNumber(amountInWei).multipliedBy(config.platformFeeRate))
      .toFixed(0);

    const txResponse = await contract.buyExactIn(
      coinAddress,
      amountInWei,
      amountOutMin,
      {
        value: amountInWithFee,
      },
    );

    await txResponse.wait();
    onBuyCoinSuccess('Buy successfully!', txResponse.hash);
  };

  const handleBuyCoin = async () => {
    if (Number(amountIn) <= 0) {
      errorMsg('Amount must be greater than 0');
      return;
    }

    if (
      new BigNumber(amountIn).comparedTo(new BigNumber(ethBalance || 0)) > 0
    ) {
      errorMsg('Insufficient balance');
      return;
    }

    setIsLoading(true);
    try {
      if (!signer?.address) {
        throw new Error('Provider or wallet not available');
      }

      const amountInWei = parseEther(amountIn).toString();

      if (isCurveComplete) {
        await handleSwapBuy(amountInWei);
      } else {
        await handleBondingCurveBuy(amountInWei);
      }
    } catch (error: any) {
      errorMsg(error?.info?.error?.message || 'Transaction failed!');
      console.log('handleBuyCoin error', error);
    } finally {
      setIsLoading(false);
      setAmountIn('');
      setEstimateAmountOut('');
      setTimeout(() => {
        fetchTokenBalance();
        fetchBalances();
      }, 1000);
    }
  };

  return (
    <div className="flex-1">
      <div className="desktop:border border-white-50 rounded-[4px]">
        <div className="flex justify-between px-2 desktop:bg-transparent bg-black-900 desktop:py-4 py-2 rounded-[4px] gap-2 items-center">
          <div className="body-sm-light-12 text-white-800">Amount</div>
          <div className="flex gap-2 flex-1 items-center">
            <NumericFormat
              value={amountIn}
              onValueChange={(values) => setAmountIn(values.value)}
              thousandSeparator=","
              decimalSeparator="."
              allowNegative={false}
              placeholder="0.00"
              className="font-bold text-right bg-transparent w-full appearance-none focus:outline-none body-md-regular-14"
              allowLeadingZeros={false}
              decimalScale={ETH_DECIMALS}
              inputMode="numeric"
            />
            <span className="text-white-500 text-sm">HYPE</span>
          </div>
        </div>
        <div className="desktop:border-t border-white-50 grid grid-cols-4 mt-2 desktop:mt-0 desktop:gap-0 gap-1">
          {BUY_AMOUNT.map((item, index) => (
            <div
              key={item}
              className={`border desktop:border-0 ${
                index === BUY_AMOUNT.length - 1 ? '' : 'desktop:border-r '
              } desktop:rounded-none rounded-[4px] border-white-50 p-1 text-white-800 body-md-light-14 text-center cursor-pointer hover:bg-white-100`}
              onClick={() => setAmountIn(item.toString())}
            >
              {item}
            </div>
          ))}
        </div>
      </div>

      {BigNumber(estimatedAmountOut || 0).isGreaterThan(0) && (
        <div className="text-right text-white-500 text-sm my-2">
          {formatNumber(estimatedAmountOut)} <span>token</span>
        </div>
      )}

      <div className="flex w-full cursor-pointer items-center justify-between my-3 gap-2">
        <div className="flex gap-2">
          <div className="flex items-center gap-2 text-white-500">
            <CoinTip /> <p className="body-sm-regular-12">0.01</p>
          </div>
          <div className="flex items-center gap-2 text-white-500">
            <SlippageIcon /> <p className="body-sm-regular-12">{slippage}%</p>
          </div>
        </div>

        <AppButton
          className="w-full flex desktop:hidden justify-center h-[40px] !rounded-[4px] !gap-1"
          type="submit"
          buttonType="contained-brand"
          onClick={handleBuyCoin}
          isLoading={isLoading}
        >
          <FlashIcon /> Buy
        </AppButton>

        <div
          className="flex items-center gap-2 text-white-500 desktop:p-0 desktop:border-0 p-2 border border-white-50 rounded-[4px]"
          onClick={() => setIsOpenModalSettingSlippage(true)}
        >
          <SettingIcon />{' '}
          <p className="body-sm-regular-12 desktop:block hidden">Settings</p>
        </div>
      </div>

      <AppButton
        className="w-full hidden desktop:flex justify-center h-[48px] !rounded-[4px] !gap-1"
        type="submit"
        buttonType="contained-brand"
        onClick={handleBuyCoin}
        isLoading={isLoading}
        disabled={!amountIn || !ethBalance}
        classNameBtnAuth="w-full"
        isButtonAuth
      >
        <FlashIcon /> Buy
      </AppButton>

      {isOpenModalSettingSlippage && (
        <ModalSettingSlippage
          isOpen={isOpenModalSettingSlippage}
          onClose={() => setIsOpenModalSettingSlippage(false)}
          slippage={slippage}
          setSlippage={setSlippage}
        />
      )}
    </div>
  );
};

export default BuyOrderForm;
