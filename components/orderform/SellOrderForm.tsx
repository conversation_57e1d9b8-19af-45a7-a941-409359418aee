import { useCoinPageContext } from '@/app/coins/[slug]/provider';
import { CoinTip, FlashIcon, SettingIcon, SlippageIcon } from '@/assets/icons';
import { AppButton } from '@/components/AppButton';
import { CONTRACT_ADDRESSES, ETH_DECIMALS, LAUNCHPAD_ABI } from '@/constants';
import { useSigner } from '@/hooks/useSigner';
import { useTokenApproval, ApprovalType } from '@/hooks/useTokenApproval';
import { errorMsg, successMsg } from '@/libs/toast';
import { ModalSettingSlippage } from '@/modals';
import { adjustForAmountSlippage, etherProvider } from '@/utils/helper';
import { swapTokensForETH, getSimulatedSwapQuote } from '@/utils/swap';
import BigNumber from 'bignumber.js';
import { Contract, formatEther, parseEther } from 'ethers';
import Image from 'next/image';
import { FC, useEffect, useMemo, useState } from 'react';
import { NumericFormat } from 'react-number-format';

const PERCENT_SELL = [10, 20, 50, 100];

type TSellOrderForm = {
  tokenBalance: number | string | null;
  fetchTokenBalance: () => void;
  fetchBalances: () => void;
};

const SellOrderForm: FC<TSellOrderForm> = ({
  tokenBalance,
  fetchBalances,
  fetchTokenBalance,
}) => {
  const [isOpenModalSettingSlippage, setIsOpenModalSettingSlippage] =
    useState<boolean>(false);
  const [sellAmount, setSellAmount] = useState<string>('');
  const [slippage, setSlippage] = useState<number>(5.0);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [estimatedAmountOut, setEstimateAmountOut] = useState('');
  const [needsApproval, setNeedsApproval] = useState<boolean>(false);

  const { coin, coinAddress, isCurveComplete } = useCoinPageContext();
  const { activeWallet, signer } = useSigner();

  // Swap token approval hook
  const { isApproving, approveMax, hasInsufficientAllowance } =
    useTokenApproval(ApprovalType.SWAP);

  const readContract = new Contract(
    CONTRACT_ADDRESSES.LAUNCHPAD,
    LAUNCHPAD_ABI,
    etherProvider,
  );

  const setPercentageOfBalance = (balance: string, percentage: number) => {
    if (!balance) return;

    if (percentage === 100) {
      setSellAmount(balance);
      return;
    }

    const percentageOfBalance = BigNumber(balance)
      .multipliedBy(percentage)
      .div(100)
      .toFixed();

    setSellAmount(percentageOfBalance);
  };

  const handleApprove = async () => {
    if (!coinAddress || !sellAmount) {
      errorMsg('Please enter a sell amount');
      return;
    }

    try {
      const txHash = await approveMax(coinAddress);
      if (txHash) {
        // Approval successful, check again to update needsApproval state
        const needsApprovalCheck = await hasInsufficientAllowance(
          coinAddress,
          sellAmount,
        );

        setNeedsApproval(needsApprovalCheck);
      }
    } catch (error: any) {
      errorMsg(error?.info?.error?.message || 'Transaction failed!');
      console.log('handleApprove error', error);
    }
  };

  const handleSwapSell = async (tokenAmountInWei: string) => {
    if (!signer) {
      return;
    }

    // Check if approval is needed before swap
    const needsApprovalCheck = await hasInsufficientAllowance(
      coinAddress,
      sellAmount,
    );
    if (needsApprovalCheck) {
      errorMsg('Token approval required. Please approve tokens first.');
      return;
    }

    // Get estimated output amount
    const estimatedWHYPE = await getSimulatedSwapQuote(
      signer,
      coinAddress,
      CONTRACT_ADDRESSES.WETH,
      tokenAmountInWei,
    );

    // Apply slippage to get minimum output
    const amountOutMin = adjustForAmountSlippage(estimatedWHYPE, slippage);

    // Swap tokens for ETH using multicall (single transaction)
    const { receipt: txResponse } = await swapTokensForETH(
      signer,
      coinAddress,
      tokenAmountInWei,
      amountOutMin,
      await signer.getAddress(),
    );

    successMsg('Swap successfully!', txResponse.hash);
  };

  const handleBondingCurveSell = async (tokenAmountInWei: string) => {
    const writeContract = new Contract(
      CONTRACT_ADDRESSES.LAUNCHPAD,
      LAUNCHPAD_ABI,
      signer,
    );

    const estimatedHype = await writeContract.estimateSellTokens(
      coinAddress,
      tokenAmountInWei,
    );

    // Apply slippage tolerance
    const amountOutMin = adjustForAmountSlippage(estimatedHype, slippage);
    console.log({ coinAddress, tokenAmountInWei, amountOutMin });

    const txResponse = await writeContract.sellExactIn(
      coinAddress,
      tokenAmountInWei,
      amountOutMin,
    );
    await txResponse.wait();

    successMsg('Sell successfully!', txResponse.hash);
    console.log('Transaction confirmed! Hash:', txResponse.hash);
  };

  const handleSellCoin = async () => {
    if (Number(sellAmount) <= 0) {
      errorMsg('Amount must be greater than 0');
      return;
    }

    if (new BigNumber(sellAmount).comparedTo(tokenBalance || 0) > 0) {
      errorMsg('Insufficient balance');
      return;
    }

    setIsLoading(true);
    try {
      if (!activeWallet || !signer) {
        throw new Error('Provider or wallet not available');
      }

      const tokenAmountInWei = parseEther(sellAmount).toString();

      if (isCurveComplete) {
        await handleSwapSell(tokenAmountInWei);
      } else {
        await handleBondingCurveSell(parseEther(sellAmount).toString());
      }
    } catch (error: any) {
      errorMsg(error?.info?.error?.message || 'Transaction failed!');
      console.log('handleSellCoin error', error);
    } finally {
      setIsLoading(false);
      setSellAmount('');
      setEstimateAmountOut('');
      setTimeout(() => {
        fetchTokenBalance();
        fetchBalances();
      }, 1000);
    }
  };

  // Check approval status when sellAmount changes
  useEffect(() => {
    const checkApprovalStatus = async () => {
      if (!sellAmount || !coinAddress || !isCurveComplete) {
        setNeedsApproval(false);
        return;
      }

      try {
        const needsApprovalCheck = await hasInsufficientAllowance(
          coinAddress,
          sellAmount,
        );
        console.log(needsApprovalCheck, 'needsApprovalCheck');
        setNeedsApproval(needsApprovalCheck);
      } catch (error) {
        console.error('Error checking approval status:', error);
        setNeedsApproval(false);
      }
    };

    checkApprovalStatus();
  }, [sellAmount, coinAddress, isCurveComplete, hasInsufficientAllowance]);

  useEffect(() => {
    setEstimateAmountOut('');

    if (!sellAmount || !signer) {
      return;
    }

    const getEstimateAmountOut = async () => {
      try {
        if (isCurveComplete) {
          const estimatedWHYPE = await getSimulatedSwapQuote(
            signer,
            coinAddress,
            CONTRACT_ADDRESSES.WETH,
            parseEther(sellAmount).toString(),
          );

          setEstimateAmountOut(formatEther(estimatedWHYPE || 0));
        } else {
          const estimatedEth = await readContract.estimateSellTokens(
            coinAddress,
            parseEther(sellAmount),
          );
          console.log(estimatedEth, 'estimatedEth');
          setEstimateAmountOut(formatEther(estimatedEth || 0));
        }
      } catch (error: any) {
        console.error('Error getting sell price:', error);
      }
    };
    getEstimateAmountOut();
  }, [sellAmount, coinAddress, isCurveComplete]);

  const isDisableSellButton = useMemo(() => {
    if (BigNumber(sellAmount || 0).isLessThanOrEqualTo(0)) {
      return true;
    }

    if (BigNumber(sellAmount || 0).isGreaterThan(tokenBalance || 0)) {
      return true;
    }

    if (isLoading) {
      return true;
    }

    return false;
  }, [tokenBalance, sellAmount, isLoading]);

  return (
    <div className="flex-1">
      <div className="desktop:border border-white-50 rounded-[4px]">
        <div className="flex justify-between px-2 desktop:bg-transparent bg-black-900 desktop:py-4 py-2 rounded-[4px] gap-2 items-center">
          <div className="body-sm-light-12 text-white-800">Amount</div>
          <div className="flex gap-2 flex-1 items-center">
            <NumericFormat
              value={sellAmount}
              onValueChange={(values) => setSellAmount(values.value)}
              thousandSeparator=","
              decimalSeparator="."
              allowNegative={false}
              placeholder="0.00"
              className="font-bold text-right bg-transparent w-full appearance-none focus:outline-none body-md-regular-14"
              allowLeadingZeros={false}
              decimalScale={ETH_DECIMALS}
              inputMode="numeric"
            />
            <Image
              src={coin?.logoUri as string}
              alt={coin.symbol}
              width={14}
              height={14}
              className="w-14px h-14px object-cover rounded-full"
            />
          </div>
        </div>
        <div className="desktop:border-t border-white-50 grid grid-cols-4 mt-2 desktop:mt-0 desktop:gap-0 gap-1">
          {PERCENT_SELL.map((item, index) => (
            <div
              key={item}
              className={`border desktop:border-0 ${
                index === PERCENT_SELL.length - 1 ? '' : 'desktop:border-r '
              } desktop:rounded-none rounded-[4px] border-white-50 p-1 text-white-800 body-md-light-14 text-center cursor-pointer hover:bg-white-100`}
              onClick={() => {
                if (!tokenBalance) return;
                setPercentageOfBalance(tokenBalance.toString(), item);
              }}
            >
              {item === 100 ? 'max' : `${item}%`}
            </div>
          ))}
        </div>
      </div>

      {BigNumber(estimatedAmountOut || 0).isGreaterThan(0) && (
        <div className="text-right text-white-500 text-sm my-2">
          ≈ {estimatedAmountOut} HYPE
        </div>
      )}

      <div className="flex w-full cursor-pointer items-center justify-between my-3 gap-2">
        <div className="flex gap-2">
          <div className="flex items-center gap-2 text-white-500">
            <CoinTip /> <p className="body-sm-regular-12">0.01</p>
          </div>
          <div className="flex items-center gap-2 text-white-500">
            <SlippageIcon /> <p className="body-sm-regular-12">{slippage}%</p>
          </div>
        </div>

        {needsApproval ? (
          <AppButton
            className="flex desktop:hidden justify-center h-[40px] !rounded-[4px] !gap-1 flex-1"
            onClick={handleApprove}
            disabled={isDisableSellButton || isApproving}
            isLoading={isApproving}
            type="button"
            buttonType="contained-brand"
          >
            {isApproving ? 'Approving...' : 'Approve Token'}
          </AppButton>
        ) : (
          <AppButton
            className="flex desktop:hidden justify-center h-[40px] !rounded-[4px] !gap-1 flex-1"
            onClick={handleSellCoin}
            disabled={isDisableSellButton}
            isLoading={isLoading}
            type="submit"
            buttonType="contained-brand"
          >
            <FlashIcon /> Sell
          </AppButton>
        )}

        <div
          className="flex items-center gap-2 text-white-500 desktop:p-0 desktop:border-0 p-2 border border-white-50 rounded-[4px]"
          onClick={() => setIsOpenModalSettingSlippage(true)}
        >
          <SettingIcon />{' '}
          <p className="body-sm-regular-12 desktop:block hidden">Settings</p>
        </div>
      </div>

      {/* Approval button for swap (desktop) */}
      {needsApproval ? (
        <AppButton
          className="desktop:flex hidden w-full justify-center h-[48px] !rounded-[4px] !gap-1"
          onClick={handleApprove}
          disabled={isDisableSellButton || isApproving}
          isButtonAuth
          classNameBtnAuth="w-full"
          isLoading={isApproving}
          type="button"
          buttonType="contained-brand"
        >
          {isApproving ? 'Approving...' : 'Approve Token'}
        </AppButton>
      ) : (
        <AppButton
          className="desktop:flex hidden w-full justify-center h-[48px] !rounded-[4px] !gap-1"
          onClick={handleSellCoin}
          disabled={isDisableSellButton}
          isButtonAuth
          classNameBtnAuth="w-full"
          isLoading={isLoading}
          type="submit"
          buttonType="contained-brand"
        >
          <FlashIcon /> Sell
        </AppButton>
      )}

      {isOpenModalSettingSlippage && (
        <ModalSettingSlippage
          isOpen={isOpenModalSettingSlippage}
          onClose={() => setIsOpenModalSettingSlippage(false)}
          slippage={slippage}
          setSlippage={setSlippage}
        />
      )}
    </div>
  );
};

export default SellOrderForm;
