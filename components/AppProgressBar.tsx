interface IProps {
  percent: number | string;
}
const AppProgressBar = ({ percent = 0 }: IProps) => {
  return (
    <div>
      <div className="h-[4px] w-full my-2 rounded-[100px] bg-white-50">
        <div
          className={`h-[4px] rounded-[100px]`}
          style={{
            background:
              'linear-gradient(90deg, var(--Blue-500, #80B2FF) 0%, var(--Orange-600, rgba(246, 139, 30, 0.90)) 100%)',
            width: percent + '%',
          }}
        />
      </div>
    </div>
  );
};
export default AppProgressBar;
