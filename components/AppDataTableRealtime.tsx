import React, {
  // useState,
  ReactNode,
  CSSProperties,
  forwardRef,
  // useImperativeHandle,
  useEffect,
  useRef,
  useImperativeHandle,
  useState,
} from 'react';
import { AppDataTable } from './AppDataTable';
import { AppBroadcast } from '@/libs/broadcast';
import { TBroadcastEvent } from '@/libs/broadcast';

type UpdateItemAction = {
  broadcastName: string;
  fieldKey: string;
  formatter: (data: any) => any;
  update: (oldData: any, newData: any) => any;
};

interface AppDataTableRealtimeProps<T> {
  ref?: React.RefObject<HTMLDivElement>;
  getData: (params: { page: number; limit: number }) => Promise<{ data: T[] }>;
  renderHeader: () => ReactNode;
  renderRow: (item: T, index: number) => ReactNode;
  height?: number | string;
  limit?: number;
  containerStyle?: CSSProperties;
  headerStyle?: CSSProperties;
  bodyStyle?: CSSProperties;
  rowStyle?: CSSProperties;
  noDataMessage?: string;
  overrideHeaderClassName?: string;
  overrideBodyClassName?: string;
  handleAddNewItem?: {
    broadcastName: string;
    fieldKey: string;
    formatter: (data: any) => any;
  };
  handleUpdateItem?: UpdateItemAction[];
  isPolling?: boolean;
  minWidth?: number;
  shouldAutoFetchOnInit?: boolean;
  isBasicTable?: boolean;
  isHideHeader?: boolean;
  components?: any;
}

export const AppDataTableRealtime = forwardRef<
  HTMLDivElement,
  AppDataTableRealtimeProps<any>
>(
  (
    {
      getData,
      renderHeader,
      renderRow,
      height = 400,
      limit = 20,
      containerStyle,
      headerStyle,
      bodyStyle,
      rowStyle,
      noDataMessage = 'No data',
      overrideHeaderClassName,
      overrideBodyClassName,
      handleAddNewItem,
      handleUpdateItem,
      minWidth,
      shouldAutoFetchOnInit = true,
      isBasicTable = false,
      components,
      isHideHeader,
    },
    ref,
  ) => {
    const newDataRef = useRef<any[]>([]);
    const dataTableRef = useRef<HTMLDivElement>(null);
    const [isInitDone, setIsInitDone] = useState(false);
    const intervalRef = useRef<NodeJS.Timeout>();

    useImperativeHandle(
      ref,
      () => {
        return {
          isLoading() {
            (dataTableRef.current as any)?.isLoading();
          },

          async appendNewData(newData: any) {
            (dataTableRef.current as any)?.appendNewData(newData);
          },

          getNewData() {
            return newDataRef?.current || [];
          },

          async refresh() {
            (dataTableRef.current as any)?.refresh();
          },

          async filter(filterParams: any) {
            (dataTableRef.current as any)?.filter(filterParams);
          },

          async polling(filterParams: any) {
            (dataTableRef.current as any)?.polling(filterParams);
          },

          async removeItem(key: string, value: any) {
            (dataTableRef.current as any)?.removeItem(key, value);
          },
          getItems() {
            return (dataTableRef.current as any)?.getItems();
          },
        } as any;
      },
      [],
    );

    useEffect(() => {
      if (!isInitDone) return;

      intervalRef.current = setInterval(() => {
        if (newDataRef.current.length > 0) {
          const oneSecondsAgo = Date.now() - 900;
          newDataRef.current = newDataRef.current?.filter(
            (item) => item && item.markTimestamp > oneSecondsAgo,
          );
        }
      }, 1000);

      // Cleanup interval khi component unmount
      return () => {
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
        }
      };
    }, [isInitDone]);

    useEffect(() => {
      if (!isInitDone || !handleUpdateItem?.length) return;
      const handlers: any = {};
      handleUpdateItem.map((item) => {
        handlers[item.broadcastName] = async (event: TBroadcastEvent) => {
          const data = event.detail;
          const formattedData = await item.formatter(data);
          if (formattedData) {
            (dataTableRef.current as any)?.updateOne(
              formattedData,
              item.fieldKey,
              item.update,
            );
          }
        };
        AppBroadcast.on(item.broadcastName, handlers[item.broadcastName]);
      });
      return () => {
        if (handleUpdateItem?.length) {
          handleUpdateItem.forEach((item) => {
            AppBroadcast.remove(
              item.broadcastName,
              handlers[item.broadcastName],
            );
          });
        }
      };
    }, [isInitDone, handleUpdateItem]);

    useEffect(() => {
      if (!isInitDone || !handleAddNewItem || !handleAddNewItem?.broadcastName)
        return;
      const handleWhenNewItem = async (event: TBroadcastEvent) => {
        const data = event.detail;
        const formattedData = await handleAddNewItem.formatter(data);
        if (formattedData) {
          formattedData.isNew = true;
          formattedData.markTimestamp = Date.now();
          const isDuplicate = newDataRef.current.some(
            (item: any) =>
              item[handleAddNewItem.fieldKey] ===
              formattedData[handleAddNewItem.fieldKey],
          );
          if (!isDuplicate) {
            (dataTableRef.current as any)?.appendNewData(formattedData);
            newDataRef.current = [formattedData, ...newDataRef.current];
          }
        }
      };
      AppBroadcast.on(handleAddNewItem.broadcastName, handleWhenNewItem);
      return () => {
        if (handleAddNewItem && handleAddNewItem.broadcastName) {
          AppBroadcast.remove(
            handleAddNewItem.broadcastName,
            handleWhenNewItem,
          );
        }
      };
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isInitDone, handleAddNewItem?.broadcastName]);

    return (
      <AppDataTable
        isHideHeader={isHideHeader}
        ref={dataTableRef}
        minWidth={minWidth}
        shouldAutoFetchOnInit={shouldAutoFetchOnInit}
        containerStyle={containerStyle}
        overrideHeaderClassName={overrideHeaderClassName}
        overrideBodyClassName={overrideBodyClassName}
        headerStyle={headerStyle}
        bodyStyle={bodyStyle}
        rowStyle={rowStyle}
        noDataMessage={noDataMessage}
        limit={limit}
        getData={getData as any}
        renderHeader={renderHeader}
        renderRow={renderRow}
        height={height}
        onInitializationDone={() => {
          setIsInitDone(true);
        }}
        isBasicTable={isBasicTable}
        components={components}
      />
    );
  },
);

AppDataTableRealtime.displayName = 'AppDataTableRealtime';
