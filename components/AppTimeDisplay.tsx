import { useForceRender } from '@/hooks/useForceRender';
import { formatAgeTime, formatUnixTimestamp } from '@/utils/format';
import React from 'react';
import { useEffect } from 'react';

export const AppTimeDisplay = ({
  timestamp,
  isAgo = false,
  prefix = '',
  suffix = 'ago',
  classNameWrapper = '',
}: {
  timestamp: number;
  isAgo?: boolean;
  suffix?: string;
  prefix?: string;
  classNameWrapper?: string;
}) => {
  const forceRender = useForceRender();

  useEffect(() => {
    if (!isAgo) {
      return;
    }
    const intervalId = setInterval(() => {
      forceRender();
    }, 1000);

    return () => clearInterval(intervalId);
  }, [timestamp, isAgo]);

  return (
    <div className={classNameWrapper}>
      {isAgo
        ? formatAgeTime(timestamp, suffix, prefix)
        : formatUnixTimestamp(timestamp, 'MMM DD, HH:mm:ss')}
    </div>
  );
};
