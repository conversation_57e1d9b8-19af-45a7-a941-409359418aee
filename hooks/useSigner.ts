import { ConnectedWallet, useWallets } from '@privy-io/react-auth';
import { ethers, JsonRpcSigner } from 'ethers';
import { useEffect, useState } from 'react';

export const useSigner = () => {
  const { wallets } = useWallets();

  const [activeWallet, setActiveWallet] = useState<ConnectedWallet | null>(
    null,
  );
  const [signer, setSigner] = useState<JsonRpcSigner | null>(null);

  useEffect(() => {
    if (wallets.length > 0) {
      setActiveWallet(wallets[0]);
    }
  }, [wallets]);

  useEffect(() => {
    const extractSigner = async () => {
      if (activeWallet) {
        const provider = await activeWallet.getEthereumProvider();
        const web3Provider = new ethers.BrowserProvider(provider);
        const signer = await web3Provider.getSigner();
        setSigner(signer);
      }
    };
    extractSigner();
  }, [activeWallet]);
  return { signer, activeWallet };
};
