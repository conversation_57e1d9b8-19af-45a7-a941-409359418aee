import { useState, useCallback, useEffect } from 'react';
import { Contract, formatEther } from 'ethers';
import { CONTRACT_ADDRESSES, CREATOR_REWARD_ABI } from '@/constants';
import { useSigner } from '@/hooks/useSigner';
import { useCoinPageContext } from '@/app/coins/[slug]/provider';
import { errorMsg, successMsg } from '@/libs/toast';

interface CreatorPoolInfo {
  initializer: string;
  creator: string;
  totalRewards: string;
}

export const useCreatorReward = () => {
  const { signer } = useSigner();
  const { coinAddress } = useCoinPageContext();
  const [creatorPoolInfo, setCreatorPoolInfo] = useState<CreatorPoolInfo | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isClaiming, setIsClaiming] = useState(false);
  const [isEligibleCreator, setIsEligibleCreator] = useState(false);

  // Get creator pool information
  const getCreatorPoolInfo = useCallback(async () => {
    if (!signer || !coinAddress) return null;

    try {
      setIsLoading(true);
      const contract = new Contract(
        CONTRACT_ADDRESSES.CREATOR_REWARD,
        CREATOR_REWARD_ABI,
        signer
      );

      const poolInfo = await contract.getCreatorPoolInfo(coinAddress);
      
      const formattedInfo: CreatorPoolInfo = {
        initializer: poolInfo[0],
        creator: poolInfo[1],
        totalRewards: formatEther(poolInfo[2])
      };

      setCreatorPoolInfo(formattedInfo);
      
      // Check if current user is the creator
      const userAddress = await signer.getAddress();
      setIsEligibleCreator(
        formattedInfo.creator.toLowerCase() === userAddress.toLowerCase()
      );

      return formattedInfo;
    } catch (error: any) {
      console.error('Error fetching creator pool info:', error);
      
      // Handle specific contract errors
      if (error?.reason === 'CreatorNotExist') {
        setCreatorPoolInfo(null);
        setIsEligibleCreator(false);
        return null;
      }
      
      errorMsg('Failed to fetch creator reward information');
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [signer, coinAddress]);

  // Claim creator rewards
  const claimCreatorPool = useCallback(async () => {
    if (!signer || !coinAddress || !isEligibleCreator) {
      errorMsg('You are not eligible to claim rewards for this token');
      return false;
    }

    try {
      setIsClaiming(true);
      const contract = new Contract(
        CONTRACT_ADDRESSES.CREATOR_REWARD,
        CREATOR_REWARD_ABI,
        signer
      );

      const tx = await contract.claimCreatorPool(coinAddress);
      await tx.wait();

      successMsg('Creator rewards claimed successfully!');
      
      // Refresh the pool info after claiming
      await getCreatorPoolInfo();
      
      return true;
    } catch (error: any) {
      console.error('Error claiming creator rewards:', error);
      
      // Handle specific contract errors
      if (error?.reason === 'RewardToClaimNotValid') {
        errorMsg('No rewards available to claim');
      } else if (error?.reason === 'CreatorNotExist') {
        errorMsg('Creator pool does not exist for this token');
      } else if (error?.reason === 'InvalidCreator') {
        errorMsg('You are not the creator of this token');
      } else {
        errorMsg(error?.message || 'Failed to claim creator rewards');
      }
      
      return false;
    } finally {
      setIsClaiming(false);
    }
  }, [signer, coinAddress, isEligibleCreator, getCreatorPoolInfo]);

  // Check if creator pool exists
  const checkCreatorPoolExists = useCallback(async () => {
    if (!signer || !coinAddress) return false;

    try {
      const contract = new Contract(
        CONTRACT_ADDRESSES.CREATOR_REWARD,
        CREATOR_REWARD_ABI,
        signer
      );

      return await contract.creatorPoolExists(coinAddress);
    } catch (error) {
      console.error('Error checking creator pool existence:', error);
      return false;
    }
  }, [signer, coinAddress]);

  // Auto-fetch creator pool info when dependencies change
  useEffect(() => {
    if (signer && coinAddress) {
      getCreatorPoolInfo();
    }
  }, [signer, coinAddress, getCreatorPoolInfo]);

  return {
    creatorPoolInfo,
    isLoading,
    isClaiming,
    isEligibleCreator,
    getCreatorPoolInfo,
    claimCreatorPool,
    checkCreatorPoolExists,
    // Computed values
    hasRewards: creatorPoolInfo && parseFloat(creatorPoolInfo.totalRewards) > 0,
    rewardAmount: creatorPoolInfo?.totalRewards || '0',
  };
};
