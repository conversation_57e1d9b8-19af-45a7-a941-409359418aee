import { usePrivy, useWallets } from '@privy-io/react-auth';
import { useEffect, useState } from 'react';
import { ethers } from 'ethers';
import config from '@/config';
import { CONTRACT_ADDRESSES, LAUNCHPAD_ABI } from '@/constants';

export interface BondingCurveSDK {
  // Bonding curve functions for meme coin launchpad
  buyTokens: (params: BuyTokensParams) => Promise<string>;
  sellTokens: (params: SellTokensParams) => Promise<string>;
  getBalance: () => Promise<string>;
  getBuyPrice: (tokenAddress: string, amount: string) => Promise<string>;
  getSellPrice: (tokenAddress: string, amount: string) => Promise<string>;
  createToken: (params: CreateTokenParams) => Promise<string>;

  // Additional launchpad functions
  getPoolInfo: (tokenAddress: string) => Promise<PoolInfo>;
  isToken: (tokenAddress: string) => Promise<boolean>;
  estimateBuyExactInCost: (
    tokenAddress: string,
    amountIn: string,
  ) => Promise<string>;
  estimateBuyExactOutCost: (
    tokenAddress: string,
    tokenAmountOut: string,
  ) => Promise<string>;
}

export interface BuyTokensParams {
  tokenAddress: string;
  ethAmount: string;
  slippage?: number;
}

export interface SellTokensParams {
  tokenAddress: string;
  tokenAmount: string;
  slippage?: number;
}

export interface CreateTokenParams {
  name: string;
  symbol: string;
  description: string;
  imageUrl: string; // This will be used as 'uri' parameter
  twitter?: string;
  telegram?: string;
  website?: string;
  customThreshold?: string; // Optional custom threshold, will use default if not provided
}

export interface PoolInfo {
  realHypeReserves: string;
  realTokenReserves: string;
  virtualTokenReserves: string;
  virtualHypeReserves: string;
  remainTokenReserves: string;
  virtualRemainTokenReserves: string;
  feeRecipient: string;
  isCompleted: boolean;
  threshold: string;
  platformFeeWithdraw: number;
  creatorFeeWithdraw: number;
  stakeFeeWithdraw: number;
  platformStakeFeeWithdraw: number;
  creationTimestamp: string;
  feeDistributionUnlockTime: string;
}

export const useHyperliquidSDK = () => {
  const { authenticated } = usePrivy();
  const { wallets } = useWallets();
  const [sdk, setSdk] = useState<BondingCurveSDK | null>(null);
  const [provider, setProvider] = useState<ethers.JsonRpcProvider | null>(null);
  const [signer, setSigner] = useState<ethers.Signer | null>(null);

  // Get the first EVM wallet
  const activeWallet = wallets.find(
    (wallet) => wallet.walletClientType === 'privy',
  );

  useEffect(() => {
    const initializeSDK = async () => {
      if (!activeWallet || !authenticated) {
        console.log('Waiting for wallet connection...');
        return;
      }

      try {
        // Initialize provider for Hyperliquid
        const rpcProvider = new ethers.JsonRpcProvider(
          config.hyperliquidRpcUrl || 'https://api.hyperliquid-testnet.xyz/evm',
        );
        setProvider(rpcProvider);

        // Get signer from Privy wallet
        const walletProvider = await activeWallet.getEthereumProvider();
        const ethersSigner = new ethers.BrowserProvider(
          walletProvider,
        ).getSigner();
        setSigner(await ethersSigner);

        // Create SDK instance for bonding curve operations
        const bondingCurveSDK: BondingCurveSDK = {
          buyTokens: async (params: BuyTokensParams) => {
            if (!ethersSigner) throw new Error('No signer available');

            try {
              const signer = await ethersSigner;
              // TODO: Implement buy tokens logic
              throw new Error('Buy tokens not implemented');
            } catch (error) {
              console.error('Error buying tokens:', error);
              throw error;
            }
          },

          sellTokens: async (params: SellTokensParams) => {
            if (!ethersSigner) throw new Error('No signer available');

            try {
              const signer = await ethersSigner;
              const contract = new ethers.Contract(
                CONTRACT_ADDRESSES.LAUNCHPAD,
                LAUNCHPAD_ABI,
                signer,
              );

              // Calculate minimum amount out with slippage protection (default 1%)
              const slippage = params.slippage || 1;
              const tokenAmountIn = ethers.parseUnits(params.tokenAmount, 18);

              // Estimate HYPE to receive
              const estimatedHype = await contract.estimateSellTokens(
                params.tokenAddress,
                tokenAmountIn,
              );

              // Apply slippage tolerance
              const amountOutMin =
                (estimatedHype * BigInt(100 - slippage)) / BigInt(100);

              const tx = await contract.sellExactIn(
                params.tokenAddress,
                tokenAmountIn,
                amountOutMin,
              );

              return tx.hash;
            } catch (error) {
              console.error('Error selling tokens:', error);
              throw error;
            }
          },

          getBalance: async () => {
            if (!ethersSigner) throw new Error('No signer available');
            const signer = await ethersSigner;
            const address = await signer.getAddress();
            const balance = await rpcProvider.getBalance(address);
            return ethers.formatEther(balance);
          },

          getBuyPrice: async (tokenAddress: string, amount: string) => {
            try {
              const contract = new ethers.Contract(
                CONTRACT_ADDRESSES.LAUNCHPAD,
                LAUNCHPAD_ABI,
                rpcProvider,
              );

              // Estimate tokens received for the given HYPE amount
              const amountIn = ethers.parseEther(amount);
              const tokensOut = await contract.estimateBuyExactInTokens(
                tokenAddress,
                amountIn,
              );

              return ethers.formatUnits(tokensOut, 18);
            } catch (error) {
              console.error('Error getting buy price:', error);
              // Fallback: return 0 if estimation fails
              return '0';
            }
          },

          getSellPrice: async (tokenAddress: string, amount: string) => {
            try {
              const contract = new ethers.Contract(
                CONTRACT_ADDRESSES.LAUNCHPAD,
                LAUNCHPAD_ABI,
                rpcProvider,
              );

              // Estimate HYPE received for the given token amount
              const tokenAmountIn = ethers.parseUnits(amount, 18);
              const hypeOut = await contract.estimateSellTokens(
                tokenAddress,
                tokenAmountIn,
              );

              return ethers.formatEther(hypeOut);
            } catch (error) {
              console.error('Error getting sell price:', error);
              // Fallback: return 0 if estimation fails
              return '0';
            }
          },

          createToken: async (params: CreateTokenParams) => {
            if (!ethersSigner) throw new Error('No signer available');

            try {
              const signer = await ethersSigner;
              const contract = new ethers.Contract(
                CONTRACT_ADDRESSES.LAUNCHPAD,
                LAUNCHPAD_ABI,
                signer,
              );

              // Use custom threshold or 0 for default
              const customThreshold = params.customThreshold
                ? ethers.parseEther(params.customThreshold)
                : BigInt(0);

              const tx = await contract.createPool(
                params.name,
                params.symbol,
                params.imageUrl, // uri parameter
                params.description,
                params.twitter || '',
                params.telegram || '',
                params.website || '',
                customThreshold,
                {
                  // Pool creation requires a fee
                  value: ethers.parseEther('0.01'), // Adjust based on actual pool creation fee
                },
              );

              return tx.hash;
            } catch (error) {
              console.error('Error creating token:', error);
              throw error;
            }
          },

          getPoolInfo: async (tokenAddress: string) => {
            try {
              const contract = new ethers.Contract(
                CONTRACT_ADDRESSES.LAUNCHPAD,
                LAUNCHPAD_ABI,
                rpcProvider,
              );

              const poolData = await contract.getPool(tokenAddress);

              return {
                realHypeReserves: ethers.formatEther(poolData.realHypeReserves),
                realTokenReserves: ethers.formatUnits(
                  poolData.realTokenReserves,
                  18,
                ),
                virtualTokenReserves: ethers.formatUnits(
                  poolData.virtualTokenReserves,
                  18,
                ),
                virtualHypeReserves: ethers.formatEther(
                  poolData.virtualHypeReserves,
                ),
                remainTokenReserves: ethers.formatUnits(
                  poolData.remainTokenReserves,
                  18,
                ),
                virtualRemainTokenReserves: ethers.formatUnits(
                  poolData.virtualRemainTokenReserves,
                  18,
                ),
                feeRecipient: poolData.feeRecipient.toString(),
                isCompleted: poolData.isCompleted,
                threshold: ethers.formatEther(poolData.threshold),
                platformFeeWithdraw: Number(poolData.platformFeeWithdraw),
                creatorFeeWithdraw: Number(poolData.creatorFeeWithdraw),
                stakeFeeWithdraw: Number(poolData.stakeFeeWithdraw),
                platformStakeFeeWithdraw: Number(
                  poolData.platformStakeFeeWithdraw,
                ),
                creationTimestamp: poolData.creationTimestamp.toString(),
                feeDistributionUnlockTime:
                  poolData.feeDistributionUnlockTime.toString(),
              };
            } catch (error) {
              console.error('Error getting pool info:', error);
              throw error;
            }
          },

          isToken: async (tokenAddress: string) => {
            try {
              const contract = new ethers.Contract(
                CONTRACT_ADDRESSES.LAUNCHPAD,
                LAUNCHPAD_ABI,
                rpcProvider,
              );

              return await contract.isToken(tokenAddress);
            } catch (error) {
              console.error('Error checking if token exists:', error);
              return false;
            }
          },

          estimateBuyExactInCost: async (
            tokenAddress: string,
            amountIn: string,
          ) => {
            try {
              const contract = new ethers.Contract(
                CONTRACT_ADDRESSES.LAUNCHPAD,
                LAUNCHPAD_ABI,
                rpcProvider,
              );

              const amountInWei = ethers.parseEther(amountIn);
              const totalCost = await contract.estimateBuyExactInCost(
                tokenAddress,
                amountInWei,
              );

              return ethers.formatEther(totalCost);
            } catch (error) {
              console.error('Error estimating buy cost:', error);
              return '0';
            }
          },

          estimateBuyExactOutCost: async (
            tokenAddress: string,
            tokenAmountOut: string,
          ) => {
            try {
              const contract = new ethers.Contract(
                CONTRACT_ADDRESSES.LAUNCHPAD,
                LAUNCHPAD_ABI,
                rpcProvider,
              );

              const tokenAmountOutWei = ethers.parseUnits(tokenAmountOut, 18);
              const cost = await contract.estimateBuyExactOutCost(
                tokenAddress,
                tokenAmountOutWei,
              );

              return ethers.formatEther(cost);
            } catch (error) {
              console.error('Error estimating buy exact out cost:', error);
              return '0';
            }
          },
        };

        setSdk(bondingCurveSDK);
      } catch (error) {
        console.error('Failed to initialize Bonding Curve SDK:', error);
      }
    };

    initializeSDK();
  }, [activeWallet, authenticated]);

  return {
    sdk,
    activeWallet,
    provider,
    signer,
    isConnected: !!activeWallet && authenticated,
  };
};
