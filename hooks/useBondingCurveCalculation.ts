import { useState, useCallback } from 'react';
import { Contract, formatEther, parseEther, Interface } from 'ethers';
import { CONTRACT_ADDRESSES, LAUNCHPAD_ABI, MULTICALL_ABI } from '@/constants';
import { etherProvider } from '@/utils/helper';
import BigNumber from 'bignumber.js';
import { calculateTokenAmountReceived, getHypeInitReserve } from '@/utils/swap';
import config from '@/config';

export interface BondingCurveCalculation {
  tokensToReceive: string;

  isCalculating: boolean;
  error: string | null;

  calculateFromInputAmount: (amount: string) => Promise<any>;
  reset: () => void;
}

export const useBondingCurveCalculation = (): BondingCurveCalculation => {
  const [tokensToReceive, setTokensToReceive] = useState<string>('0');

  const [isCalculating, setIsCalculating] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const multicallContract = new Contract(
    CONTRACT_ADDRESSES.MULTICALL,
    MULTICALL_ABI,
    etherProvider,
  );

  const launchpadInterface = new Interface(LAUNCHPAD_ABI);

  const calculateFromInputAmount = useCallback(
    async (amount: string) => {
      if (!amount || amount === '0') {
        setTokensToReceive('0');
        setError(null);
        return;
      }

      try {
        const amountBN = new BigNumber(amount);
        if (amountBN.isNaN()) {
          setError('Please enter a valid number');
          return;
        }
        if (amountBN.isLessThanOrEqualTo(0)) {
          setError('Amount must be greater than 0');
          return;
        }
      } catch {
        setError('Please enter a valid number');
        return;
      }

      setIsCalculating(true);
      setError(null);

      try {
        const amountInWei = parseEther(amount);

        const virtualTokenReserves =
          config.bondingConfig.initVirtualTokenReserves;
        const virtualHypeReserves = getHypeInitReserve(
          config.bondingConfig.targetRaise,
        );
        const amountOut = calculateTokenAmountReceived(
          virtualHypeReserves.toString(),
          virtualTokenReserves.toString(),
          amountInWei.toString(),
        );

        return formatEther(amountOut);
      } catch (error) {
        console.error('Error calculating bonding curve:', error);
        setError('Failed to calculate token amount');
        setTokensToReceive('0');
      } finally {
        setIsCalculating(false);
      }
    },
    [multicallContract, launchpadInterface],
  );

  const reset = useCallback(() => {
    setTokensToReceive('0');
    setError(null);
  }, []);

  return {
    tokensToReceive,

    isCalculating,
    error,

    calculateFromInputAmount,
    reset,
  };
};
