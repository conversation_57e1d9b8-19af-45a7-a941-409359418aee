import { useEffect, useState } from 'react';
import rf from '@/services/RequestFactory';
// import config from '@/config';

export const usePrice = () => {
  const [hypePriceUsd, setHypePriceUsd] = useState('');
  const [shroPrice, setShroPrice] = useState('');

  const getHypePrice = async () => {
    try {
      const data = await rf.getRequest('MetadataRequest').getHypePrice();
      setHypePriceUsd(data.priceUsd || 0);
    } catch (error) {
      console.error('Error fetching HYPE price:', error);
    }
  };

  const getShroPrice = async () => {
    // try {
    //   await fetch(config.shroPriceApiUrl)
    //     .then((response: Response) => response.text())
    //     .then((responseTest: string) => {
    //       const data = JSON.parse(responseTest);
    //       setShroPrice(data?.tokenBase?.priceUsd || '0');
    //     });
    // } catch (error) {
    //   console.error('Error fetching SUI price:', error);
    // }
    setShroPrice('1');
  };

  useEffect(() => {
    const getPriceInterval = setInterval(() => {
      getHypePrice();
      getShroPrice();
    }, 30000);

    getHypePrice();
    getShroPrice();

    return () => clearInterval(getPriceInterval);
  }, []);

  return {
    hypePriceUsd,
    shroPrice,
  };
};
