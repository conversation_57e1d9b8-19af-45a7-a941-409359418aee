import { Contract, ethers } from 'ethers';
import { useCallback, useEffect, useState } from 'react';
import { usePrivy } from '@privy-io/react-auth';
import { etherProvider, publicProvider } from '@/utils/helper';
import { useSigner } from './useSigner';
import { ERC20_ABI } from '@/constants';

export const useBalance = (tokenAddress?: string) => {
  const [ethBalance, setEthBalance] = useState<number | string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [tokenBalance, setTokenBalance] = useState<number | string | null>(
    null,
  );

  const { signer } = useSigner();
  const { authenticated } = usePrivy();

  const fetchBalances = useCallback(async () => {
    if (!signer?.address || !authenticated) return;
    setIsLoading(true);
    try {
      const balance = await publicProvider.getBalance({
        address: signer.address as any,
      });

      setEthBalance(ethers.formatEther(balance));
    } catch (error) {
      console.error('Error fetching balances:', error);
    } finally {
      setIsLoading(false);
    }
  }, [signer?.address, authenticated, publicProvider]);

  const fetchTokenBalance = useCallback(async () => {
    if (!signer?.address || !tokenAddress || !etherProvider) {
      return;
    }

    setIsLoading(true);
    try {
      const tokenContract = new Contract(tokenAddress, ERC20_ABI, signer);
      const tokenBalance = await tokenContract.balanceOf(signer?.address);
      console.log(tokenBalance, 'tokenBalance');
      setTokenBalance(ethers.formatEther(tokenBalance));
    } catch (error) {
      console.error('Error fetching balances:', error);
    } finally {
      setIsLoading(false);
    }
  }, [signer?.address, authenticated, etherProvider, tokenAddress, signer]);

  useEffect(() => {
    if (signer?.address && tokenAddress) {
      fetchTokenBalance();
    }
  }, [signer?.address, authenticated, tokenAddress, fetchTokenBalance]);

  useEffect(() => {
    if (signer?.address) {
      fetchBalances();
    }
  }, [signer?.address, authenticated, fetchBalances]);

  return {
    ethBalance,
    isLoading,
    tokenBalance,
    fetchTokenBalance,
    fetchBalances,
  };
};
