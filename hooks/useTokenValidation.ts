import { CONTRACT_ADDRESSES, ERC20_ABI, MULTICALL_ABI } from '@/constants';
import { errorMsg } from '@/libs/toast';
import { Contract, Interface, isAddress } from 'ethers';
import { useCallback, useState, useEffect } from 'react';
import { useSigner } from './useSigner';
import { useDebounce } from './useDebounce';

export interface TokenInfo {
  name: string;
  symbol: string;
  decimals: number;
  balance: string;
  tokenAddress: string;
}

interface TokenValidationResult {
  isValidating: boolean;
  tokenInfo: TokenInfo | null;
  error: string | null;
  validateTokenAndFetchInfo: (
    tokenAddress: string,
  ) => Promise<TokenInfo | null>;
  clearTokenInfo: () => void;
}

export const useTokenValidation = (
  tokenAddress: string,
): TokenValidationResult => {
  const [isValidating, setIsValidating] = useState<boolean>(false);
  const [tokenInfo, setTokenInfo] = useState<TokenInfo | null>(null);
  const [error, setError] = useState<string | null>(null);

  const debouncedTokenAddress = useDebounce(tokenAddress, 500);

  const { signer } = useSigner();

  const validateTokenAndFetchInfo = useCallback(
    async (tokenAddress: string): Promise<TokenInfo | null> => {
      if (!tokenAddress) {
        setError('Token address is required');
        return null;
      }

      if (!isAddress(tokenAddress)) {
        setError('Invalid token address format');
        setTokenInfo(null);
        return null;
      }

      if (!signer) {
        setError('Wallet not connected');
        return null;
      }

      setIsValidating(true);
      setError(null);

      try {
        const multicallContract = new Contract(
          CONTRACT_ADDRESSES.MULTICALL,
          MULTICALL_ABI,
          signer,
        );

        const erc20Interface = new Interface(ERC20_ABI);

        const calls = [
          {
            target: tokenAddress,
            callData: erc20Interface.encodeFunctionData('name', []),
          },
          {
            target: tokenAddress,
            callData: erc20Interface.encodeFunctionData('symbol', []),
          },
          {
            target: tokenAddress,
            callData: erc20Interface.encodeFunctionData('decimals', []),
          },
          {
            target: tokenAddress,
            callData: erc20Interface.encodeFunctionData('balanceOf', [
              signer.address,
            ]),
          },
        ];

        const [, returnData] = await multicallContract.aggregate(calls);

        const name = erc20Interface.decodeFunctionResult(
          'name',
          returnData[0],
        )[0];
        const symbol = erc20Interface.decodeFunctionResult(
          'symbol',
          returnData[1],
        )[0];
        const decimals = erc20Interface.decodeFunctionResult(
          'decimals',
          returnData[2],
        )[0];
        const balance = erc20Interface.decodeFunctionResult(
          'balanceOf',
          returnData[3],
        )[0];

        const tokenData: TokenInfo = {
          name,
          symbol,
          decimals: Number(decimals),
          balance: balance.toString(),
          tokenAddress,
        };

        setTokenInfo(tokenData);
        setError(null);
        return tokenData;
      } catch (error: any) {
        console.error('Token validation error:', error);
        let errorMessage = 'Failed to validate token';

        if (error?.message?.includes('call revert exception')) {
          errorMessage = 'Invalid token contract or not an ERC20 token';
        } else if (error?.message?.includes('network')) {
          errorMessage = 'Network error. Please check your connection';
        } else if (error?.message) {
          errorMessage = error.message;
        }

        setError(errorMessage);
        setTokenInfo(null);
        errorMsg(errorMessage);
        return null;
      } finally {
        setIsValidating(false);
      }
    },
    [signer],
  );

  const clearTokenInfo = useCallback(() => {
    setTokenInfo(null);
    setError(null);
  }, []);

  useEffect(() => {
    if (!debouncedTokenAddress) {
      clearTokenInfo();
      return;
    }

    if (!isAddress(debouncedTokenAddress)) {
      setError('Invalid token address format');
      setTokenInfo(null);
      return;
    }

    setError(null);

    if (debouncedTokenAddress && isAddress(debouncedTokenAddress) && signer) {
      validateTokenAndFetchInfo(debouncedTokenAddress);
    }
  }, [
    debouncedTokenAddress,
    signer,
    validateTokenAndFetchInfo,
    clearTokenInfo,
  ]);

  return {
    isValidating,
    tokenInfo,
    error,
    validateTokenAndFetchInfo,
    clearTokenInfo,
  };
};
