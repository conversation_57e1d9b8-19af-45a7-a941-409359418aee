import { useState, useCallback } from 'react';
import { Contract, parseEther, formatEther, MaxUint256 } from 'ethers';
import { CONTRACT_ADDRESSES, ERC20_ABI } from '@/constants';
import { useSigner } from './useSigner';
import { errorMsg, successMsg } from '@/libs/toast';
import BigNumber from 'bignumber.js';

export enum ApprovalType {
  SWAP = 'SWAP',
  TOKEN_LOCK = 'TOKEN_LOCK',
}

interface TokenApprovalResult {
  // State
  isCheckingAllowance: boolean;
  isApproving: boolean;
  currentAllowance: string;
  error: string | null;

  // Functions
  checkAllowance: (tokenAddress: string, amount: string) => Promise<boolean>;
  approveToken: (
    tokenAddress: string,
    amount?: string,
  ) => Promise<string | null>;
  approveMax: (tokenAddress: string) => Promise<string | null>;
  hasInsufficientAllowance: (
    tokenAddress: string,
    amount: string,
  ) => Promise<boolean>;
  refreshAllowance: (tokenAddress: string) => Promise<void>;
}

export const useTokenApproval = (
  approvalType: ApprovalType = ApprovalType.SWAP,
): TokenApprovalResult => {
  const [isCheckingAllowance, setIsCheckingAllowance] =
    useState<boolean>(false);
  const [isApproving, setIsApproving] = useState<boolean>(false);
  const [currentAllowance, setCurrentAllowance] = useState<string>('0');
  const [error, setError] = useState<string | null>(null);

  const { signer } = useSigner();

  // Get the appropriate spender address based on approval type
  const getSpenderAddress = useCallback(() => {
    switch (approvalType) {
      case ApprovalType.SWAP:
        return CONTRACT_ADDRESSES.SWAP_ROUTER_V3;
      case ApprovalType.TOKEN_LOCK:
        return CONTRACT_ADDRESSES.TOKEN_LOCK;
      default:
        return CONTRACT_ADDRESSES.LAUNCHPAD;
    }
  }, [approvalType]);

  const checkAllowance = useCallback(
    async (tokenAddress: string, amount: string): Promise<boolean> => {
      if (!signer?.address || !tokenAddress || !amount) {
        setError('Missing required parameters for allowance check');
        return false;
      }

      if (new BigNumber(amount).isLessThanOrEqualTo(0)) {
        setError('Amount must be greater than 0');
        return false;
      }

      setIsCheckingAllowance(true);
      setError(null);

      try {
        const tokenContract = new Contract(tokenAddress, ERC20_ABI, signer);
        const spenderAddress = getSpenderAddress();

        // Get current allowance for the specified contract
        const allowance = await tokenContract.allowance(
          signer.address,
          spenderAddress,
        );

        const allowanceFormatted = formatEther(allowance);
        setCurrentAllowance(allowanceFormatted);

        const requiredAmount = new BigNumber(amount);
        const currentAllowanceBN = new BigNumber(allowanceFormatted);

        const hasSufficientAllowance =
          currentAllowanceBN.isGreaterThanOrEqualTo(requiredAmount);

        console.log('Allowance check:', {
          tokenAddress,
          requiredAmount: amount,
          currentAllowance: allowanceFormatted,
          hasSufficientAllowance,
          spender: spenderAddress,
          approvalType,
        });

        return hasSufficientAllowance;
      } catch (error: any) {
        const errorMessage =
          error?.message || 'Failed to check token allowance';
        console.error('Allowance check error:', error);
        setError(errorMessage);
        return false;
      } finally {
        setIsCheckingAllowance(false);
      }
    },
    [signer, getSpenderAddress, approvalType],
  );

  const approveToken = useCallback(
    async (tokenAddress: string, amount?: string): Promise<string | null> => {
      if (!signer || !tokenAddress) {
        errorMsg('Please connect wallet and provide valid token address');
        return null;
      }

      if (amount && new BigNumber(amount).isLessThanOrEqualTo(0)) {
        errorMsg('Amount must be greater than 0');
        return null;
      }

      setIsApproving(true);
      setError(null);

      try {
        const tokenContract = new Contract(tokenAddress, ERC20_ABI, signer);
        const spenderAddress = getSpenderAddress();

        // Use max amount if no specific amount provided, otherwise use the specified amount
        const approvalAmount = amount ? parseEther(amount) : MaxUint256;

        // Call approve function
        const tx = await tokenContract.approve(spenderAddress, approvalAmount);

        successMsg('Approval transaction submitted');
        console.log('Approval transaction hash:', tx.hash);

        // Wait for transaction confirmation
        await tx.wait();

        successMsg('Token approval successful!');

        // Update current allowance after successful approval
        await refreshAllowance(tokenAddress);

        return tx.hash;
      } catch (error: any) {
        const errorMessage = error?.message || 'Failed to approve tokens';
        console.error('Approval error:', error);
        setError(errorMessage);
        errorMsg(errorMessage);
        return null;
      } finally {
        setIsApproving(false);
      }
    },
    [signer, getSpenderAddress],
  );

  const approveMax = useCallback(
    async (tokenAddress: string): Promise<string | null> => {
      return approveToken(tokenAddress);
    },
    [approveToken],
  );

  const hasInsufficientAllowance = useCallback(
    async (tokenAddress: string, amount: string): Promise<boolean> => {
      const hasSufficientAllowance = await checkAllowance(tokenAddress, amount);
      return !hasSufficientAllowance;
    },
    [checkAllowance],
  );

  const refreshAllowance = useCallback(
    async (tokenAddress: string): Promise<void> => {
      if (!signer?.address || !tokenAddress) {
        return;
      }

      try {
        const tokenContract = new Contract(tokenAddress, ERC20_ABI, signer);
        const spenderAddress = getSpenderAddress();
        const allowance = await tokenContract.allowance(
          signer.address,
          spenderAddress,
        );

        const allowanceFormatted = formatEther(allowance);
        setCurrentAllowance(allowanceFormatted);
      } catch (error) {
        console.error('Error refreshing allowance:', error);
      }
    },
    [signer, getSpenderAddress],
  );

  return {
    // State
    isCheckingAllowance,
    isApproving,
    currentAllowance,
    error,

    // Functions
    checkAllowance,
    approveToken,
    approveMax,
    hasInsufficientAllowance,
    refreshAllowance,
  };
};

// Legacy hook for backward compatibility - use useTokenApproval(ApprovalType.SWAP) instead
export const useSwapTokenApproval = () => useTokenApproval(ApprovalType.SWAP);
