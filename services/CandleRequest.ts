import config from '@/config';
import BaseRequest from './BaseRequest';

export default class CandleRequest extends BaseRequest {
  getUrlPrefix() {
    return config.apiUrl;
  }

  async getCandles(params: {
    limit: number;
    to: number;
    tokenAddress: string;
    queryBy: string;
    resolution: number | string;
  }) {
    const requestId = `candle-req-${Date.now()}-${Math.random()
      .toString(36)
      .substr(2, 9)}`;
    const url = `/candle`;

    console.log(`🌐 [${requestId}] CandleRequest.getCandles called`, {
      timestamp: new Date().toISOString(),
      url: `${this.getUrlPrefix()}${url}`,
      params,
      fullUrl: `${this.getUrlPrefix()}${url}?limit=${params.limit}&to=${
        params.to
      }&tokenAddress=${params.tokenAddress}&queryBy=${
        params.queryBy
      }&resolution=${params.resolution}`,
      stackTrace: new Error().stack?.split('\n').slice(1, 8),
    });

    try {
      const result = await this.get(url, params);
      console.log(`✅ [${requestId}] CandleRequest.getCandles response`, {
        timestamp: new Date().toISOString(),
        candleCount: result?.candles?.length || 0,
        hasData: !!result?.candles,
      });
      return result;
    } catch (error) {
      console.error(`❌ [${requestId}] CandleRequest.getCandles error:`, error);
      throw error;
    }
  }
}
