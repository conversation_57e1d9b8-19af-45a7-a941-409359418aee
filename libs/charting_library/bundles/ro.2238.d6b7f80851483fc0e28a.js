(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[2238],{4543:e=>{e.exports={en:["Cancel"],ro:["Cancel"]}},47742:e=>{e.exports={en:["Close menu"],ro:["Close menu"]}},19295:e=>{e.exports={en:["Ok"],ro:["Ok"]}},85888:e=>{e.exports={en:["No results found"],ro:["No results found"]}},64e3:e=>{e.exports={en:["Save"],ro:["Save"]}},51405:e=>{e.exports={en:["Save Indicator Template"],ro:["Save Indicator Template"]}},6081:e=>{e.exports={en:["Saved indicators"],ro:["Saved indicators"]}},63035:e=>{e.exports={en:["Selecting this option will set the {interval} interval on the chart when this template is applied"],ro:["Selecting this option will set the {interval} interval on the chart when this template is applied"]}},13860:e=>{e.exports={en:["Selecting this option will set the {symbol} symbol on the chart when this template is applied"],ro:["Selecting this option will set the {symbol} symbol on the chart when this template is applied"]}},56542:e=>{e.exports={en:["Study Template '{templateName}' already exists. Do you really want to replace it?"],ro:["Study Template '{templateName}' already exists. Do you really want to replace it?"]}},18698:e=>{e.exports={en:["Remember Interval"],ro:["Remember Interval"]}},57149:e=>{e.exports={en:["Remember Symbol"],ro:["Remember Symbol"]}},59233:e=>{e.exports={en:["Template name"],ro:["Template name"]}}}]);