"use strict";(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[507],{74011:(t,e,i)=>{i.d(e,{PolygonRenderer:()=>p});var s=i(34026),r=i(4652),n=i(91046),a=i(49857),o=i(56468),l=i(19063),d=i(75919),h=i(61993),c=i(37743),u=i(51056),f=i(64034);class p extends d.MediaCoordinatesPaneRenderer{constructor(t){super(),this._data=null,this._backHittest=new o.HitTestResult(o.HitTarget.MovePointBackground),this._points=[],this._hittest=null!=t?t:new o.HitTestResult(o.HitTarget.MovePoint)}setData(t){this._data=t,this._points=t.points}hitTest(t){if(null===this._data||void 0!==this._data.mouseTouchable&&!this._data.mouseTouchable)return null;const e=Math.max((0,h.interactionTolerance)().line,Math.ceil(this._data.linewidth/2)),i=this._points.length;if(1===i){return(0,s.pointInCircle)(t,this._points[0],e)?this._hittest:null}for(let s=1;s<i;s++){const i=this._points[s-1],n=this._points[s];if((0,r.distanceToSegment)(i,n,t).distance<=e)return this._hittest}if(this._data.filled&&this._data.fillBackground&&i>0){const s=this._points[0],n=this._points[i-1];if((0,r.distanceToSegment)(s,n,t).distance<=e)return this._hittest}return this._data.filled&&this._data.fillBackground&&(0,s.pointInPolygon)(t,this._data.points)?this._backHittest:null}_drawImpl(t){var e,i;const s=t.context,r=this._points.length;if(null===this._data||0===r)return;if(1===r)return void this._drawPoint(s,this._points[0],this._data.linewidth/2,this._data.color);s.beginPath();const o=this._data.linestyle===u.LINESTYLE_SOLID?"round":"butt",d=null!==(e=this._data.linecap)&&void 0!==e?e:o;s.lineCap=d,s.strokeStyle=this._data.color,s.lineWidth=this._data.linewidth,s.lineJoin=null!==(i=this._data.linejoin)&&void 0!==i?i:"round",(0,c.setLineStyle)(s,this._data.linestyle);const h=this._points[0];s.moveTo(h.x,h.y);for(const t of this._points)s.lineTo(t.x,t.y);if(this._data.filled&&this._data.fillBackground&&(s.fillStyle=(0,l.generateColor)(this._data.backcolor,this._data.transparency),s.fill()),this._data.filled&&!this._data.skipClosePath&&s.closePath(),r>1){if(this._data.leftend===a.LineEnd.Arrow){const t=this._correctArrowPoints(this._points[1],this._points[0],s.lineWidth,d);(0,n.drawArrow)(t[0],t[1],s,s.lineWidth,f.dpr1PixelRatioInfo)}if(this._data.rightend===a.LineEnd.Arrow){const t=this._correctArrowPoints(this._points[r-2],this._points[r-1],s.lineWidth,d);(0,n.drawArrow)(t[0],t[1],s,s.lineWidth,f.dpr1PixelRatioInfo)}}this._data.linewidth>0&&s.stroke()}_drawPoint(t,e,i,s){0!==i&&(t.beginPath(),t.fillStyle=s,t.arc(e.x,e.y,i,0,2*Math.PI,!0),t.fill(),t.closePath())}_correctArrowPoints(t,e,i,s){const r=e.subtract(t),n=r.length();if("butt"===s||n<1)return[t,e];const a=n+i/2;return[t,r.scaled(a/n).add(t)]}}},55269:(t,e,i)=>{i.r(e),i.d(e,{BackgroundPaneView:()=>l});var s=i(50151),r=i(95201),n=i(7114),a=i(19063);class o{constructor(t){this._data=t}hitTest(t){return null}draw(t,e){}drawBackground(t,e){const i=this._data,s=(0,a.generateColor)(this._data.color,this._data.transparency),r=Math.round(i.x1*e.horizontalPixelRatio),o=Math.round(i.x2*e.verticalPixelRatio);(0,
n.fillRect)(t,r,0,o-r,e.bitmapSize.height,s)}}class l{constructor(t,e,i){this._data=[],this._invalidated=!0,this._provider=t,this._model=e,this._data=[]}update(){this._invalidated=!0}renderer(){this._invalidated&&(this._updateViewInternal(),this._invalidated=!1);const t=new r.CompositeRenderer;for(const e of this._data)t.append(new o(e));return t}_updateViewInternal(){this._data=[];const t=this._provider.priceScale(),e=this._model.timeScale();if(!t||t.isEmpty()||e.isEmpty())return;const i=this._provider.graphics().backgrounds();if(0===i.size)return;const r=this._model.timeScale().visibleBarsStrictRange();if(null===r)return;const n=r.firstBar(),a=r.lastBar(),o=(0,s.ensureDefined)(this._provider.properties().childs().graphics.childs().backgrounds);i.forEach(((t,i)=>{const r=(0,s.ensureDefined)(o.childs()[i]).childs();if(!r.visible.value())return;let l,d;t.forEach((t=>{const i=null!==t.start?t.start:(0,s.ensureNotNull)(e.points().range().value()).firstIndex,o=t.stop;if(o<n||a<i)return;let h=e.barBorders(i).left;const c=e.barBorders(o).right;d===i-1&&(h=l||h),l=c,d=o,h<0&&c<0||h>e.width()&&c>e.width()||this._data.push({x1:h,x2:c,color:r.color.value(),transparency:r.transparency.value()})}))}))}}},56208:(t,e,i)=>{i.r(e),i.d(e,{HHistPaneView:()=>w});var s=i(24377),r=i(50151),n=i(9233),a=i(95201),o=i(86441),l=i(7114),d=i(56468),h=i(43290),c=i(15938);function u(t,e){return{min:Math.min(t,e),max:Math.max(t,e)}}const f=(0,h.getVolumeFormatter)();class p{constructor(t){this._data=t}hitTest(t,e){const i=this._data;for(const s of i.histograms){if(s.yRange.min>=t.y||t.y>=s.yRange.max)continue;let r=null;for(const a of s.bars){const o=i.styles[a.styleId];if(!o.visible)continue;if(o.location===n.HHistLocation.Absolute&&(s.xRange.min>=t.x||t.x>=s.xRange.max))continue;null===r&&(r=0,s.bars.forEach((t=>{const e=t.subBarValues.reduce(((t,e)=>t+e));r=Math.max(r,e)})));const l=_(s.xRange,o,e.mediaSize.width),{xBasePoint:h,sign:c,xRangeWidth:u}=l,f=Math.max(o.percentWidth*u/100-a.subBarValues.length,0);let p=h;for(let e=0;e<a.subBarValues.length;e++){const i=a.y,s=a.y+a.height,n=p,o=n+c*(f*a.subBarValues[e]/r);if(p=o,(t.x>=n&&t.x<=o||t.x>=o&&t.x<=n)&&t.y>=i&&t.y<=s)return new d.HitTestResult(d.HitTarget.Regular)}}}return null}draw(t,e){const i=this._data,{horizontalPixelRatio:r,verticalPixelRatio:n}=e;t.save(),i.histograms.forEach((a=>{if(i.histBoxBgColor){t.beginPath(),t.fillStyle=i.histBoxBgColor;const e=Math.round(a.xRange.min*r),s=Math.round(a.yRange.min*n),o=Math.round(a.xRange.max*r)-e,l=Math.round(a.yRange.max*n)-s;t.rect(e,s,o,l),t.fill()}const o=[];let l=0,d=0;a.bars.forEach((t=>{const e=t.subBarValues.reduce(((t,e)=>t+e));l=Math.max(l,e),d+=t.height}));const h=d/a.bars.length,c=(u=h,f=r,Math.floor(u*f)>=1*f?Math.floor(f):0);var u,f;const p=[];if(a.bars.forEach((s=>{const d=i.styles[s.styleId];if(!d.visible)return;if(d.showValues&&d.addToTotalValue)for(let t=0;t<s.subBarValues.length;t++)o[t]=(o[t]||0)+s.subBarValues[t];const h=_(a.xRange,d,e.mediaSize.width),{xBasePoint:u,sign:f,xRangeWidth:m}=h
;if(s.y>e.mediaSize.height||s.y+s.height<0)return;const x=Math.max(d.percentWidth*m/100-s.subBarValues.length,0);let y=u;for(let e=0;e<s.subBarValues.length;e++){const i=s.y,a=s.y+s.height,o=y,h=o+f*(x*s.subBarValues[e]/l);if(Math.abs(h-o)<.5)continue;y=h,t.beginPath(),t.fillStyle=d.colors[e];const u=Math.round(o*r),p=Math.round(i*n),_=Math.round(h*r)-u,v=Math.max(Math.round(a*n)-p-c,1);t.rect(u,p,_,v),t.fill()}if(!d.showValues)return;const w=v(s.displayedValues,d.direction),R=g(m,s.y,s.height,h,d,w);p.push(R)})),o.length>0){const t=i.styles[a.bars[0].styleId],r=_(a.xRange,t,e.mediaSize.width),n=v(o,t.direction),l=a.bars[a.bars.length-1],d=g(r.xRangeWidth,l.y+l.height,h,r,t,n);d.color=(0,s.shiftColor)(d.color,1.5),p.push(d)}const x=Math.min(...p.map((t=>t.fontSize)));if(x>=7.5)for(const i of p)i.fontSize=x,m(t,e,i)})),t.restore()}}function _(t,e,i){const s=e.location===n.HHistLocation.Absolute,r=e.location===n.HHistLocation.Relative,a=e.direction===n.HHistDirection.LeftToRight,o=e.direction===n.HHistDirection.RightToLeft;let l,d;if(s&&a)l=t.min,d=1;else if(s&&o)l=t.max,d=-1;else if(r&&a)l=0,d=1;else{if(!r||!o)throw new Error(`Unknown location/direction values: ${e.location}/${e.direction}`);l=i,d=-1}var h;return{xBasePoint:l,sign:d,xRangeWidth:r?i:(h=t).max-h.min}}function v(t,e){return e===n.HHistDirection.RightToLeft&&(t=t.slice()).reverse(),t.map((t=>f.format(t))).join("x")}function g(t,e,i,s,r,a){const{fontSize:l,verticalOffset:d}=function(t,e,i){return{fontSize:Math.min(Math.round(1.7*t/i.length),Math.round(.6*e)),verticalOffset:.7*e}}(t,i,a),h=r.direction===n.HHistDirection.LeftToRight?"left":"right",{xBasePoint:c,sign:u}=s,f=c+3*u,p=e+d;return{text:a,color:r.valuesColor,fontSize:l,align:h,point:new o.Point(f,p)}}function m(t,e,i){const{text:s,color:r,fontSize:n,align:a,point:o}=i;t.font=`${n}px ${c.CHART_FONT_FAMILY}`,t.fillStyle=r,t.textAlign=a,(0,l.drawScaled)(t,e.horizontalPixelRatio,e.verticalPixelRatio,(()=>t.fillText(s,o.x,o.y)))}var x,y=i(19063);!function(t){t.UpDominatePostfix="UpDominate",t.DownDominatePostfix="DownDominate"}(x||(x={}));class w{constructor(t,e,i,s,r=!1,n=!1){this._invalidated=!0,this._histBoxBgStyle=null,this._provider=t,this._model=e,this._histBoxBgStyle=null!=s?s:null,this._extendToBarsEndings=r,this._alwaysVisible=n,this._rendererData={histograms:[],styles:{}},this._hhistRenderer=new p(this._rendererData)}update(){this._invalidated=!0}renderer(){this._invalidated&&(this._updateViewInternal(),this._invalidated=!1);const t=new a.CompositeRenderer;return t.append(this._hhistRenderer),t}_resetRenderersData(){this._rendererData.histograms=[],this._rendererData.styles={},this._rendererData.histBoxBgColor=void 0}_prepareStyles(){var t,e;const i=(0,r.ensureDefined)(this._provider.graphicsInfo().hhists),s=Object.keys(i),a=(0,r.ensureDefined)(this._provider.properties().childs().graphics.childs().hhists),o=(null===(e=null===(t=this._provider.properties().child("inputs"))||void 0===t?void 0:t.child("volume"))||void 0===e?void 0:e.value())===n.HHistVolumeMode.Delta;for(const t of s){
const e=(0,r.ensureDefined)(a.childs()[t]).childs(),s=(0,r.ensureDefined)(i[t]);if(o)this._rendererData.styles[t+"UpDominate"]={colors:R(e.colors[0].value(),e.transparencies[0].value()),visible:e.visible.value(),percentWidth:e.percentWidth.value(),location:s.location,direction:e.direction.value(),showValues:e.showValues.value(),addToTotalValue:!1,valuesColor:e.valuesColor.value()},this._rendererData.styles[t+"DownDominate"]={colors:R(e.colors[1].value(),e.transparencies[1].value()),visible:e.visible.value(),percentWidth:e.percentWidth.value(),location:s.location,direction:e.direction.value(),showValues:e.showValues.value(),addToTotalValue:!1,valuesColor:e.valuesColor.value()};else{const i=(0,y.generateColor)(e.colors[0].value(),e.transparencies[0].value()),r=e.colors[1]?(0,y.generateColor)(e.colors[1].value(),e.transparencies[1].value()):i;this._rendererData.styles[t]={colors:[i,r],visible:e.visible.value(),percentWidth:e.percentWidth.value(),location:s.location,direction:e.direction.value(),showValues:e.showValues.value(),addToTotalValue:!0,valuesColor:e.valuesColor.value()}}}if(this._histBoxBgStyle){const t=this._histBoxBgStyle.childs();this._rendererData.histBoxBgColor=(0,y.generateColor)(t.color.value(),t.transparency.value())}}_updateViewInternal(){this._resetRenderersData();const t=this._provider.priceScale(),e=this._model.timeScale();if(!t||t.isEmpty()||e.isEmpty())return;if(null===this._provider.firstValue())return;const i=this._provider.graphics().hhistsByTimePointIndex();if(0===i.size)return;const s=e.visibleBarsStrictRange();if(null===s)return;const n=s.firstBar(),a=s.lastBar();this._prepareStyles(),(0,r.ensureDefined)(i).forEach(((i,s)=>{let r=1/0,o=-1/0;i.forEach((t=>{r=Math.min(r,t.firstBarTime),o=Math.max(o,t.lastBarTime)})),!this._alwaysVisible&&(o<n||r>a)||this._updateDataForRenderers(i,t,e)}))}_updateDataForRenderers(t,e,i){var s,a;if(t.size<=0)return;let o=null;if(t.forEach((t=>{o=o||t})),null===o)return;let l=o;t.forEach((t=>{t.priceLow<l.priceLow&&(l=t)}));const d=(null===(a=null===(s=this._provider.properties().child("inputs"))||void 0===s?void 0:s.child("volume"))||void 0===a?void 0:a.value())===n.HHistVolumeMode.Delta,h=function(t,e,i){const s=e.barBorders(t.firstBarTime),r=e.barBorders(t.lastBarTime);return i?u(s.left,r.right):u(s.center,r.center)}(o,i,this._extendToBarsEndings),c=(0,r.ensureNotNull)(this._provider.firstValue()),f=[];t.forEach((t=>{null==t.rate[t.rate.length-1]&&t.rate.splice(-1,1);let i=[],s=[],r=t.styleId;if(d){const[e,n,a]=t.rate[0]>t.rate[1]?[t.rate[1],t.rate[0],"UpDominate"]:[t.rate[0],t.rate[1],"DownDominate"];i=[n-e,e,e],s=[n-e],r+=a}else i=t.rate,s=t.rate;const n=function(t,e,i){return u(e.priceToCoordinate(t.priceHigh,i),e.priceToCoordinate(t.priceLow,i))}(t,e,c);f.push({height:n.max-n.min,y:n.min,subBarValues:i,displayedValues:s,styleId:r})})),f.sort(((t,e)=>t.y-e.y)),this._rendererData.histograms.push({xRange:h,yRange:u(f[0].y,f[f.length-1].y+f[f.length-1].height),bars:f})}}function R(t,e){let i;if((0,y.isHexColor)(t))i=100-e;else{const r=(0,s.tryParseRgba)(t)
;i=100-(null!==r?(0,y.alphaToTransparency)(r[3]):e)}return[(0,y.generateColor)(t,100-i,!0),(0,y.generateColor)(t,100-i/2,!0),(0,y.generateColor)(t,100-i/4,!0)]}},95258:(t,e,i)=>{i.r(e),i.d(e,{HorizLinePaneView:()=>o});var s=i(50151),r=i(56468),n=i(95201),a=i(50600);class o{constructor(t,e,i,s=!1){this._data=[],this._invalidated=!0,this._provider=t,this._model=e,this._extendToBarsEndings=s,this._hitTestResult=void 0!==i?new r.HitTestResult(r.HitTarget.Custom,i):new r.HitTestResult(r.HitTarget.Regular)}update(){this._invalidated=!0}renderer(){this._invalidated&&(this._updateViewInternal(),this._invalidated=!1);const t=new n.CompositeRenderer;for(const e of this._data){const i=new a.HorizontalLineRenderer;i.setData(e),i.setHitTest(this._hitTestResult),t.append(i)}return t}_updateViewInternal(){this._data=[];const t=this._provider.priceScale(),e=this._model.timeScale();if(!t||t.isEmpty()||e.isEmpty())return;const i=this._provider.graphics().horizlines();if(0===i.size)return;const r=this._model.timeScale().visibleBarsStrictRange();if(null===r)return;const n=this._provider.firstValue();if(null===n)return;const a=r.firstBar(),o=r.lastBar(),l=(0,s.ensureDefined)(this._provider.properties().childs().graphics.childs().horizlines);i.forEach(((i,r)=>{const d=(0,s.ensureDefined)(l.childs()[r]).childs();d.visible.value()&&i.forEach((i=>{const r=i.startIndex,l=i.endIndex;if(!i.extendRight&&Math.max(r,l)<a||!i.extendLeft&&Math.min(r,l)>o)return;let h,c;if(!i.extendLeft){const t=e.barBorders(r);h=this._extendToBarsEndings?t.left:t.center}if(!i.extendRight){const t=e.barBorders(l);c=this._extendToBarsEndings?t.right:t.center}this._data.push({y:t.priceToCoordinate((0,s.ensureDefined)(i.level),n),left:h,right:c,color:d.color.value(),linewidth:d.width.value(),linestyle:d.style.value()})}))}))}}},38332:(t,e,i)=>{i.r(e),i.d(e,{PolygonPaneView:()=>d});var s=i(50151),r=i(86441),n=i(56468),a=i(95201),o=i(51056),l=i(74011);class d{constructor(t,e,i){this._data=[],this._invalidated=!0,this._provider=t,this._model=e,this._hitTestResult=void 0!==i?new n.HitTestResult(n.HitTarget.Custom,i):new n.HitTestResult(n.HitTarget.Regular)}update(){this._invalidated=!0}renderer(){this._invalidated&&(this._updateViewInternal(),this._invalidated=!1);const t=new a.CompositeRenderer;for(const e of this._data){const i=new l.PolygonRenderer(this._hitTestResult);i.setData(e),t.append(i)}return t}_updateViewInternal(){this._data=[];const t=this._provider.priceScale(),e=this._model.timeScale();if(!t||t.isEmpty()||e.isEmpty())return;const i=this._provider.graphics().polygons();if(0===i.size)return;const n=this._model.timeScale().visibleBarsStrictRange();if(null===n)return;const a=this._provider.firstValue();if(null===a)return;const l=n.firstBar(),d=n.lastBar(),h=(0,s.ensureDefined)(this._provider.properties().childs().graphics.childs().polygons),c=(0,s.ensureDefined)(this._provider.graphicsInfo().polygons);i.forEach(((i,n)=>{const u=(0,s.ensureDefined)(h.childs()[n]).childs(),f=(0,s.ensureDefined)(c[n]);100!==u.transparency.value()&&i.forEach((i=>{let n=1/0,h=-1/0
;for(const t of i.points){const e=t.index+(t.offset||0);n=Math.min(n,e),h=Math.max(h,e)}if(h<l||d<n)return;const c=i.points.map((i=>{const n=e.indexToCoordinate(i.index+(i.offset||0)),o=t.priceToCoordinate((0,s.ensureDefined)(i.level),a);return new r.Point(n,o)}));this._data.push({points:c,color:u.color.value(),backcolor:u.color.value(),linewidth:f.showBorder?1:0,linestyle:o.LINESTYLE_SOLID,filled:!0,fillBackground:!0,transparency:u.transparency.value(),mouseTouchable:f.mouseTouchable})}))}))}}},51914:(t,e,i)=>{i.r(e),i.d(e,{VertLinePaneView:()=>l});var s=i(50151),r=i(56468),n=i(85904),a=i(95201),o=i(95173);class l{constructor(t,e,i){this._data=[],this._invalidated=!0,this._provider=t,this._model=e,this._hitTestResult=void 0!==i?new r.HitTestResult(r.HitTarget.Custom,i):new r.HitTestResult(r.HitTarget.Regular)}update(){this._invalidated=!0}renderer(){this._invalidated&&(this._updateViewInternal(),this._invalidated=!1);const t=new a.CompositeRenderer;for(const e of this._data){const i=new o.VerticalLineRenderer;i.setData(e),i.setHitTest(this._hitTestResult),t.append(i)}return t}_updateViewInternal(){this._data=[];const t=this._provider.priceScale(),e=this._model.timeScale();if(!t||t.isEmpty()||e.isEmpty())return;const i=this._provider.graphicsInfo().vertlines,r=this._provider.graphics().vertlines();if(0===r.size||void 0===i)return;const a=this._model.timeScale().visibleBarsStrictRange();if(null===a)return;const o=this._provider.firstValue();if(null===o)return;const l=a.firstBar(),d=a.lastBar(),h=(0,s.ensureDefined)(this._provider.properties().childs().graphics.childs().vertlines);r.forEach(((r,a)=>{const c=(0,s.ensureDefined)(h.childs()[a]).childs(),u=(0,s.ensureDefined)(i[a]);c.visible.value()&&r.forEach((i=>{const r=i.index;if(r<l||d<r)return;const{left:a,center:h,right:f}=e.barBorders(r);let p;switch(u.halign){case n.HAlign.Left:p=a;break;case n.HAlign.Right:p=f;break;default:p=h}this._data.push({x:p,top:i.extendTop?void 0:t.priceToCoordinate((0,s.ensureDefined)(i.endPrice),o),bottom:i.extendBottom?void 0:t.priceToCoordinate((0,s.ensureDefined)(i.startPrice),o),color:c.color.value(),linewidth:c.width.value(),linestyle:c.style.value()})}))}))}}}}]);