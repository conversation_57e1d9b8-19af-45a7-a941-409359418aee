"use strict";(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[607],{42838:(e,t,i)=>{i.r(t),i.d(t,{RegressionTrendDefinitionsViewModel:()=>C});var n=i(11542),o=i(45126),r=(i(21251),i(4116)),s=i(31507),l=i(49406),a=i(1183);const u=new o.TranslatedString("change {title} base line visibility",n.t(null,void 0,i(96177))),p=new o.TranslatedString("change {title} base line color",n.t(null,void 0,i(78509))),c=new o.TranslatedString("change {title} base line width",n.t(null,void 0,i(17926))),d=new o.TranslatedString("change {title} base line style",n.t(null,void 0,i(27864))),y=new o.TranslatedString("change {title} up line visibility",n.t(null,void 0,i(42553))),h=new o.TranslatedString("change {title} up line color",n.t(null,void 0,i(89178))),f=new o.TranslatedString("change {title} up line width",n.t(null,void 0,i(63824))),v=new o.TranslatedString("change {title} up line style",n.t(null,void 0,i(51188))),_=new o.TranslatedString("change {title} down line visibility",n.t(null,void 0,i(46410))),S=new o.TranslatedString("change {title} down line color",n.t(null,void 0,i(41638))),m=new o.TranslatedString("change {title} down line width",n.t(null,void 0,i(64615))),P=new o.TranslatedString("change {title} down line style",n.t(null,void 0,i(51842))),g=new o.TranslatedString("change {title} extend lines",n.t(null,void 0,i(76295))),b=new o.TranslatedString("change {title} show pearson's r",n.t(null,void 0,i(6207))),D=n.t(null,void 0,i(42398)),w=n.t(null,void 0,i(22691)),T=n.t(null,void 0,i(71776)),I=n.t(null,void 0,i(13350)),V=n.t(null,void 0,i(819));class C extends s.StudyLineDataSourceDefinitionsViewModel{constructor(e,t){super(e,t)}_stylePropertyDefinitions(){const e=this._source.properties().childs().styles.childs(),t=this._source.name(),i=(0,l.removeSpaces)(t),n=new o.TranslatedString(t,this._source.translatedType()),s=e.baseLine.childs(),C=(0,r.createLinePropertyDefinition)({checked:(0,r.convertToDefinitionProperty)(this._propertyApplier,new a.StudyPlotVisibleProperty(s.display),u.format({title:n})),color:(0,r.getColorDefinitionProperty)(this._propertyApplier,s.color,e.transparency,p.format({title:n})),width:(0,r.convertToDefinitionProperty)(this._propertyApplier,s.linewidth,c.format({title:n})),style:(0,r.convertToDefinitionProperty)(this._propertyApplier,s.linestyle,d.format({title:n}))},{id:`${i}BaseLine`,title:D}),L=e.upLine.childs(),M=(0,r.createLinePropertyDefinition)({checked:(0,r.convertToDefinitionProperty)(this._propertyApplier,new a.StudyPlotVisibleProperty(L.display),y.format({title:n})),color:(0,r.getColorDefinitionProperty)(this._propertyApplier,L.color,e.transparency,h.format({title:n})),width:(0,r.convertToDefinitionProperty)(this._propertyApplier,L.linewidth,f.format({title:n})),style:(0,r.convertToDefinitionProperty)(this._propertyApplier,L.linestyle,v.format({title:n}))},{id:`${i}UpLine`,title:w}),k=e.downLine.childs();return{definitions:[C,M,(0,r.createLinePropertyDefinition)({checked:(0,
r.convertToDefinitionProperty)(this._propertyApplier,new a.StudyPlotVisibleProperty(k.display),_.format({title:n})),color:(0,r.getColorDefinitionProperty)(this._propertyApplier,k.color,e.transparency,S.format({title:n})),width:(0,r.convertToDefinitionProperty)(this._propertyApplier,k.linewidth,m.format({title:n})),style:(0,r.convertToDefinitionProperty)(this._propertyApplier,k.linestyle,P.format({title:n}))},{id:`${i}DownLine`,title:T}),(0,r.createCheckablePropertyDefinition)({checked:(0,r.convertToDefinitionProperty)(this._propertyApplier,e.extendLines,g.format({title:n}))},{id:`${i}ExtendLines`,title:V}),(0,r.createCheckablePropertyDefinition)({checked:(0,r.convertToDefinitionProperty)(this._propertyApplier,e.showPearsons,b.format({title:n}))},{id:`${i}Pearsons`,title:I})]}}}},15306:(e,t,i)=>{i.r(t),i.d(t,{StudyOverlayDefinitionsViewModel:()=>U});var n=i(11542),o=i(45126),r=i(56570),s=i(50151),l=(i(21251),i(4116)),a=i(57717),u=i(65383),p=i(64147),c=i(49406);const d=new o.TranslatedString("change {inputName} property",n.t(null,void 0,i(66110)));function y(e,t){const i=e.id;return i!==u.RangeDependentStudyInputNames.FirstBar&&i!==u.RangeDependentStudyInputNames.LastBar&&("time"!==e.type&&(!e.isHidden&&(!(t&&!e.confirm)&&void 0===e.groupId)))}function h(e){return e.name||(0,c.capitalizeFirstLetterInWord)(e.id.toLowerCase())}function f(e){return n.t(e,{context:"input"},i(32856))}var v,_=i(68159),S=i(10074),m=i(26434),P=i(19466),g=i(74343);!function(e){e.Style="style",e.Inputs="inputs",e.Visibility="visibility"}(v||(v={}));const b=n.t(null,void 0,i(92516)),D=n.t(null,void 0,i(21429)),w=n.t(null,void 0,i(40091)),T=["1","3","5","15","30","45","60","120","180","240","1D","1W","1M"].map((e=>({value:e,title:(0,S.getTranslatedResolutionModel)(e).hint})));var I=i(18653),V=i(64482),C=i(68805);const L=new o.TranslatedString("change study overlay style",n.t(null,void 0,i(82924))),M=new o.TranslatedString("change price line visibility",n.t(null,void 0,i(8662))),k=new o.TranslatedString("change study overlay min tick",n.t(null,void 0,i(91109))),A=n.t(null,void 0,i(27377)),N=n.t(null,void 0,i(45054)),W=n.t(null,void 0,i(13459)),x=n.t(null,void 0,i(55761)),B=n.t(null,void 0,i(3554)),O=n.t(null,void 0,i(34456)),$=n.t(null,void 0,i(99906)),R=n.t(null,void 0,i(59213)),E=n.t(null,void 0,i(98236)),F=n.t(null,void 0,i(9394)),G=n.t(null,void 0,i(69217)),j=n.t(null,void 0,i(92516)),H=n.t(null,void 0,i(72926)),z=n.t(null,void 0,i(64075)),J=[{title:A,value:0},{title:N,value:1},{title:W,value:9},{title:x,value:13},{title:B,value:2},{title:F,value:14},{title:G,value:15},{title:O,value:3},{title:$,value:16},{title:R,value:10}];r.enabled("chart_style_hilo")&&J.push({title:E,value:12});class U extends class{constructor(e,t){this._inputSourceItems=null,this._propertyPages=[],this._sourceInput=null,this._source=t,this._undoModel=e;const i=this._sortInputs(this._source.metaInfo().inputs);for(const e of i)"source"===e.type&&(this._sourceInput=e);this._createPropertyRages(),
null!==this._inputSourceItems&&this._undoModel.model().dataSourceCollectionChanged().subscribe(this,(()=>{null!==this._inputSourceItems&&this._inputSourceItems.setValue(this._getInputSourceItems())}))}destroy(){null!==this._inputSourceItems&&this._undoModel.model().dataSourceCollectionChanged().unsubscribeAll(this),this._propertyPages.forEach((e=>{(0,l.destroyDefinitions)(e.definitions.value())}))}propertyPages(){return Promise.resolve(this._propertyPages)}_createPropertyRages(){this._propertyPages=[];const e=this._createInputsPropertyPage();null!==e&&this._propertyPages.push(e);const t=this._createStylePropertyPage();null!==t&&this._propertyPages.push(t),this._propertyPages.push(this._createVisibilitiesPropertyPage())}_createStylePropertyPage(){const e=this._stylePropertyDefinitions();return null!==e?(0,a.createPropertyPage)(e,"style",b):null}_createVisibilitiesPropertyPage(){const e=this._source.properties().childs().intervalsVisibilities.childs();return(0,a.createPropertyPage)((0,m.getIntervalsVisibilitiesPropertiesDefinitions)(this._undoModel,e,new o.TranslatedString(this._source.name(!0),this._source.title(P.TitleDisplayTarget.StatusLine,!0))),"visibility",w)}_stylePropertyDefinitions(){return null}_createInputsPropertyPage(){const e=this._inputsPropertyDefinitions();return null!==e?(0,a.createPropertyPage)(e,"inputs",D):null}_inputsPropertyDefinitions(){const e=this._sortInputs(this._source.metaInfo().inputs),t=this._source.properties().childs().inputs.childs();return null!==this._sourceInput&&(this._inputSourceItems=new p.WatchedValue(this._getInputSourceItems())),function(e,t,r,a,u){const c=[];for(const v of t){if(!y(v,a))continue;const t=h(v),_=`StudyInput${v.id}`,S=f(t),m=new o.TranslatedString(t,S);let P=null;if("resolution"===v.type)P=(0,l.createOptionsPropertyDefinition)({option:(0,l.convertToDefinitionProperty)(e,r[v.id],d.format({inputName:m}))},{id:_,title:S,options:new p.WatchedValue(u.resolutionItems)});else if("source"===v.type){const t=(0,s.ensure)(u.sourcesItems);P=(0,l.createOptionsPropertyDefinition)({option:(0,l.convertToDefinitionProperty)(e,r[v.id],d.format({inputName:m}))},{id:_,title:S,options:t})}else if("options"in v&&void 0!==v.options){const t=[];for(const e of v.options){const o=v.optionsTitles&&v.optionsTitles[e]||e,r=n.t(o,{context:"input"},i(32856));t.push({value:e,title:r})}P=(0,l.createOptionsPropertyDefinition)({option:(0,l.convertToDefinitionProperty)(e,r[v.id],d.format({inputName:m}))},{id:_,title:S,options:new p.WatchedValue(t)})}else if("symbol"===v.type){const t=r[v.id],i=(0,s.ensure)(u.getSymbolInfoBySymbol),n=(0,s.ensure)(u.onSymbolsInfosChanged);P=(0,l.createSymbolPropertyDefinition)({symbol:(0,l.getSymbolDefinitionProperty)(e,t,i,n,d.format({inputName:m}),u.customSymbolInputSetter)},{id:_,title:S})}else if("session"===v.type)P=(0,l.createSessionPropertyDefinition)({session:(0,l.convertToDefinitionProperty)(e,r[v.id],d.format({inputName:m}))},{id:_,title:S});else if("bool"===v.type)P=(0,l.createCheckablePropertyDefinition)({checked:(0,
l.convertToDefinitionProperty)(e,r[v.id],d.format({inputName:m}))},{id:_,title:S});else if("integer"===v.type||"float"===v.type||"price"===v.type){const t={id:_,title:S,type:"float"===v.type||"price"===v.type?1:0,defval:v.defval};void 0!==v.min&&(t.min=new p.WatchedValue(v.min)),void 0!==v.max&&(t.max=new p.WatchedValue(v.max)),void 0!==v.step&&isFinite(v.step)&&v.step>0&&(t.step=new p.WatchedValue(v.step)),P=(0,l.createNumberPropertyDefinition)({value:(0,l.convertToDefinitionProperty)(e,r[v.id],d.format({inputName:m}))},t)}else P=(0,l.createTextPropertyDefinition)({text:(0,l.convertToDefinitionProperty)(e,r[v.id],d.format({inputName:m}))},{id:_,title:S,isEditable:!0,isMultiLine:!1});c.push(P)}return 0===c.length?null:{definitions:c}}(this._undoModel,e,t,!1,{resolutionItems:T,customSymbolInputSetter:this._customSymbolInputSetter(),getSymbolInfoBySymbol:this._getSymbolInfoBySymbol.bind(this),onSymbolsInfosChanged:this._source.symbolsResolved(),sourcesItems:this._inputSourceItems})}_sortInputs(e){return e}_getInputSourceItems(){const e=g.basePriceSources.slice(),t=(0,s.ensureNotNull)(this._sourceInput);if(this._source&&this._source.isChildStudy()){const i=this._source.inputs()[t.id],n=(0,s.ensureNotNull)(this._source.parentSourceForInput(i.v)),o=n.title(P.TitleDisplayTarget.StatusLine),r=_.StudyMetaInfo.getChildSourceInputTitles(t,n.metaInfo(),o);for(const t of Object.keys(r))e.push({id:t,value:t,title:r[t]})}if(r.enabled("study_on_study")&&this._source&&(this._source.isChildStudy()||_.StudyMetaInfo.canBeChild(this._source.metaInfo()))){const t=new Set([this._source,...this._source.getAllChildren()]);this._undoModel.model().allStudies().filter((e=>e.canHaveChildren()&&!t.has(e))).forEach((t=>{const i=t.title(P.TitleDisplayTarget.StatusLine,!0,void 0,!0),n=t.sourceId()||"#"+t.id(),o=t.metaInfo(),r=o.styles,l=o.plots||[];if(1===l.length)e.push({id:n,value:n,title:i});else if(l.length>1){const t=l.reduce(((e,t,o)=>{if(!_.StudyMetaInfo.canPlotBeSourceOfChildStudy(t.type))return e;let l;try{l=(0,s.ensureDefined)((0,s.ensureDefined)(r)[t.id]).title}catch(e){l=t.id}return{...e,[`${n}$${o}`]:`${i}: ${l}`}}),{});for(const i of Object.keys(t))e.push({id:i,value:i,title:t[i]})}}))}return e}_customSymbolInputSetter(){}_getSymbolInfoBySymbol(e){return this._source.resolvedSymbolInfoBySymbol(e.value())}}{constructor(e,t){super(e,t),this._stylesPropertyPage=null,this.propertyPages().then((e=>{this._stylesPropertyPage=e.filter((e=>"style"===e.id))[0]})),this._source.properties().childs().style.subscribe(this,(e=>{var t;null!==this._stylesPropertyPage&&((0,l.destroyDefinitions)(this._stylesPropertyPage.definitions.value()),this._stylesPropertyPage.definitions.setValue(this._stylePropertyDefinitions().definitions)),null===(t=this._availableStylesWV)||void 0===t||t.setValue(this._availableStyles())}))}destroy(){this._source.properties().childs().style.unsubscribeAll(this),this._source.symbolResolved().unsubscribeAll(this),super.destroy()}_customSymbolInputSetter(){return e=>{this._undoModel.setSymbol(this._source,e)}}
_stylePropertyDefinitions(){void 0===this._availableStylesWV&&(this._availableStylesWV=new p.WatchedValue(this._availableStyles()),this._source.symbolResolved().subscribe(this,(()=>{var e;null===(e=this._availableStylesWV)||void 0===e||e.setValue(this._availableStyles())})));const e=this._source.properties().childs(),t=(0,l.createOptionsPropertyDefinition)({option:(0,l.convertToDefinitionProperty)(this._undoModel,e.style,L)},{id:"StudyOverlayStyle",title:j,options:this._availableStylesWV}),i=(0,l.createCheckablePropertyDefinition)({checked:(0,l.convertToDefinitionProperty)(this._undoModel,e.showPriceLine,M)},{id:"StudyOverlayPriceLine",title:H}),n=(0,l.createOptionsPropertyDefinition)({option:(0,l.convertToDefinitionProperty)(this._undoModel,e.minTick,k)},{id:"StudyOverlayMinTick",title:z,options:new p.WatchedValue((0,V.seriesPrecisionValues)())}),o=(0,c.removeSpaces)(this._source.title(P.TitleDisplayTarget.StatusLine));return{definitions:[(0,l.createPropertyDefinitionsGeneralGroup)([t,...this._getSeriesStylesDefinitions()],`SeriesStyleGroup${o}`),i,n]}}_getSeriesStylesDefinitions(){const e=this._source.properties().childs(),t=e.style.value();return(0,I.getSeriesStylePropertiesDefinitions)(this._undoModel,e,t,{seriesPriceSources:g.basePriceSources,isJapaneseChartsAvailable:!1},"mainSeries")}_availableStyles(){const e=this._source.symbolInfo();return J.map((t=>t.readonly?t:{readonly:!1,value:t.value,title:t.title,disabled:(0,C.isCloseBasedSymbol)(e)&&!(0,C.isSingleValueBasedStyle)(t.value)}))}}}}]);