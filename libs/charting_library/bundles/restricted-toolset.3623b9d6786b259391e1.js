(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[5516,9685],{83072:e=>{e.exports={small:"small-CtnpmPzP",medium:"medium-CtnpmPzP",large:"large-CtnpmPzP",switchView:"switchView-CtnpmPzP",checked:"checked-CtnpmPzP",track:"track-CtnpmPzP",disabled:"disabled-CtnpmPzP",thumb:"thumb-CtnpmPzP"}},12006:e=>{e.exports={switcher:"switcher-fwE97QDf",input:"input-fwE97QDf",thumbWrapper:"thumbWrapper-fwE97QDf",activeStylesEnabled:"activeStylesEnabled-fwE97QDf",select:"select-fwE97QDf"}},53330:e=>{e.exports={"tablet-normal-breakpoint":"screen and (max-width: 768px)","small-height-breakpoint":"screen and (max-height: 360px)","tablet-small-breakpoint":"screen and (max-width: 430px)"}},90083:e=>{e.exports={footer:"footer-dwINHZFL"}},98992:e=>{e.exports={favorite:"favorite-_FRQhM5Y",hovered:"hovered-_FRQhM5Y",disabled:"disabled-_FRQhM5Y",active:"active-_FRQhM5Y",checked:"checked-_FRQhM5Y"}},69961:e=>{e.exports={item:"item-zwyEh4hn",label:"label-zwyEh4hn",labelRow:"labelRow-zwyEh4hn",toolbox:"toolbox-zwyEh4hn"}},11772:e=>{e.exports={button:"button-iLKiGOdQ",hovered:"hovered-iLKiGOdQ",disabled:"disabled-iLKiGOdQ",active:"active-iLKiGOdQ",hidden:"hidden-iLKiGOdQ"}},17946:(e,t,a)=>{"use strict";a.d(t,{CustomBehaviourContext:()=>i});const i=(0,a(50959).createContext)({enableActiveStateStyles:!0});i.displayName="CustomBehaviourContext"},125:(e,t,a)=>{"use strict";a.d(t,{useForceUpdate:()=>n});var i=a(50959);const n=()=>{const[,e]=(0,i.useReducer)((e=>e+1),0);return e}},76974:(e,t,a)=>{"use strict";a.d(t,{useIsMounted:()=>n});var i=a(50959);const n=()=>{const e=(0,i.useRef)(!1);return(0,i.useEffect)((()=>(e.current=!0,()=>{e.current=!1})),[]),e}},24437:(e,t,a)=>{"use strict";a.d(t,{DialogBreakpoints:()=>n});var i=a(53330);const n={SmallHeight:i["small-height-breakpoint"],TabletSmall:i["tablet-small-breakpoint"],TabletNormal:i["tablet-normal-breakpoint"]}},39362:(e,t,a)=>{"use strict";a.d(t,{SymbolSearchDialogFooter:()=>l});var i=a(50959),n=a(97754),o=a.n(n),s=a(90083);function l(e){const{className:t,children:a}=e;return i.createElement("div",{className:o()(s.footer,t)},a)}},36189:(e,t,a)=>{"use strict";a.d(t,{FavoriteButton:()=>d});var i=a(11542),n=a(50959),o=a(97754),s=a(9745),l=a(39146),r=a(48010),h=a(98992);const c={add:i.t(null,void 0,a(69207)),remove:i.t(null,void 0,a(85106))};function d(e){const{className:t,isFilled:a,isActive:i,onClick:d,...u}=e;return n.createElement(s.Icon,{...u,className:o(h.favorite,"apply-common-tooltip",a&&h.checked,i&&h.active,t),icon:a?l:r,onClick:d,title:a?c.remove:c.add})}},36947:(e,t,a)=>{"use strict";a.d(t,{useForceUpdate:()=>i.useForceUpdate});var i=a(125)},70412:(e,t,a)=>{"use strict";a.d(t,{hoverMouseEventFilter:()=>o,useAccurateHover:()=>s,useHover:()=>n});var i=a(50959);function n(){const[e,t]=(0,i.useState)(!1);return[e,{onMouseOver:function(e){o(e)&&t(!0)},onMouseOut:function(e){o(e)&&t(!1)}}]}function o(e){return!e.currentTarget.contains(e.relatedTarget)}function s(e){const[t,a]=(0,i.useState)(!1);return(0,i.useEffect)((()=>{const t=t=>{if(null===e.current)return
;const i=e.current.contains(t.target);a(i)};return document.addEventListener("mouseover",t),()=>document.removeEventListener("mouseover",t)}),[]),t}},81332:(e,t,a)=>{"use strict";a.d(t,{multilineLabelWithIconAndToolboxTheme:()=>s});var i=a(40173),n=a(2908),o=a(69961);const s=(0,i.mergeThemes)(n,o)},96040:(e,t,a)=>{"use strict";a.d(t,{RemoveButton:()=>h});var i=a(11542),n=a(50959),o=a(97754),s=a(9745),l=a(33765),r=a(11772);function h(e){const{className:t,isActive:h,onClick:c,onMouseDown:d,title:u,hidden:v,"data-name":m="remove-button",icon:p,...g}=e;return n.createElement(s.Icon,{...g,"data-name":m,className:o(r.button,"apply-common-tooltip",h&&r.active,v&&r.hidden,t),icon:p||l,onClick:c,onMouseDown:d,title:u||i.t(null,void 0,a(67410))})}},48967:e=>{e.exports={summary:"summary-ynHBVe1n",hovered:"hovered-ynHBVe1n",caret:"caret-ynHBVe1n"}},97169:e=>{e.exports={item:"item-KMkDzD5K",accessible:"accessible-KMkDzD5K",round:"round-KMkDzD5K",active:"active-KMkDzD5K"}},67505:e=>{e.exports={accessible:"accessible-raQdxQp0"}},88259:e=>{e.exports={button:"button-LkmyTVRc",active:"active-LkmyTVRc"}},20807:e=>{e.exports={wrapper:"wrapper-psOC5oyI",labelRow:"labelRow-psOC5oyI",label:"label-psOC5oyI",labelHint:"labelHint-psOC5oyI",labelOn:"labelOn-psOC5oyI"}},73546:e=>{e.exports={wrapper:"wrapper-bl9AR3Gv",hovered:"hovered-bl9AR3Gv",switchWrap:"switchWrap-bl9AR3Gv",withIcon:"withIcon-bl9AR3Gv",labelRow:"labelRow-bl9AR3Gv",label:"label-bl9AR3Gv",icon:"icon-bl9AR3Gv",labelHint:"labelHint-bl9AR3Gv",labelOn:"labelOn-bl9AR3Gv",accessible:"accessible-bl9AR3Gv"}},15965:e=>{e.exports={button:"button-Y1TCZogJ",active:"active-Y1TCZogJ"}},16135:e=>{e.exports={button:"button-ptpAHg8E",withText:"withText-ptpAHg8E",withoutText:"withoutText-ptpAHg8E"}},90531:e=>{e.exports={spinnerWrap:"spinnerWrap-cZT0OZe0"}},6993:e=>{e.exports={button:"button-neROVfUe",first:"first-neROVfUe",last:"last-neROVfUe"}},21411:e=>{e.exports={wrap:"wrap-n5bmFxyX"}},36531:e=>{e.exports={hidden:"hidden-5MVS18J8"}},22604:e=>{e.exports={"tablet-small-breakpoint":"screen and (max-width: 430px)",item:"item-o5a0MQMm",withIcon:"withIcon-o5a0MQMm",shortcut:"shortcut-o5a0MQMm",loading:"loading-o5a0MQMm",icon:"icon-o5a0MQMm"}},97060:e=>{e.exports={button:"button-b3Cgff6l",group:"group-b3Cgff6l",menu:"menu-b3Cgff6l",betaBadge:"betaBadge-b3Cgff6l",newBadge:"newBadge-b3Cgff6l",label:"label-b3Cgff6l"}},39351:e=>{e.exports={customTradingViewStyleButton:"customTradingViewStyleButton-zigjK1n2",withoutIcon:"withoutIcon-zigjK1n2"}},10047:e=>{e.exports={dropdown:"dropdown-l0nf43ai",label:"label-l0nf43ai",smallWidthTitle:"smallWidthTitle-l0nf43ai",smallWidthMenuItem:"smallWidthMenuItem-l0nf43ai",smallWidthWrapper:"smallWidthWrapper-l0nf43ai"}},53658:e=>{e.exports={value:"value-gwXludjS",selected:"selected-gwXludjS"}},64347:e=>{e.exports={smallWidthMenuItem:"smallWidthMenuItem-RmqZNwwp",menuItem:"menuItem-RmqZNwwp",remove:"remove-RmqZNwwp",signal:"signal-RmqZNwwp","highlight-animation":"highlight-animation-RmqZNwwp"}},69397:e=>{e.exports={button:"button-S_1OCXUK",first:"first-S_1OCXUK",
last:"last-S_1OCXUK",menu:"menu-S_1OCXUK",dropdown:"dropdown-S_1OCXUK",menuContent:"menuContent-S_1OCXUK",section:"section-S_1OCXUK",smallTabletSectionTitle:"smallTabletSectionTitle-S_1OCXUK",addCustomInterval:"addCustomInterval-S_1OCXUK",desktop:"desktop-S_1OCXUK",group:"group-S_1OCXUK"}},66494:e=>{e.exports={button:"button-gn9HMufu"}},64212:e=>{e.exports={button:"button-ZuDkGGhF",isDisabled:"isDisabled-ZuDkGGhF"}},97807:e=>{e.exports={saveString:"saveString-XVd1Kfjg",hidden:"hidden-XVd1Kfjg",loader:"loader-XVd1Kfjg"}},40842:e=>{e.exports={menuBtnWrap:"menuBtnWrap-yyMUOAN9",menu:"menu-yyMUOAN9",hintPlaceholder:"hintPlaceholder-yyMUOAN9",hintWrapper:"hintWrapper-yyMUOAN9",hintText:"hintText-yyMUOAN9",hintButton:"hintButton-yyMUOAN9",hintButtons:"hintButtons-yyMUOAN9",hintAdditionalButton:"hintAdditionalButton-yyMUOAN9",opened:"opened-yyMUOAN9",hover:"hover-yyMUOAN9",clicked:"clicked-yyMUOAN9",autoSaveWrapper:"autoSaveWrapper-yyMUOAN9",sharingWrapper:"sharingWrapper-yyMUOAN9",button:"button-yyMUOAN9",buttonSmallPadding:"buttonSmallPadding-yyMUOAN9",hintPlaceHolder:"hintPlaceHolder-yyMUOAN9",smallHintPlaceHolder:"smallHintPlaceHolder-yyMUOAN9",popupItemRowTabletSmall:"popupItemRowTabletSmall-yyMUOAN9",shortcut:"shortcut-yyMUOAN9",toolTitle:"toolTitle-yyMUOAN9",toolTitleMobile:"toolTitleMobile-yyMUOAN9",layoutItem:"layoutItem-yyMUOAN9",layoutMeta:"layoutMeta-yyMUOAN9",toolbox:"toolbox-yyMUOAN9",toolboxSmall:"toolboxSmall-yyMUOAN9",layoutTitle:"layoutTitle-yyMUOAN9",layoutItemWrap:"layoutItemWrap-yyMUOAN9",layoutItemWrapSmall:"layoutItemWrapSmall-yyMUOAN9",layoutTitleSmall:"layoutTitleSmall-yyMUOAN9",active:"active-yyMUOAN9",textWrap:"textWrap-yyMUOAN9",text:"text-yyMUOAN9",withIcon:"withIcon-yyMUOAN9",sharingLabelWrap:"sharingLabelWrap-yyMUOAN9",titleSharingLabel:"titleSharingLabel-yyMUOAN9",switcherLabel:"switcherLabel-yyMUOAN9",iconWrap:"iconWrap-yyMUOAN9",infoIcon:"infoIcon-yyMUOAN9",copyLink:"copyLink-yyMUOAN9",copyLinkMobile:"copyLinkMobile-yyMUOAN9",accessibleLabel:"accessibleLabel-yyMUOAN9"}},30399:e=>{e.exports={button:"button-cq__ntSC",smallLeftPadding:"smallLeftPadding-cq__ntSC",text:"text-cq__ntSC",uppercase:"uppercase-cq__ntSC"}},89452:e=>{e.exports={description:"description-jgoQcEnP"}},82574:e=>{e.exports={wrap:"wrap-HXSqojvq",titleWrap:"titleWrap-HXSqojvq",indicators:"indicators-HXSqojvq",title:"title-HXSqojvq",icon:"icon-HXSqojvq",text:"text-HXSqojvq",titleTabletSmall:"titleTabletSmall-HXSqojvq",labelRow:"labelRow-HXSqojvq",label:"label-HXSqojvq"}},37926:e=>{e.exports={labelRow:"labelRow-JeQoCpvi",toolbox:"toolbox-JeQoCpvi",description:"description-JeQoCpvi",descriptionTabletSmall:"descriptionTabletSmall-JeQoCpvi",item:"item-JeQoCpvi",titleItem:"titleItem-JeQoCpvi",remove:"remove-JeQoCpvi",titleItemTabletSmall:"titleItemTabletSmall-JeQoCpvi",itemTabletSmall:"itemTabletSmall-JeQoCpvi",itemLabelTabletSmall:"itemLabelTabletSmall-JeQoCpvi",wrap:"wrap-JeQoCpvi",hovered:"hovered-JeQoCpvi"}},10222:e=>{e.exports={menu:"menu-hcofKPms",menuSmallTablet:"menuSmallTablet-hcofKPms",
menuItemHeaderTabletSmall:"menuItemHeaderTabletSmall-hcofKPms",menuItemHeader:"menuItemHeader-hcofKPms"}},71232:e=>{e.exports={wrap:"wrap-jiC5bgmi",full:"full-jiC5bgmi",first:"first-jiC5bgmi",last:"last-jiC5bgmi",medium:"medium-jiC5bgmi",buttonWithFavorites:"buttonWithFavorites-jiC5bgmi"}},4417:e=>{e.exports={icon:"icon-uMfL97K2"}},39706:(e,t,a)=>{"use strict";a.d(t,{CollapsibleSection:()=>r});var i=a(50959),n=a(97754),o=a.n(n),s=a(10381),l=a(48967);const r=(0,i.forwardRef)((function(e,t){const{open:a,summary:n,children:r,onStateChange:h,tabIndex:c,className:d,...u}=e;return i.createElement(i.Fragment,null,i.createElement("div",{...u,className:o()(d,l.summary),onClick:function(){h&&h(!a)},"data-open":a,ref:t,tabIndex:c},n,i.createElement(s.ToolWidgetCaret,{className:l.caret,dropped:Boolean(a)})),a&&r)}))},40894:(e,t,a)=>{"use strict";a.d(t,{MenuFavoriteButton:()=>h});var i=a(50959),n=a(97754),o=a.n(n),s=a(50238),l=a(36189),r=a(88259);function h(e){const{tooltip:t,onClick:a,...n}=e,[h,c]=(0,s.useRovingTabindexElement)(null);return i.createElement("button",{ref:h,tabIndex:c,onClick:a,className:o()(r.button,n.isActive&&r.active),type:"button"},i.createElement(l.FavoriteButton,{"aria-label":t,...n,"data-tooltip":t}))}},10428:(e,t,a)=>{"use strict";a.d(t,{DEFAULT_MENU_ITEM_SWITCHER_THEME:()=>S,MenuItemSwitcher:()=>_});var i,n=a(50959),o=a(97754),s=a.n(o),l=a(17946),r=a(83072);function h(e){const{size:t="small",checked:a,disabled:i}=e;return n.createElement("span",{className:s()(r.switchView,r[t],i&&r.disabled,a&&r.checked)},n.createElement("span",{className:r.track}),n.createElement("span",{className:r.thumb}))}!function(e){e.Small="small",e.Medium="medium",e.Large="large"}(i||(i={}));var c,d=a(3343),u=a(12006),v=a.n(u);function m(e){const t=(0,n.useContext)(l.CustomBehaviourContext),{size:a,intent:i="default",checked:s,className:r,enableActiveStateStyles:c=t.enableActiveStateStyles,disabled:u,onChange:m,title:p,id:g,name:b,value:C,tabIndex:S,role:_="switch",ariaDisabled:y,reference:w,ariaLabelledBy:f,ariaLabel:k,...M}=e;return n.createElement("span",{className:o(r,s&&v().checked,v().switcher)},n.createElement("input",{...M,type:"checkbox",className:o(v().input,c&&v().activeStylesEnabled),role:_,"aria-checked":s,checked:s,onKeyDown:e=>{var t;13===(0,d.hashFromEvent)(e)&&(null===(t=e.currentTarget)||void 0===t||t.click())},onChange:m,disabled:u,"aria-disabled":y,title:p,id:g,name:b,value:C,ref:w,"aria-label":k,"aria-labelledby":f}),n.createElement("span",{className:o(v().thumbWrapper,v()[i])},n.createElement(h,{checked:s,size:a,disabled:u})))}!function(e){e.Default="default",e.Select="select"}(c||(c={}));var p=a(9745),g=a(50238),b=a(90186),C=a(73546);const S=C;function _(e){const{className:t,checked:a,id:i,label:o,labelDescription:l,value:r,preventLabelHighlight:h,reference:c,switchReference:u,theme:v=C,disabled:S,icon:_}=e,[y,w]=(0,g.useRovingTabindexElement)(null),f=s()(v.label,a&&!h&&v.labelOn),k=s()(t,v.wrapper,a&&v.wrapperWithOnLabel,l&&v.wrapperWithDescription);return n.createElement("label",{
className:s()(k,_&&v.withIcon,C.accessible),htmlFor:i,ref:c,onKeyDown:function(e){if(e.target!==e.currentTarget)return;const t=(0,d.hashFromEvent)(e);13!==t&&32!==t||(e.preventDefault(),y.current instanceof HTMLElement&&y.current.click())},tabIndex:w,"data-role":"menuitem","aria-disabled":e.disabled||void 0},void 0!==_&&n.createElement(p.Icon,{className:v.icon,icon:_}),n.createElement("div",{className:v.labelRow},n.createElement("div",{className:f},o),l&&n.createElement("div",{className:v.labelHint},l)),n.createElement("div",{className:C.switchWrap},n.createElement(m,{disabled:S,className:v.switch,reference:function(e){y(e),null==u||u(e)},checked:a,onChange:function(t){const a=t.target.checked;void 0!==e.onChange&&e.onChange(a)},value:r,tabIndex:-1,id:i,role:e.switchRole,ariaDisabled:!0,...(0,b.filterDataProps)(e)})))}},38068:(e,t,a)=>{"use strict";a.d(t,{MenuRemoveButton:()=>c});var i=a(50959),n=a(97754),o=a.n(n),s=a(50238),l=a(96040),r=a(60925),h=a(15965);function c(e){const{tooltip:t,onClick:a,...n}=e,[c,d]=(0,s.useRovingTabindexElement)(null);return i.createElement("button",{ref:c,tabIndex:d,onClick:a,className:o()(h.button,n.isActive&&h.active),type:"button"},i.createElement(l.RemoveButton,{"aria-label":t,...n,"data-tooltip":t,icon:r}))}},34009:(e,t,a)=>{"use strict";a.r(t),a.d(t,{getRestrictedToolSet:()=>oi});var i=a(56570),n=a(50959),o=a(19036),s=a(9745),l=a(11542),r=a(82992),h=a(5171),c=a(23076),d=a(88811),u=a(97754),v=a.n(u),m=a(21411);const p=n.forwardRef(((e,t)=>{const{children:a,className:i,...o}=e;return n.createElement("div",{className:u(i,m.wrap),ref:t,...o},a)}));var g=a(20792),b=a(6993);class C extends n.PureComponent{constructor(){super(...arguments),this._handleClick=()=>{const{onClick:e,onClickArg:t}=this.props;e&&e(t)}}render(){const{className:e,icon:t,hint:a,text:i,isDisabled:o,isActive:s,isFirst:l,isLast:r,onClick:h,onClickArg:c,...d}=this.props;return n.createElement(g.ToolbarButton,{...d,icon:t,text:i,tooltip:a,isDisabled:o,isActive:s,isGrouped:!0,onClick:this._handleClick,className:u(e,b.button,{[b.first]:l,[b.last]:r})})}}var S=a(11684),_=a(90692),y=a(24437),w=a(81332),f=a(77151),k=a(42989),M=a(68805),E=a(47201),x=a(3343),T=a(19291);function I(e){const{orientation:t,onKeyDown:a,...i}=e;return n.createElement("div",{...i,role:"radiogroup","aria-orientation":t,onKeyDown:(0,E.createSafeMulticastEventHandler)((function(e){if(e.defaultPrevented)return;if(!(document.activeElement instanceof HTMLElement))return;const a=(0,x.hashFromEvent)(e);if("vertical"!==t&&38!==a&&40!==a)return;if("vertical"===t&&37!==a&&39!==a)return;const i=(n=e.currentTarget,Array.from(n.querySelectorAll('[role="radio"]:not([disabled]):not([aria-disabled])')).filter((0,T.createScopedVisibleElementFilter)(n))).sort(T.navigationOrderComparator);var n;if(0===i.length)return;const o=i.indexOf(document.activeElement);if(-1===o)return;e.preventDefault();const s=()=>{const e=(o+i.length-1)%i.length;i[o].dispatchEvent(new CustomEvent("roving-tabindex:secondary-element")),
i[e].dispatchEvent(new CustomEvent("roving-tabindex:main-element")),i[e].focus()},l=()=>{const e=(o+i.length+1)%i.length;i[o].dispatchEvent(new CustomEvent("roving-tabindex:secondary-element")),i[e].dispatchEvent(new CustomEvent("roving-tabindex:main-element")),i[e].focus()};switch(a){case 38:"vertical"!==t&&s();break;case 40:"vertical"!==t&&l();break;case 37:"vertical"===t&&s();break;case 39:"vertical"===t&&l()}}),a)})}var R=a(10838),A=a(40894),N=a(97060);const F=(0,f.registryContextType)();function H(e){var t;return!(null===(t=r.linking.supportedChartStyles.value())||void 0===t?void 0:t.includes(e))}const O="ITEMS_DIVIDER",U=[[0,1,9,19],[2,14,15],[3,16,10],[13,12],[17,18,20],[8,4,7,5,6,11]];new Set([17,18,19,20]),new Set([]);class D extends n.PureComponent{constructor(e,t){super(e,t),this._handleChangeStyle=e=>{const{favorites:t,lastSelectedNotFavorite:a,activeStyle:i}=this.state;this.setState({activeStyle:e,lastSelectedNotFavorite:t.includes(i)?a:i})},this._handleSelectStyle=e=>{const{chartWidgetCollection:t}=this.context;e!==t.activeChartStyle.value()&&t.setChartStyleToWidget(e)},this._handleClickFavorite=e=>{this._isStyleFavorited(e)?this._handleRemoveFavorite(e):this._handleAddFavorite(e)},this._boundForceUpdate=()=>{this.forceUpdate()},this._handleQuickClick=e=>{this._handleSelectStyle(e),this._trackClick()},(0,f.validateRegistry)(t,{chartWidgetCollection:o.any.isRequired,favoriteChartStylesService:o.any.isRequired});const{chartWidgetCollection:a,favoriteChartStylesService:i}=t,n=a.activeChartStyle.value(),s=i.get(),l=new Set((0,k.allChartStyles)());this.state={activeStyle:n,favorites:s,styles:U.reduce(((e,t)=>{const a=t.filter((e=>l.has(e)));return a.length&&(e.length&&a.unshift(O),e.push(...a)),e}),[])}}componentDidMount(){const{chartWidgetCollection:e,favoriteChartStylesService:t}=this.context;e.activeChartStyle.subscribe(this._handleChangeStyle),t.getOnChange().subscribe(this,this._handleChangeSettings),r.linking.supportedChartStyles.subscribe(this._boundForceUpdate)}componentWillUnmount(){const{chartWidgetCollection:e,favoriteChartStylesService:t}=this.context;e.activeChartStyle.unsubscribe(this._handleChangeStyle),t.getOnChange().unsubscribe(this,this._handleChangeSettings),r.linking.supportedChartStyles.unsubscribe(this._boundForceUpdate)}render(){const{isShownQuicks:e,displayMode:t="full",id:i}=this.props,{activeStyle:o,favorites:r,styles:u,lastSelectedNotFavorite:v}=this.state,m="small"!==t&&e&&0!==r.length,g=[...r];g.includes(o)?void 0!==v&&g.push(v):g.push(o);const b=m&&g.length>1;return n.createElement(_.MatchMedia,{rule:y.DialogBreakpoints.TabletSmall},(e=>{const t=u.map(((t,a)=>t===O?n.createElement(S.PopupMenuSeparator,{key:`separator-${a}`}):this._renderPopupMenuItem(t,t===o,e)));return n.createElement(p,{id:i},b&&n.createElement(I,{orientation:"horizontal",className:N.group},g.map(((e,t)=>n.createElement(C,{role:"radio",className:N.button,icon:c.SERIES_ICONS[e],"aria-checked":m&&o===e,isActive:m&&o===e,isDisabled:H(e),key:t,hint:(0,M.getTranslatedChartStyleName)(e),isFirst:0===t,
isLast:t===g.length-1,onClick:m?this._handleQuickClick:void 0,onClickArg:e,"data-value":h.STYLE_SHORT_NAMES[e]})))),n.createElement(d.ToolbarMenuButton,{arrow:Boolean(b),content:b?void 0:n.createElement(p,null,n.createElement(s.Icon,{icon:c.SERIES_ICONS[o]})),tooltip:b?l.t(null,void 0,a(27246)):(0,M.getTranslatedChartStyleName)(o),className:N.menu,isDrawer:e,onClick:this._trackClick},t))}))}_renderPopupMenuItem(e,t,a){const{isFavoritingAllowed:i}=this.props,o=this._isStyleFavorited(e);return n.createElement(R.AccessibleMenuItem,{key:`chart-type-${e}`,theme:a?w.multilineLabelWithIconAndToolboxTheme:void 0,icon:c.SERIES_ICONS[e],isActive:t,isDisabled:H(e),label:n.createElement("div",{className:N.label},(0,M.getTranslatedChartStyleName)(e)||"",!1,!1),onClick:this._handleSelectStyle,onClickArg:e,showToolboxOnHover:!o,showToolboxOnFocus:!0,toolbox:i&&n.createElement(A.MenuFavoriteButton,{isActive:t,isFilled:o,onClick:()=>this._handleClickFavorite(e)}),"data-value":h.STYLE_SHORT_NAMES[e]})}_handleChangeSettings(e){this.setState({lastSelectedNotFavorite:void 0,favorites:e})}_isStyleFavorited(e){return-1!==this.state.favorites.indexOf(e)}_handleAddFavorite(e){const{favorites:t}=this.state,{favoriteChartStylesService:a}=this.context;a.set([...t,e])}_handleRemoveFavorite(e){const{favorites:t}=this.state,{favoriteChartStylesService:a}=this.context;a.set(t.filter((t=>t!==e)))}_trackClick(){0}}D.contextType=F;var L=a(50238),W=a(81348),P=a(16135);const B=["medium","small"],V=(0,n.forwardRef)((function(e,t){const{text:a,className:i,displayMode:o,collapseWhen:s=B,...l}=e,r=!s.includes(o);return n.createElement(W.ToolWidgetButton,{...l,ref:t,text:r?a:void 0,className:u(i,P.button,r?P.withText:P.withoutText)})}));function z(e){const{tooltip:t,...a}=e,[i,o]=(0,L.useRovingTabindexElement)(null);return n.createElement(V,{"aria-label":t,...a,tag:"button",tabIndex:o,ref:i,"data-tooltip":t})}var Z=a(51768),K=a(76460),q=a(1393);const Q=(0,f.registryContextType)();class G extends n.PureComponent{constructor(e,t){super(e,t),this._updateState=e=>{this.setState({isActive:e})},this._handleClick=e=>{var t;(0,Z.trackEvent)("GUI","Chart Header Toolbar","compare"),null===(t=this._compareDialogRenderer)||void 0===t||t.show({shouldReturnFocus:(0,K.isKeyboardClick)(e)})},(0,f.validateRegistry)(t,{chartWidgetCollection:o.any.isRequired}),this.state={isActive:!1},this._compareDialogRenderer=this.context.chartWidgetCollection.getCompareDialogRenderer()}componentDidMount(){var e;null===(e=this._compareDialogRenderer)||void 0===e||e.visible().subscribe(this._updateState)}componentWillUnmount(){var e;null===(e=this._compareDialogRenderer)||void 0===e||e.visible().unsubscribe(this._updateState)}render(){const{isActive:e}=this.state;return n.createElement(z,{...this.props,icon:q,isOpened:e,onClick:this._handleClick,collapseWhen:["full","medium","small"],tooltip:l.t(null,void 0,a(53942))})}}G.contextType=Q;var j=a(98945),X=a(61814),$=a(68335),J=a(97268),Y=a(36992);const ee=(0,X.hotKeySerialize)({keys:[(0,$.humanReadableModifiers)($.Modifiers.Shift,!1),"F"],
text:"{0} + {1}"}),te=(0,f.registryContextType)();function ae(e){return e.fullscreen().value()?Y:J}class ie extends n.PureComponent{constructor(e,t){super(e,t),this._updateState=()=>{this.setState({icon:ae(this.context.chartWidgetCollection)})},this._handleClick=()=>{const{chartWidgetCollection:e}=this.context;e.fullscreen().value()?e.exitFullscreen():e.startFullscreen()},(0,f.validateRegistry)(t,{chartWidgetCollection:o.any.isRequired}),this.state={icon:ae(this.context.chartWidgetCollection)},this._subscribe()}render(){const{className:e,id:t}=this.props,{icon:i}=this.state;return n.createElement(j.ToolbarIconButton,{id:t,icon:i,onClick:this._handleClick,className:u(e),tooltip:l.t(null,void 0,a(67092)),"data-tooltip-hotkey":ee})}componentWillUnmount(){this._unsubscribe()}_subscribe(){this.context.chartWidgetCollection.fullscreen().subscribe(this._updateState)}_unsubscribe(){this.context.chartWidgetCollection.fullscreen().unsubscribe(this._updateState)}}ie.contextType=te;var ne=a(50151),oe=a(16396),se=a(20243),le=a(16638);const re=(0,a(31955).getLogger)("FavoritesInfo");function he(e){if(0===e.length)return Promise.resolve([]);re.logNormal("Requesting favorites info");const t=[],a=new Map,i=new Map,n=new Map;return e.forEach((e=>{switch(e.type){case"java":n.set(e.studyId,e);break;case"pine":isPublishedPineId(e.pineId)?a.set(e.pineId,e):i.set(e.pineId,e);break;default:(0,ne.assert)(!1,`unknown favorite type ${JSON.stringify(e)}`)}})),0!==n.size&&t.push((0,le.studyMetaInfoRepository)().findAllJavaStudies().then((e=>{const t=new Map;for(const a of e)!a.is_hidden_study&&n.has(a.id)&&t.set(a.id,{name:a.description,localizedName:a.description_localized,studyMarketShittyObject:a});return t})).then((e=>{const t=function(e,t){const a={items:[],notFoundItems:[]};return e.forEach(((e,i)=>{const n=t.get(i);void 0!==n?a.items.push({item:e,info:n}):a.notFoundItems.push(e)})),a}(n,e);if(0!==t.notFoundItems.length){const e=t.notFoundItems.map((e=>e.studyId));re.logWarn(`Cannot find java scripts: ${JSON.stringify(e)}`)}return t.items}))),Promise.all(t).then((e=>(re.logNormal("Requesting favorites info finished"),e.reduce(((e,t)=>e.concat(t)),[]))))}var ce=a(630),de=a(928),ue=a(26996),ve=a(90531);function me(e){const{className:t}=e;return n.createElement("div",{className:v()(ve.spinnerWrap,t)},n.createElement(ue.Loader,null))}var pe=a(16829),ge=a(76422),be=a(39681),Ce=a(10047);const Se=(0,X.hotKeySerialize)({keys:["/"],text:"{0}"}),_e=(0,f.registryContextType)();class ye extends n.PureComponent{constructor(e,t){super(e,t),this._promise=null,this._menu=n.createRef(),this._menuItemsContainer=n.createRef(),this._favoriteFundamentalsModel=null,this._setActiveState=e=>{this.setState({isActive:e})},this._handleClick=e=>{const{studyMarket:t}=this.props;this.setState({isActive:!0},(()=>{t.visible().value()?t.hide():t.show({shouldReturnFocus:(0,K.isKeyboardClick)(e)})})),this._trackClick()},this._handleSelectIndicator=e=>{e=(0,ne.ensureDefined)(e),this._trackFavoriteAction("Favorite indicator from toolbar");"java"===e.type?e.studyId:e.pineId
;(async()=>{e=(0,ne.ensureDefined)(e);const{chartWidgetCollection:t}=this.context;if("java"===e.type){const t=(0,ce.tryFindStudyLineToolNameByStudyId)(e.studyId);if(null!==t)return await(0,ce.initLineTool)(t),void de.tool.setValue(t)}t.activeChartWidget.value().insertStudy(e,[])})()},this._handleFavoriteIndicatorsChange=()=>{const{favoriteScriptsModel:e}=this.context,t=[...(0,ne.ensureDefined)(e).favorites()];this.setState({favorites:t}),this._clearCache()},this._handleFavoriteFundamentalsChange=()=>{var e;const t=new Set((null===(e=this._favoriteFundamentalsModel)||void 0===e?void 0:e.favorites())||[]);this.setState({favoriteFundamentals:t}),this._clearCache()},this._handleMouseEnter=()=>{this._prefetchFavorites()},this._handleWrapClick=()=>{this._prefetchFavorites()},this._handleChangeActiveWidget=()=>{this._clearCache()},this._clearCache=()=>{this._promise=null,this.setState({infos:[]})},this._handleScriptRenamed=e=>{const{favoriteScriptsModel:t}=this.context;void 0!==t&&t.isFav(e.scriptIdPart)&&this._clearCache()},this._handleFavoriteMenuClick=()=>{this._trackClick(),this._trackFavoriteAction("Select favorite indicators dropdown")},(0,f.validateRegistry)(t,{favoriteScriptsModel:o.any,chartWidgetCollection:o.any.isRequired});const{favoriteScriptsModel:a}=t,i=void 0!==a?a.favorites():[];this.state={isActive:!1,isLoading:!1,favorites:i,favoriteFundamentals:void 0,infos:[]}}componentDidMount(){const{studyMarket:e}=this.props,{favoriteScriptsModel:t,chartWidgetCollection:a}=this.context;e.visible().subscribe(this._setActiveState),void 0!==t&&(t.favoritesChanged().subscribe(this,this._handleFavoriteIndicatorsChange),a.activeChartWidget.subscribe(this._handleChangeActiveWidget)),ge.on("TVScriptRenamed",this._handleScriptRenamed,null)}componentWillUnmount(){const{studyMarket:e}=this.props,{favoriteScriptsModel:t,chartWidgetCollection:a}=this.context;e.visible().unsubscribe(this._setActiveState),void 0!==t&&(t.favoritesChanged().unsubscribe(this,this._handleFavoriteIndicatorsChange),a.activeChartWidget.unsubscribe(this._handleChangeActiveWidget)),ge.unsubscribe("TVScriptRenamed",this._handleScriptRenamed,null),this._promise=null}render(){const{isActive:e,favorites:t,favoriteFundamentals:i,isLoading:o}=this.state,{className:s,displayMode:r,id:h}=this.props,{chartWidgetCollection:c}=this.context;return n.createElement(n.Fragment,null,n.createElement(p,{id:h,onMouseEnter:this._handleMouseEnter,onClick:this._handleWrapClick},n.createElement(z,{displayMode:r,className:s,icon:be,isOpened:e,onClick:this._handleClick,text:l.t(null,void 0,a(84549)),"data-role":"button","data-name":"open-indicators-dialog",tooltip:l.t(null,void 0,a(62005)),"data-tooltip-hotkey":Se}),Boolean(t.length>0||(null==i?void 0:i.size))&&n.createElement(_.MatchMedia,{rule:"screen and (max-width: 430px)"},(e=>n.createElement(d.ToolbarMenuButton,{key:c.activeChartWidget.value().id(),arrow:!0,closeOnClickOutside:!0,isDrawer:e,drawerPosition:"Bottom",ref:this._menu,menuReference:this._menuItemsContainer,onClick:this._handleFavoriteMenuClick,
"data-name":"show-favorite-indicators",tooltip:l.t(null,void 0,a(70234))},n.createElement("div",{className:v()(Ce.dropdown,e&&Ce.smallWidthWrapper)},n.createElement(pe.ToolWidgetMenuSummary,{className:e&&Ce.smallWidthTitle},l.t(null,void 0,a(52530))),o&&n.createElement(me,null),!o&&n.createElement(n.Fragment,null,this.state.infos.length>0?this.state.infos.map((t=>n.createElement(R.AccessibleMenuItem,{className:v()(e&&Ce.smallWidthMenuItem),theme:e?w.multilineLabelWithIconAndToolboxTheme:void 0,key:"java"===t.item.type?t.item.studyId:t.item.pineId,onClick:this._handleSelectIndicator,onClickArg:t.item,label:n.createElement("span",{className:v()(!e&&Ce.label,e&&Ce.smallWidthLabel,"apply-overflow-tooltip")},we(t))}))):null!==this._promise&&n.createElement(oe.PopupMenuItem,{isDisabled:!0,label:l.t(null,void 0,a(18448))}))))))))}_prefetchFavorites(){const{chartWidgetCollection:e}=this.context;if(null!==this._promise||!window.is_authenticated)return;if(!e.activeChartWidget.value().hasModel())return;this.setState({isLoading:!0});const t=this._promise=Promise.all([he(this.state.favorites),void 0]).then((e=>{if(t!==this._promise)return;const[a,i]=e;let n=[...a];if(i){const e=i.filter((e=>{var t;return null===(t=this.state.favoriteFundamentals)||void 0===t?void 0:t.has(e.scriptIdPart)})).map(this._mapFundamentalToFavoriteItemInfo);n.push(...e)}n=[...n].sort(((e,t)=>we(e).localeCompare(we(t)))),this.setState({infos:n,isLoading:!1},(()=>{var e;null===(e=this._menu.current)||void 0===e||e.update(),this._menuItemsContainer.current&&document.activeElement===this._menuItemsContainer.current&&(0,se.focusFirstMenuItem)(this._menuItemsContainer.current)}))}))}_trackClick(){0}_trackFavoriteAction(e){(0,Z.trackEvent)("GUI","Chart Header Toolbar",e)}_mapFundamentalToFavoriteItemInfo(e){return{item:{type:"pine",pineId:e.scriptIdPart},info:{name:e.scriptName,localizedName:getLocalizedFundamentalsName(e),studyMarketShittyObject:void 0}}}}function we(e){return e.info.localizedName||l.t(e.info.name,{context:"study"},a(83477))}ye.contextType=_e;var fe=a(10074),ke=a(85049),Me=a(53658);function Ee(e){return n.createElement("div",{className:u(Me.value,{[Me.selected]:e.isSelected})},e.value,e.metric)}var xe=a(90186),Te=a(70412),Ie=a(32563),Re=a(38068),Ae=a(64347);function Ne(e){const{interval:t,hint:a,isActive:i,scrollContainerRef:o,isDisabled:s,isFavorite:l,isSignaling:r,onClick:h,onClickRemove:c,onClickFavorite:d,isSmallTablet:u}=e,m=(0,xe.filterDataProps)(e),[p,g]=(0,Te.useHover)(),b=n.useCallback((e=>c(t,e)),[c,t]),C=n.useCallback((()=>d(t)),[d,t]),S=(0,n.useRef)(null);return(0,n.useEffect)((()=>{if(r){const e=S.current;if(e&&o.current){const t=e.getBoundingClientRect(),a=o.current.getBoundingClientRect();(a.top>t.top||a.bottom<t.bottom)&&e.scrollIntoView()}}}),[r,o]),n.createElement("div",{...g,ref:S},n.createElement(R.AccessibleMenuItem,{...m,className:v()(Ae.menuItem,u&&Ae.smallWidthMenuItem,r&&Ae.signal),theme:u?w.multilineLabelWithIconAndToolboxTheme:void 0,isActive:i,isDisabled:s,onClick:h,onClickArg:t,toolbox:function(){
const{isRemovable:t,isFavoritingAllowed:a}=e,o=n.createElement(Re.MenuRemoveButton,{key:"remove",isActive:i,hidden:!Ie.touch&&!p,onClick:b,className:Ae.remove}),s=n.createElement(A.MenuFavoriteButton,{key:"favorite",isActive:i,isFilled:l,onClick:C});return[t&&o,a&&s]}(),showToolboxOnHover:!l,showToolboxOnFocus:!0,label:a}))}const Fe={[ke.ResolutionKind.Ticks]:l.t(null,{context:"interval_group_name"},a(49512)),[ke.ResolutionKind.Seconds]:l.t(null,{context:"interval_group_name"},a(32116)),[ke.ResolutionKind.Minutes]:l.t(null,{context:"interval_group_name"},a(21551)),[ke.SpecialResolutionKind.Hours]:l.t(null,{context:"interval_group_name"},a(18817)),[ke.ResolutionKind.Days]:l.t(null,{context:"interval_group_name"},a(57252)),[ke.ResolutionKind.Weeks]:l.t(null,{context:"interval_group_name"},a(41815)),[ke.ResolutionKind.Months]:l.t(null,{context:"interval_group_name"},a(89506)),[ke.ResolutionKind.Range]:l.t(null,{context:"interval_group_name"},a(22163)),[ke.ResolutionKind.Invalid]:""};function He(e,t=!1){return{id:e,name:Fe[e],items:[],mayOmitSeparator:t}}var Oe=a(52033),Ue=a(29197),De=a(59064),Le=a(39706),We=a(67505);function Pe(e){const{className:t,...a}=e,[i,o]=(0,L.useRovingTabindexElement)(null);return n.createElement(Le.CollapsibleSection,{...a,ref:i,tabIndex:o,"data-role":"menuitem",className:v()(We.accessible,t),onKeyDown:function(e){const t=(0,x.hashFromEvent)(e);13!==t&&32!==t||(e.preventDefault(),i.current instanceof HTMLElement&&i.current.click())}})}var Be=a(34585),Ve=a(69397),ze=a(6198);const Ze={openDialog:l.t(null,void 0,a(46311)),timeInterval:l.t(null,void 0,a(41173))},Ke=(0,X.hotKeySerialize)({keys:[","],text:l.t(null,void 0,a(92969))}),qe=(0,f.registryContextType)(),Qe=new Oe.Delegate,Ge=n.lazy((async()=>({default:(await Promise.all([a.e(101),a.e(7528),a.e(6246),a.e(7811),a.e(412),a.e(6625),a.e(55),a.e(4392),a.e(6665),a.e(4447),a.e(8065),a.e(6955),a.e(8843),a.e(2079),a.e(2264),a.e(4013)]).then(a.bind(a,17891))).ToolWidgetIntervalsAddDialog})));class je extends n.PureComponent{constructor(e,t){super(e,t),this._menu=n.createRef(),this._menuItemsContainerRef=n.createRef(),this._drawerContainerRef=n.createRef(),this._renderChildren=(e,t)=>[...this._createIntervalItem(t),...this._createMenuItems(e,t)],this._handleChangeInterval=()=>{const e=r.linking.interval.value(),{activeInterval:t,lastNotQuicked:a}=this.state,i=this._getQuicks();this.setState({activeInterval:(0,fe.normalizeIntervalString)(e),lastNotQuicked:void 0===t||i.includes(t)?a:t})},this._bindedForceUpdate=()=>{this.forceUpdate()},this._handleSelectInterval=e=>{void 0!==e&&e!==r.linking.interval.value()&&this.context.chartWidgetCollection.setResolution(e),e&&(0,Z.trackEvent)("GUI","Time Interval",e)},this._handleClickFavorite=e=>{e=(0,ne.ensureDefined)(e),this._isIntervalFavorite(e)?this._handleRemoveFavorite(e):this._handleAddFavorite(e)},this._handleAddFavorite=e=>{const{favorites:t}=this.state;this.context.favoriteIntervalsService.set([...t,e])},this._handleRemoveFavorite=e=>{const{favorites:t}=this.state
;this.context.favoriteIntervalsService.set(t.filter((t=>t!==e)))},this._handleAddInterval=(e,t)=>{const{intervalService:a,intervalsMenuViewStateService:i}=this.context,{menuViewState:n}=this.state,o=a.add(e,t);if(o){this.setState({lastAddedInterval:o});const e=ke.Interval.parseExt(o);if(e.interval.isMinuteHours())return void i.set({...n,[ke.SpecialResolutionKind.Hours]:!1});const t=e.guiResolutionKind;if(t===ke.ResolutionKind.Days||t===ke.ResolutionKind.Weeks||t===ke.ResolutionKind.Months)return void i.set({...n,[ke.ResolutionKind.Days]:!1});i.set({...n,[t]:!1})}},this._handleRemoveInterval=(e,t)=>{var a;const{intervalService:i}=this.context;if(e){if(t&&(0,K.isKeyboardClick)(t)&&this._menuItemsContainerRef.current){const t=(0,se.queryMenuElements)(this._menuItemsContainerRef.current),i=t.findIndex((t=>t.matches(`[data-value="${e}"]`)));if(-1!==i){const e=null!==(a=t[i+1])&&void 0!==a?a:t[i-1];e?e.focus():(0,se.focusFirstMenuItem)(this._menuItemsContainerRef.current)}}i.remove(e),this._handleRemoveFavorite(e)}},this._getHandleSectionStateChange=e=>t=>{const{menuViewState:a}=this.state,{intervalsMenuViewStateService:i}=this.context;i.set({...a,[e]:!t})},this._handleOpenAddIntervalDialog=()=>{this.setState({isAddIntervalDialogOpened:!0})},this._handleCloseAddIntervalDialog=()=>{this.setState({isAddIntervalDialogOpened:!1})},this._handleGlobalClose=()=>{const{isFake:e}=this.props,{isAddIntervalDialogOpened:t}=this.state;e||t||Qe.fire()},this._handeQuickClick=e=>{this._handleSelectInterval(e),this._trackClick()},(0,f.validateRegistry)(t,{chartApiInstance:o.any.isRequired,favoriteIntervalsService:o.any.isRequired,intervalService:o.any.isRequired,intervalsMenuViewStateService:o.any.isRequired});const{chartApiInstance:a,favoriteIntervalsService:s,intervalService:l,intervalsMenuViewStateService:h}=t;this._customIntervals=i.enabled("custom_resolutions");const c=r.linking.interval.value(),d=c&&(0,fe.normalizeIntervalString)(c),u=s.get(),v=l.getCustomIntervals(),m=h.get();this._defaultIntervals=a.defaultResolutions().filter(fe.isIntervalEnabled).map(fe.normalizeIntervalString),this.state={isOpenedFormMenu:!1,activeInterval:d,favorites:u,customs:v,menuViewState:m,isAddIntervalDialogOpened:!1}}componentDidMount(){const{favoriteIntervalsService:e,intervalService:t,intervalsMenuViewStateService:a}=this.context;e.getOnChange().subscribe(this,this._handleChangeFavorites),a.getOnChange().subscribe(this,this._handleChangeMenuViewState),t.getOnChange().subscribe(this,this._handleChangeCustoms),r.linking.interval.subscribe(this._handleChangeInterval),r.linking.intraday.subscribe(this._bindedForceUpdate),r.linking.seconds.subscribe(this._bindedForceUpdate),r.linking.ticks.subscribe(this._bindedForceUpdate),r.linking.range.subscribe(this._bindedForceUpdate),r.linking.supportedResolutions.subscribe(this._bindedForceUpdate),r.linking.dataFrequencyResolution.subscribe(this._bindedForceUpdate),De.globalCloseDelegate.subscribe(this,this._handleGlobalClose)}componentWillUnmount(){
const{favoriteIntervalsService:e,intervalService:t,intervalsMenuViewStateService:a}=this.context;e.getOnChange().unsubscribe(this,this._handleChangeFavorites),a.getOnChange().unsubscribe(this,this._handleChangeMenuViewState),t.getOnChange().unsubscribe(this,this._handleChangeCustoms),r.linking.interval.unsubscribe(this._handleChangeInterval),r.linking.intraday.unsubscribe(this._bindedForceUpdate),r.linking.seconds.unsubscribe(this._bindedForceUpdate),r.linking.ticks.unsubscribe(this._bindedForceUpdate),r.linking.range.unsubscribe(this._bindedForceUpdate),r.linking.supportedResolutions.unsubscribe(this._bindedForceUpdate),r.linking.dataFrequencyResolution.unsubscribe(this._bindedForceUpdate),De.globalCloseDelegate.unsubscribe(this,this._handleGlobalClose)}componentDidUpdate(e,t){this.state.lastAddedInterval&&setTimeout((()=>this.setState({lastAddedInterval:void 0})),1600)}render(){const{isShownQuicks:e,id:t}=this.props,{activeInterval:a,customs:i,lastNotQuicked:o,isAddIntervalDialogOpened:s}=this.state,l=this._getQuicks(),r=(0,fe.sortResolutions)([...l]);void 0!==a&&r.includes(a)?void 0!==o&&r.push(o):void 0!==a&&r.push(a);const h=(!(!e||0===l.length)||void 0)&&r.length>1,c={},v=(0,fe.mergeResolutions)(this._defaultIntervals,i);(void 0!==a?v.concat(a):v).filter(fe.isAvailable).forEach((e=>c[e]=!0));const m=void 0!==a?(0,fe.getTranslatedResolutionModel)(a):null;return n.createElement(p,{id:t},h&&n.createElement(I,{className:Ve.group,orientation:"horizontal"},r.map(((e,t)=>{const i=(0,fe.getTranslatedResolutionModel)(e);return n.createElement(C,{key:t,role:"radio",className:u(Ve.button,{[Ve.first]:0===t,[Ve.last]:t===r.length-1}),text:n.createElement(Ee,{value:i.mayOmitMultiplier?void 0:i.multiplier,metric:i.shortKind}),hint:i.hint,"aria-checked":a===e,isActive:a===e,isDisabled:!c[e],onClick:this._handeQuickClick,onClickArg:e,"data-value":e})}))),n.createElement(_.MatchMedia,{rule:y.DialogBreakpoints.TabletSmall},(e=>n.createElement(n.Fragment,null,n.createElement(Ue.CloseDelegateContext.Provider,{value:Qe},n.createElement(d.ToolbarMenuButton,{arrow:Boolean(h),closeOnClickOutside:!s,content:h||null===m?void 0:n.createElement(p,{className:Ve.menuContent},n.createElement(Ee,{value:m.mayOmitMultiplier?void 0:m.multiplier,metric:m.shortKind})),hotKey:h?Ke:void 0,className:Ve.menu,ref:this._menu,isDrawer:e,onClick:this._trackClick,tooltip:h||null===m?Ze.timeInterval:m.hint,menuReference:this._menuItemsContainerRef,drawerReference:this._drawerContainerRef},n.createElement("div",{className:Ve.dropdown},this._renderChildren(v,e)))),s&&n.createElement(n.Suspense,{fallback:null},n.createElement(Ge,{onAdd:this._handleAddInterval,onClose:this._handleCloseAddIntervalDialog,onUnmount:this._handleCloseAddIntervalDialog,intervalService:this.context.intervalService,isSmallTablet:e}))))))}_createMenuItems(e,t){const a=function(e){const t=He(ke.ResolutionKind.Ticks),a=He(ke.ResolutionKind.Seconds),i=He(ke.ResolutionKind.Minutes),n=He(ke.SpecialResolutionKind.Hours),o=He(ke.ResolutionKind.Days),s=He(ke.ResolutionKind.Range)
;return e.forEach((e=>{const l=ke.Interval.parse(e);l.isMinuteHours()?n.items.push(e):l.isMinutes()?(0,ke.isHour)(Number(l.multiplier()))?n.items.push(e):i.items.push(e):l.isSeconds()?a.items.push(e):l.isDWM()?o.items.push(e):l.isRange()?s.items.push(e):l.isTicks()&&t.items.push(e)})),[t,a,i,n,o,s].filter((e=>0!==e.items.length))}(e).map(((e,a,i)=>this._renderResolutionsGroup(e,1===i.length,t)));return function(e){let t=!1;return e.filter(((e,a,i)=>{let n=!0;return e.type===S.PopupMenuSeparator&&(0!==a&&a!==i.length-1||(n=!1),t&&(n=!1)),t=e.type===S.PopupMenuSeparator,n}))}([].concat(...a))}_createIntervalItem(e){return this._customIntervals?[n.createElement(Xe,{key:"add-dialog",isSmallTablet:e,onClick:this._handleOpenAddIntervalDialog}),n.createElement(S.PopupMenuSeparator,{key:"custom-interval-separator"})]:[]}_renderResolutionsGroup(e,t=!1,a){const i=[],o=e.items.map((e=>this._renderPopupMenuItem(e,a)));if(t)i.push(...o);else if(a){const t=n.createElement($e,{key:e.id,title:e.name},o);i.push(t)}else{const{intervalsMenuViewStateService:t}=this.context,{menuViewState:a}=this.state;if(!t.isAllowed(e.id))return[];const s=n.createElement(Pe,{key:e.id,className:Ve.section,summary:e.name,open:!a[e.id],onStateChange:this._getHandleSectionStateChange(e.id)},o);i.push(s)}return(!e.mayOmitSeparator||e.items.length>1)&&(i.unshift(n.createElement(S.PopupMenuSeparator,{key:`begin-${e.name}`})),i.push(n.createElement(S.PopupMenuSeparator,{key:`end-${e.name}`}))),i}_handleChangeFavorites(e){this.setState({lastNotQuicked:void 0,favorites:e})}_handleChangeCustoms(e){this.setState({customs:e})}_handleChangeMenuViewState(e){this.setState({menuViewState:e},(()=>{this._menu.current&&this._menu.current.update()}))}_renderPopupMenuItem(e,t){const{isFavoritingAllowed:a}=this.props,{activeInterval:i,lastAddedInterval:o}=this.state,s=e===i,l=(0,fe.isAvailable)(e),r=this._isIntervalFavorite(e),h=this._isIntervalDefault(e),c=(0,fe.getTranslatedResolutionModel)(e);return n.createElement(Ne,{key:e,isSmallTablet:t,interval:e,hint:c.hint,isSignaling:o===e,isFavoritingAllowed:a,isDisabled:!l,isFavorite:r,isRemovable:!h,isActive:s,onClick:this._handleSelectInterval,onClickRemove:this._handleRemoveInterval,onClickFavorite:this._handleClickFavorite,scrollContainerRef:t?this._drawerContainerRef:this._menuItemsContainerRef,"data-value":e})}_isIntervalDefault(e){return this._defaultIntervals.includes(e)}_isIntervalFavorite(e){return this.state.favorites.includes(e)}_getQuicks(e){return this.props.isShownQuicks&&"small"!==this.props.displayMode?void 0===e?this.state.favorites:e:[]}_trackClick(){0}}function Xe(e){const{onClick:t,className:i,isSmallTablet:o}=e;return n.createElement(R.AccessibleMenuItem,{key:"add-dialog",className:u(Ve.addCustomInterval,!o&&Ve.desktop,i),onClick:t,label:(0,Be.appendEllipsis)(l.t(null,void 0,a(80335))),icon:ze,dontClosePopup:!0})}function $e(e){const{children:t,title:a,className:i}=e;return n.createElement("div",{className:i},n.createElement("div",{className:Ve.smallTabletSectionTitle},a),t)}je.contextType=qe
;var Je=a(66494),Ye=a(82436);const et=(0,f.registryContextType)();class tt extends n.PureComponent{constructor(e,t){super(e,t),this._handleClick=()=>{const{chartWidgetCollection:e,windowMessageService:t,isFundamental:a}=this.context,i=e.activeChartWidget.value();i.withModel(null,(()=>{t.post(parent,"openChartInPopup",{symbol:i.model().mainSeries().actualSymbol(),interval:i.model().mainSeries().interval(),fundamental:a})}))},(0,f.validateRegistry)(t,{isFundamental:o.any,chartWidgetCollection:o.any.isRequired,windowMessageService:o.any.isRequired})}render(){const{className:e}=this.props;return n.createElement(j.ToolbarIconButton,{className:u(e,Je.button),icon:Ye,onClick:this._handleClick,tooltip:l.t(null,void 0,a(34770))})}}tt.contextType=et;var at=a(34369);const it=(0,f.registryContextType)();class nt extends n.PureComponent{constructor(e,t){super(e,t),this._updateState=e=>{this.setState({isOpened:e})},this._handleClick=e=>{const{chartWidgetCollection:t}=this.context,a=t.activeChartWidget.value();(0,Z.trackEvent)("GUI","Chart Header Toolbar","chart properties"),a.showGeneralChartProperties(void 0,{shouldReturnFocus:(0,K.isKeyboardClick)(e)})},(0,f.validateRegistry)(t,{chartWidgetCollection:o.any.isRequired}),this.state={isOpened:!1},this._propertiesDialogRenderer=this.context.chartWidgetCollection.getChartPropertiesDialogRenderer()}componentDidMount(){var e;null===(e=this._propertiesDialogRenderer)||void 0===e||e.visible().subscribe(this._updateState)}componentWillUnmount(){var e;null===(e=this._propertiesDialogRenderer)||void 0===e||e.visible().unsubscribe(this._updateState)}render(){const{isOpened:e}=this.state;return n.createElement(j.ToolbarIconButton,{...this.props,icon:at,isOpened:e,onClick:this._handleClick,tooltip:l.t(null,void 0,a(71262))})}}nt.contextType=it;var ot=a(4741),st=a(93352),lt=a(49483),rt=a(40173),ht=a(10428),ct=a(20807);(0,rt.mergeThemes)(ht.DEFAULT_MENU_ITEM_SWITCHER_THEME,ct);var dt=a(97807);function ut(e){const{wasChanges:t,isSaving:i,className:o}=e;return n.createElement("span",{className:u(dt.saveString,!t&&!i&&dt.hidden,o)},i?n.createElement(ue.Loader,{className:dt.loader,size:"small",staticPosition:!0}):l.t(null,void 0,a(64e3)))}var vt=a(27830),mt=a(36296),pt=a(40842),gt=a(64212);a(73546);const bt=i.enabled("widget"),Ct=(0,rt.mergeThemes)(W.DEFAULT_TOOL_WIDGET_BUTTON_THEME,gt),St=(0,rt.mergeThemes)(oe.DEFAULT_POPUP_MENU_ITEM_THEME,{shortcut:pt.shortcut,withIcon:pt.withIcon}),_t=((0,rt.mergeThemes)(oe.DEFAULT_POPUP_MENU_ITEM_THEME,{toolbox:pt.toolbox}),(0,rt.mergeThemes)(oe.DEFAULT_POPUP_MENU_ITEM_THEME,{toolbox:pt.toolboxSmall}),l.t(null,void 0,a(40115))),yt=l.t(null,void 0,a(26276)),wt=[],ft=(0,X.hotKeySerialize)({keys:[(0,$.humanReadableModifiers)($.Modifiers.Mod,!1),"S"],text:"{0} + {1}"}),kt=()=>null;class Mt extends n.PureComponent{constructor(e){super(e),this._copyElRef=n.createRef(),this._handleCopyLinkClick=()=>Promise.resolve(),this._handleFavoriteClick=(e,t)=>{e.preventDefault(),t.favoriteAction()},this._handleCloneClick=()=>{var e,t
;null===(t=(e=this.props).onCloneChart)||void 0===t||t.call(e)},this._handleSaveClick=()=>{var e,t;null===(t=(e=this.props).onSaveChart)||void 0===t||t.call(e),this._trackClick()},this._handleSaveAsClick=()=>{var e,t;null===(t=(e=this.props).onSaveAsChart)||void 0===t||t.call(e)},this.state={isSaving:!1}}componentDidUpdate(e,t){e.isProcessing&&!this.props.isProcessing&&(clearTimeout(this._timeout),this._timeout=void 0,this.setState({isSaving:!1})),!e.isProcessing&&this.props.isProcessing&&(this._timeout=setTimeout((()=>{this.setState({isSaving:!0})}),1e3))}componentWillUnmount(){this._timeout&&clearTimeout(this._timeout)}render(){const{id:e,isReadOnly:t,displayMode:i,isProcessing:o,title:r,wasChanges:h,hideMenu:c,isTabletSmall:v,onOpenMenu:m,dataNameSaveMenu:g,isSaveDialogOpened:b,chartList:C,favorites:S,showRemoveFavoriteHint:_,onButtonClick:y,onAdditionalButtonClick:w}=this.props,f=!t&&!c;let k=[];vt.showFavoriteLayouts&&C&&S&&(k=C.filter((e=>S[e.id])).sort(((e,t)=>e.name.localeCompare(t.name))));const M=!(h||!r||this.state.isSaving),E=n.createElement("div",{className:pt.textWrap},n.createElement("span",{className:pt.text},r||l.t(null,void 0,a(64e3))),n.createElement(ut,{isSaving:this.state.isSaving,wasChanges:h}));return n.createElement(p,null,t?n.createElement(p,null,n.createElement(z,{id:e,displayMode:i,icon:n.createElement(s.Icon,{icon:mt}),isDisabled:o,onClick:this._handleCloneClick,text:l.t(null,void 0,a(49680)),collapseWhen:wt,tooltip:_t})):n.createElement(p,null,n.createElement(z,{id:e,className:u(pt.button,f&&pt.buttonSmallPadding),displayMode:i,"aria-disabled":!!M||void 0,isDisabled:o,onClick:M?void 0:this._handleSaveClick,text:E,theme:Ct,collapseWhen:wt,isOpened:b,tooltip:M?l.t(null,void 0,a(89771)):l.t(null,void 0,a(15356)),"data-tooltip-hotkey":bt||M?"":ft}),f&&n.createElement("div",{className:pt.menuBtnWrap},n.createElement(d.ToolbarMenuButton,{"data-name":g,arrow:!0,isDrawer:v,drawerPosition:"Bottom",onClick:this._trackClick,onOpen:m,tooltip:l.t(null,void 0,a(38973)),menuClassName:u(!v&&pt.menu)},this._renderMenuItems(Boolean(v))),vt.showFavoriteLayouts&&_&&k.length>FAVORITE_LAYOUTS_LIMIT&&n.createElement(n.Suspense,{fallback:null},n.createElement(kt,{onClick:y,onAdditionalClick:w,placement:"auto",theme:undefined,buttonSize:"small",saveHintStateByClick:!1,text:l.t(null,{count:k.length,plural:"Your favorite layouts have moved to the top toolbar. You currently have {count} favorites. You can reduce the number to find them easier — unfavorite all in just one click or set up your choice manually.",replace:{count:k.length.toString()}},a(6094)),buttonText:l.t(null,void 0,a(66418)),additionalButtonText:l.t(null,void 0,a(36291)),settingsHintKey:"hint.favoriteLayouts",placeHolderClassName:pt.hintPlaceholder,hideWithReference:!0,hintStateHandler:favoriteLayoutsHintStateHandler}))),!1))}_renderMenuItems(e){
const{wasChanges:t,isProcessing:i,chartId:o,onSaveChartFromMenu:s,onRenameChart:r,onLoadChart:h,onNewChart:c,isAutoSaveEnabled:d,autoSaveId:v,sharingId:m,onAutoSaveChanged:p,isSharingEnabled:g,onSharingChanged:b,recentItems:C,onExportData:_,isAuthenticated:y,favorites:f}=this.props,k=e?w.multilineLabelWithIconAndToolboxTheme:St,M=e?void 0:(0,$.humanReadableHash)($.Modifiers.Mod+83),E=e?void 0:l.t(null,{context:"hotkey"},a(23821)),x=[];return x.push(n.createElement(R.AccessibleMenuItem,{key:"save",isDisabled:Boolean(i||!t&&o),label:yt,onClick:s,shortcut:M,labelRowClassName:u(e&&pt.popupItemRowTabletSmall),theme:k,"data-name":"save-load-menu-item-save"})),void 0!==o&&x.push(n.createElement(R.AccessibleMenuItem,{key:"rename",icon:void 0,label:(0,Be.appendEllipsis)(l.t(null,void 0,a(6321))),onClick:r,labelRowClassName:u(e&&pt.popupItemRowTabletSmall),theme:k,"data-name":"save-load-menu-item-rename"}),n.createElement(R.AccessibleMenuItem,{key:"save-as",icon:void 0,label:(0,Be.appendEllipsis)(_t),onClick:this._handleSaveAsClick,labelRowClassName:u(e&&pt.popupItemRowTabletSmall),theme:k,"data-name":"save-load-menu-item-clone"})),x.push(n.createElement(S.PopupMenuSeparator,{key:"all-layouts-separator"}),n.createElement(R.AccessibleMenuItem,{key:"all-layouts",className:"js-save-load-menu-item-load-chart",label:(0,Be.appendEllipsis)(l.t(null,void 0,a(79825))),onClick:h,labelRowClassName:u(e&&pt.popupItemRowTabletSmall),theme:k,shortcut:E,"data-name":"save-load-menu-item-load"})),x}_trackClick(){0}}(0,n.forwardRef)(((e,t)=>{const{isTabletSmall:i,onClick:o}=e,[s,r]=(0,L.useRovingTabindexElement)(t);return n.createElement("div",{ref:s,className:u(pt.copyLink,i&&pt.copyLinkMobile,pt.accessibleLabel),onClick:o,tabIndex:r,onKeyDown:function(e){if(e.target!==e.currentTarget)return;const t=(0,$.hashFromEvent)(e);13!==t&&32!==t||(e.preventDefault(),s.current instanceof HTMLElement&&s.current.click())},"data-role":"menuitem"},l.t(null,void 0,a(81939)))}));const Et=(0,f.registryContextType)();class xt extends n.PureComponent{constructor(e,t){super(e,t),this._exportDialogPromise=null,this._recentLayoutsAbortController=null,this._requestRecentLayouts=()=>{var e;null===(e=this._recentLayoutsAbortController)||void 0===e||e.abort()},this._updateState=e=>{this.setState((t=>({...t,...e})))},this._handleExportData=()=>{0},this._onSaveDialogVisibleChange=e=>{this.setState({isSaveDialogOpened:e})},this._syncState=e=>{this.setState(e)},this._handleAdditionalButton=()=>{const{loadChartService:e}=this.context;e.deleteAllFavorites()},this._onChangeHasChanges=e=>{this.state.wasChanges!==e&&this.setState({wasChanges:e})},this._onChangeAutoSaveEnabled=e=>{0},this._onChangeSharingEnabled=e=>{this.setState({isSharingEnabled:e})},this._onChangeTitle=e=>{this.setState({title:e})},this._onChangeId=e=>{this.setState({id:e})},this._onChartAboutToBeSaved=()=>{this.setState({isProcessing:!0})},this._onChartSaved=()=>{this.setState({isProcessing:!1})},this._handleAutoSaveEnabled=e=>{0},this._handleSharingEnabled=e=>{0},this._handleClickSave=()=>{
this.context.saveChartService.saveChartOrShowTitleDialog(),this._trackEvent("Save click")},this._handleOpenMenu=()=>{this._requestRecentLayouts()},this._handleClickSaveFromMenu=()=>{this.context.saveChartService.saveChartOrShowTitleDialog(),this._trackEvent("Save From Menu")},this._handleClickClone=()=>{this.context.saveChartService.cloneChart()},this._handleClickSaveAs=()=>{this.context.saveChartService.saveChartAs(),this._trackEvent("Make a copy")},this._handleClickNew=()=>{this._trackEvent("New chart layout")},this._handleClickLoad=()=>{this.context.loadChartService.showLoadDialog()},this._handleHotkey=()=>{this.context.loadChartService.showLoadDialog()},this._handleClickRename=()=>{this.context.saveChartService.renameChart(),this._trackEvent("Rename")},(0,f.validateRegistry)(t,{chartWidgetCollection:o.any.isRequired,chartChangesWatcher:o.any.isRequired,saveChartService:o.any.isRequired,sharingChartService:o.any,loadChartService:o.any.isRequired});const{chartWidgetCollection:a,chartChangesWatcher:i,saveChartService:n,sharingChartService:s,loadChartService:l}=t;this._loadChartServiceState=l.state(),this.state={isAuthenticated:window.is_authenticated,isProcessing:!1,id:a.metaInfo.id.value(),title:a.metaInfo.name.value(),wasChanges:i.hasChanges(),iconHovered:!1,isSaveDialogOpened:!1,...this._loadChartServiceState.value()}}componentDidMount(){const{chartSaver:e,isFake:t,stateSyncEmitter:i}=this.props,{chartWidgetCollection:n,chartChangesWatcher:o,saveChartService:s,sharingChartService:r,loadChartService:h}=this.context;t?i.on("change",this._syncState):(o.getOnChange().subscribe(this,this._onChangeHasChanges),n.metaInfo.name.subscribe(this._onChangeTitle),n.metaInfo.id.subscribe(this._onChangeId),this._hotkeys=(0,ot.createGroup)({desc:"Save/Load"}),this._hotkeys.add({desc:l.t(null,void 0,a(29517)),handler:this._handleHotkey,hotkey:190}),e.chartSaved().subscribe(this,this._onChartSaved),e.chartAboutToBeSaved().subscribe(this,this._onChartAboutToBeSaved),window.loginStateChange.subscribe(this,this._onLoginStateChange),this.context.saveChartService.getCreateController().visible().subscribe(this._onSaveDialogVisibleChange),this._loadChartServiceState.subscribe(this._updateState),vt.showFavoriteLayouts&&h.refreshChartList())}componentDidUpdate(e,t){this.props.isFake||t!==this.state&&this.props.stateSyncEmitter.emit("change",this.state)}componentWillUnmount(){var e;const{chartSaver:t,isFake:a,stateSyncEmitter:i}=this.props,{chartWidgetCollection:n,chartChangesWatcher:o,saveChartService:s,sharingChartService:l}=this.context;a?i.off("change",this._syncState):(o.getOnChange().unsubscribe(this,this._onChangeHasChanges),n.metaInfo.name.unsubscribe(this._onChangeTitle),n.metaInfo.id.unsubscribe(this._onChangeId),(0,ne.ensureDefined)(this._hotkeys).destroy(),t.chartSaved().unsubscribe(this,this._onChartSaved),t.chartAboutToBeSaved().unsubscribe(this,this._onChartAboutToBeSaved),window.loginStateChange.unsubscribe(this,this._onLoginStateChange),null===(e=this._recentLayoutsAbortController)||void 0===e||e.abort(),
this.context.saveChartService.getCreateController().visible().unsubscribe(this._onSaveDialogVisibleChange),this._loadChartServiceState.unsubscribe(this._updateState))}render(){const{isReadOnly:e,displayMode:t,id:a,isFake:i}=this.props,{isProcessing:o,isAuthenticated:s,title:l,id:r,wasChanges:h,isAutoSaveEnabled:c,isSharingEnabled:d,isSaveDialogOpened:u,favorites:v,chartList:m,recents:p}=this.state,g={displayMode:t,isReadOnly:e,isAuthenticated:s,isProcessing:o,wasChanges:h,title:l,id:a,isSaveDialogOpened:u,favorites:v,chartList:m,chartId:null!==r?r:void 0,dataNameSaveMenu:i?void 0:"save-load-menu",showRemoveFavoriteHint:!i,onCloneChart:this._handleClickClone,onSaveChart:this._handleClickSave,onSaveChartFromMenu:this._handleClickSaveFromMenu,onRenameChart:this._handleClickRename,onSaveAsChart:this._handleClickSaveAs,onLoadChart:this._handleClickLoad,onButtonClick:this._handleClickLoad,onAdditionalButtonClick:this._handleAdditionalButton};return n.createElement(_.MatchMedia,{rule:y.DialogBreakpoints.TabletSmall},(e=>n.createElement(Mt,{...g,isTabletSmall:e})))}_onLoginStateChange(){const e=window.is_authenticated;this.setState({isAuthenticated:e}),vt.showFavoriteLayouts&&this.context.loadChartService.refreshChartList()}_trackEvent(e){0}}xt.contextType=Et;var Tt=a(7986),It=a(52388),Rt=a(13665);const At=new It.DateTimeFormatter({dateTimeSeparator:"_",timeFormat:"%h-%m-%s"}),Nt={takeSnapshot:l.t(null,void 0,a(87972))},Ft=(0,f.registryContextType)();function Ht(e,t,i){return async function(e,t,i){const n=URL.createObjectURL(new Blob([`<!doctype html><html style="background-color:${getComputedStyle(document.documentElement).backgroundColor}"><head><meta charset="utf-8"><title>${l.t(null,void 0,a(93292))}</title></head><body style="background-color:${getComputedStyle(document.body).backgroundColor}"></body></html>`],{type:"text/html"}));try{const a=open(n,t,i);if(!a)throw new Error("cound not open a new tab");const o=await e.catch((()=>{}));void 0!==o?a.location.replace(o):a.close()}finally{URL.revokeObjectURL(n)}}(e,t,i)}var Ot=a(65446),Ut=a(7372),Dt=a(36531);function Lt(e){const t=u(e.isLoading&&Dt.hidden),a=u(!e.isLoading&&Dt.hidden);return n.createElement("div",null,n.createElement("span",{className:t},e.children),n.createElement("span",{className:a},n.createElement(ue.Loader,null)))}var Wt=a(76974),Pt=a(67487),Bt=a(1457),Vt=a(23595),zt=a(29414),Zt=a(99280),Kt=a(22604);const qt=(0,rt.mergeThemes)(oe.DEFAULT_POPUP_MENU_ITEM_THEME,Kt);function Qt(e){const{serverSnapshot:t,clientSnapshot:i,hideShortcuts:o}=e,[s,r]=(0,n.useState)(!1),[h,c]=(0,n.useState)(!1),[d,v]=(0,n.useState)(!1),[m,p]=(0,n.useState)(!1),[g,b]=(0,n.useState)(!1),C=(0,Wt.useIsMounted)(),S=(0,n.useCallback)((async()=>{var e;const t=i(),a=t.then((e=>new Promise((t=>e.canvas.toBlob((e=>{null!==e&&t(e)}))))));try{await(0,Ot.writePromiseUsingApi)(a,"image/png"),ge.emit("onClientScreenshotCopiedToClipboard")}catch(a){const{canvas:i}=await t;null===(e=window.open())||void 0===e||e.document.write(`<img width="100%" src="${i.toDataURL()}"/>`)}}),[i]),_=(0,
n.useCallback)((async()=>{const e=await i(),t=await function(e){return new Promise((t=>{try{e.canvas.toBlob((e=>{if(null===e)throw new Error("Unable to generate blob");t(URL.createObjectURL(e))}))}catch(a){t(e.canvas.toDataURL())}}))}(e);t&&(0,Ut.downloadFile)(`${e.name}.png`,t)}),[i]),y=e=>Ht(e.then((e=>e.imageUrl))),w=(0,n.useCallback)((async(e=!1)=>{const a=t();try{if(e)await y(a);else{const e=a.then((e=>new Blob([e.imageUrl],{type:"text/plain"})));await(0,Ot.writePromiseUsingApi)(e,"text/plain"),ge.emit("onServerScreenshotCopiedToClipboard")}return!0}catch(e){return y(a),!0}finally{C.current&&(c(!1),r(!1),(0,De.globalCloseMenu)())}}),[t]),f=((0,n.useCallback)((async()=>{const e=t();try{const t=720,a=e.then((async e=>{var a,i,n;const o=await snapshoter().getSnapshot(e.symbol,new Set(["description"])),s="error"!==o.status?o.values.description:e.symbol,l=null!==(i=null===(a=e.imageUrl.match(/\/x\/([0-9a-zA-Z]{8})/))||void 0===a?void 0:a[1])&&void 0!==i?i:"",r=null===(n=createSnapshotImageUrls(l))||void 0===n?void 0:n.url;return new Blob([`<img width="${t}" loading="lazy" src="${r}"/><p><a href="https://www.tradingview.com${getSymbolPagePath({shortName:e.symbol})}">${s} chart</a> by TradingView</p>`],{type:"text/plain"})}));return await(0,Ot.writePromiseUsingApi)(a,"text/plain"),ge.emit("onServerScreenshotEmbedCodeCopiedToClipboard"),!0}catch(t){return y(e),!0}finally{C.current&&(v(!1),(0,De.globalCloseMenu)())}}),[t]),(0,n.useCallback)((async()=>{p(!0);const[e,i]=await Promise.all([a.e(4665).then(a.bind(a,66921)),t()]);e.Twitter.shareSnapshotInstantly(i.symbol,i.imageUrl),C.current&&(p(!1),(0,De.globalCloseMenu)())}),[t]));return n.createElement(n.Fragment,null,n.createElement(pe.ToolWidgetMenuSummary,null,l.t(null,void 0,a(58425))),n.createElement(R.AccessibleMenuItem,{"data-name":"save-chart-image",label:l.t(null,void 0,a(26200)),icon:Vt,onClick:_,shortcut:o?void 0:(0,$.humanReadableHash)($.Modifiers.Mod+$.Modifiers.Alt+83),theme:qt}),n.createElement(R.AccessibleMenuItem,{"data-name":"copy-chart-image",label:l.t(null,void 0,a(96554)),icon:Bt,onClick:S,shortcut:o?void 0:(0,$.humanReadableHash)($.Modifiers.Mod+$.Modifiers.Shift+83),theme:qt}),!(0,lt.onWidget)()&&n.createElement(R.AccessibleMenuItem,{"data-name":"copy-link-to-the-chart-image",label:n.createElement(Lt,{isLoading:s},l.t(null,void 0,a(81939))),icon:zt,onClick:()=>{r(!0),w(!1)},dontClosePopup:!0,isDisabled:s,shortcut:o?void 0:(0,$.humanReadableHash)($.Modifiers.Alt+83),className:u(s&&Kt.loading),theme:qt}),!1,!(0,lt.onWidget)()&&!(0,lt.isSymphonyEmbed)()&&n.createElement(R.AccessibleMenuItem,{"data-name":"open-image-in-new-tab",label:n.createElement(Lt,{isLoading:h},l.t(null,void 0,a(19159))),icon:Zt,onClick:()=>{c(!0),w(!0)},dontClosePopup:!0,isDisabled:h,className:u(h&&Kt.loading),theme:qt}),!(0,lt.onWidget)()&&!(0,lt.isSymphonyEmbed)()&&n.createElement(R.AccessibleMenuItem,{"data-name":"tweet-chart-image",label:n.createElement(Lt,{isLoading:m},l.t(null,void 0,a(28239))),icon:Pt,onClick:f,dontClosePopup:!0,isDisabled:m,className:u(m&&Kt.loading),
theme:qt}),!1)}var Gt=a(84015);function jt(e){const[t,a]=(0,n.useState)(!1),i=(0,Wt.useIsMounted)(),o=(0,n.useCallback)((async()=>{a(!0),await e.serverSnapshot(),i.current&&a(!1)}),[e.serverSnapshot]);return n.createElement(W.ToolWidgetButton,{id:e.id,className:e.className,isDisabled:t,onClick:o,title:e.tooltip,icon:e.icon})}var Xt=a(72644);const $t=(Jt=function(e){return(0,Gt.isOnMobileAppPage)("any")?n.createElement(jt,{...e,icon:Xt}):n.createElement(d.ToolbarMenuButton,{content:n.createElement(W.ToolWidgetButton,{tag:"div",id:e.id,className:e.className,icon:Xt}),drawerPosition:"Bottom",drawerBreakpoint:y.DialogBreakpoints.TabletSmall,arrow:!1,onClick:function(){},tooltip:e.tooltip},n.createElement(Qt,{...e}))},(Yt=class extends n.PureComponent{constructor(e,t){super(e,t),this._clientSnapshot=async()=>{const e=this.context.chartWidgetCollection.activeChartWidget.value().model().mainSeries().actualSymbol();return{canvas:await this.context.chartWidgetCollection.clientSnapshot(),name:`${(0,Rt.shortName)(e)}_${At.formatLocal(new Date)}`}},this._serverSnapshot=async()=>{const e=this.context.chartWidgetCollection.activeChartWidget.value().model().mainSeries().actualSymbol(),t=await this.context.chartWidgetCollection.takeScreenshot(),a=i.enabled("charting_library_base")&&void 0!==this.context.snapshotUrl?t:(0,Tt.convertImageNameToUrl)(t);return{symbol:(0,Rt.shortName)(e),imageUrl:a}},(0,f.validateRegistry)(t,{chartWidgetCollection:o.any.isRequired})}render(){const{className:e,id:t}=this.props;return n.createElement(Jt,{id:t,className:e,tooltip:Nt.takeSnapshot,serverSnapshot:this._serverSnapshot,clientSnapshot:this._clientSnapshot})}}).contextType=Ft,Yt);var Jt,Yt,ea=a(79652),ta=a(39362),aa=a(50340);class ia{async show(e){if(null!==ia._provider){const e=await ia._provider.getSymbol();return r.linking.setSymbolAndLogInitiator(e.symbol,"symbol search UI"),e}if(ia._currentShowingInstance)throw new DOMException("SymbolSearchUI is already shown","InvalidStateError");try{ia._currentShowingInstance=this,ia.preload();const t=await ia._implementation;return(0,ne.assert)(null!==t),new Promise((a=>{t.showDefaultSearchDialog({...e,onSearchComplete:e=>{a({symbol:e})}})}))}finally{ia._currentShowingInstance=null}}static setProvider(e){this._provider=e}static preload(){null===this._provider&&null===this._implementation&&(this._implementation=(0,aa.loadNewSymbolSearch)())}}ia._currentShowingInstance=null,ia._provider=null,ia._implementation=null;var na=a(29142),oa=a(30399);const sa=(0,rt.mergeThemes)(g.DEFAULT_TOOLBAR_BUTTON_THEME,oa);class la extends n.PureComponent{constructor(e){super(e),this._openSymbolSearchDialog=async e=>{if((0,$.modifiersFromEvent)(e)!==$.Modifiers.Alt){if(!this.state.isOpened)try{(0,Z.trackEvent)("GUI","SS","main search"),await(new ia).show({trackResultsOptions:{trackResults:!1,emptySearchType:"empty_result__supercharts"},onClose:()=>{this.setState({isOpened:!1})},onOpen:()=>{this.setState({isOpened:!0})},shouldReturnFocus:(0,K.isKeyboardClick)(e),
defaultValue:this._isSpread(this.state.symbol)?this.state.symbol:this.state.shortName,showSpreadActions:(0,ea.canShowSpreadActions)()&&this.props.isActionsVisible,source:"searchBar",footer:Ie.mobiletouch?void 0:n.createElement(ta.SymbolSearchDialogFooter,null,l.t(null,void 0,a(50021)))})}catch(e){}}else(0,st.getClipboard)().writeText(this.state.symbol)},this._isSpread=e=>!1,this._onSymbolChanged=()=>{const e=r.linking.proSymbol.value();this.setState({symbol:e,shortName:ra()})},this.state={symbol:r.linking.proSymbol.value(),shortName:ra(),isOpened:!1}}componentDidMount(){r.linking.proSymbol.subscribe(this._onSymbolChanged),r.linking.seriesShortSymbol.subscribe(this._onSymbolChanged),ia.preload()}componentWillUnmount(){r.linking.proSymbol.unsubscribe(this._onSymbolChanged),r.linking.seriesShortSymbol.unsubscribe(this._onSymbolChanged)}render(){const{id:e,className:t}=this.props;return n.createElement(g.ToolbarButton,{id:e,className:v()(t,i.enabled("uppercase_instrument_names")&&oa.uppercase,oa.smallLeftPadding),theme:sa,icon:na,isOpened:this.state.isOpened,text:this.state.shortName,onClick:this._openSymbolSearchDialog,tooltip:l.t(null,void 0,a(51165))})}async _updateQuotes(e){}}function ra(){return r.linking.seriesShortSymbol.value()||r.linking.proSymbol.value()||""}var ha=a(89452);function ca(e){return n.createElement("div",{className:u(ha.description,e.className)},e.children)}var da=a(76032),ua=a(37926);const va=(0,rt.mergeThemes)(oe.DEFAULT_POPUP_MENU_ITEM_THEME,{labelRow:ua.labelRow,toolbox:ua.toolbox,item:ua.titleItem}),ma=(0,rt.mergeThemes)(oe.DEFAULT_POPUP_MENU_ITEM_THEME,{labelRow:ua.labelRow,toolbox:ua.toolbox,item:ua.titleItemTabletSmall}),pa=(0,rt.mergeThemes)(oe.DEFAULT_POPUP_MENU_ITEM_THEME,{item:ua.item}),ga=(0,rt.mergeThemes)(oe.DEFAULT_POPUP_MENU_ITEM_THEME,{item:ua.itemTabletSmall});function ba(e){const{className:t,item:a,onApply:i,onRemove:o,onFavor:s,favorite:l,isFavoritingAllowed:r,isTabletSmall:h}=e,[c,d]=(0,Te.useHover)(),u=a.meta_info,m=u?(0,da.getStudyTemplateDescString)(u.indicators):void 0,p=h?ma:va,g=h?ga:pa,b=(0,n.useCallback)((()=>i(a)),[i,a]),C=(0,n.useCallback)((e=>o(a,e)),[o,a]),S=(0,n.useCallback)((()=>{s&&s(a)}),[s,a]);return n.createElement("div",{...d,className:v()(t,ua.wrap),"data-name":a.name,"data-id":a.id,"data-is-default":Boolean(a.is_default)},n.createElement(R.AccessibleMenuItem,{theme:p,label:a.name,labelRowClassName:v()(h&&ua.itemLabelTabletSmall),isHovered:c,showToolboxOnHover:!l&&!c,showToolboxOnFocus:!0,onClick:b,toolbox:n.createElement(n.Fragment,null,!a.is_default&&n.createElement(Re.MenuRemoveButton,{key:"remove",hidden:!Ie.touch&&!c,onClick:C,className:ua.remove}),Boolean(s)&&r&&n.createElement(A.MenuFavoriteButton,{key:"favorite",isFilled:Boolean(l),onClick:S}))}),m&&n.createElement(oe.PopupMenuItem,{theme:g,label:n.createElement(ca,{className:v()(ua.description,h&&ua.descriptionTabletSmall)},m),onClick:b,isHovered:c}))}var Ca=a(53707),Sa=a(82574);const _a=(0,rt.mergeThemes)(oe.DEFAULT_POPUP_MENU_ITEM_THEME,Sa);function ya(e){
const{onClick:t,isTabletSmall:i,className:o}=e;return n.createElement(R.AccessibleMenuItem,{theme:_a,className:v()(o,Sa.wrap),label:n.createElement("div",{className:Sa.titleWrap},n.createElement("div",{className:v()(Sa.title,i&&Sa.titleTabletSmall)},n.createElement(s.Icon,{className:Sa.icon,icon:Ca}),n.createElement("div",{className:Sa.text},(0,Be.appendEllipsis)(l.t(null,void 0,a(26869)))))),onClick:t})}var wa=a(36947),fa=a(64706);const ka=n.createContext(null);var Ma=a(10222);function Ea(e){const{templates:t,favorites:a,onTemplateSave:i,onTemplateRemove:o,onTemplateSelect:s,onTemplateFavorite:l,isTabletSmall:r,isLoading:h}=e,c=(0,n.useMemo)((()=>t.filter((e=>e.is_default))),[t]),d=(0,n.useMemo)((()=>t.filter((e=>!e.is_default))),[t]),u=(0,n.useMemo)((()=>new Set(a.map((e=>e.name)))),[a]),m=(0,n.useContext)(ka),p=(0,n.useContext)(fa.MenuContext),g=(0,wa.useForceUpdate)();(0,n.useEffect)((()=>{if(null!==m){const e={};return m.getOnChange().subscribe(e,(()=>{g(),p&&p.update()})),()=>m.getOnChange().unsubscribeAll(e)}return()=>{}}),[]);const b=e=>n.createElement(ba,{key:e.name,item:e,isFavoritingAllowed:Boolean(l),favorite:u.has(e.name),onApply:s,onFavor:l,onRemove:o,isTabletSmall:r});return n.createElement("div",{className:v()(Ma.menu,r&&Ma.menuSmallTablet)},n.createElement(ya,{onClick:i,isTabletSmall:r}),h&&n.createElement(n.Fragment,null,n.createElement(S.PopupMenuSeparator,null),n.createElement(me,null)),!h&&(r?n.createElement(xa,{defaults:c,customs:d,render:b}):n.createElement(Ta,{defaults:c,customs:d,render:b,state:m})))}function xa(e){const{defaults:t,customs:i,render:o}=e;return n.createElement(n.Fragment,null,i.length>0&&n.createElement(n.Fragment,null,n.createElement(S.PopupMenuSeparator,null),n.createElement(pe.ToolWidgetMenuSummary,{className:Ma.menuItemHeaderTabletSmall},l.t(null,void 0,a(83300))),i.map(o)),t.length>0&&n.createElement(n.Fragment,null,n.createElement(S.PopupMenuSeparator,null),n.createElement(pe.ToolWidgetMenuSummary,{className:Ma.menuItemHeaderTabletSmall},l.t(null,void 0,a(46838))),t.map(o)))}function Ta(e){const{defaults:t,customs:i,render:o,state:s}=e;return n.createElement(n.Fragment,null,i.length>0&&n.createElement(n.Fragment,null,n.createElement(S.PopupMenuSeparator,null),n.createElement(pe.ToolWidgetMenuSummary,{className:Ma.menuItemHeader},l.t(null,void 0,a(83300))),i.map(o)),i.length>0&&t.length>0&&s&&n.createElement(n.Fragment,null,n.createElement(S.PopupMenuSeparator,null),n.createElement(Pe,{summary:l.t(null,void 0,a(46838)),open:!s.get().defaultsCollapsed,onStateChange:e=>s.set({defaultsCollapsed:!e})},t.map(o))),0===i.length&&t.length>0&&n.createElement(n.Fragment,null,n.createElement(S.PopupMenuSeparator,null),n.createElement(pe.ToolWidgetMenuSummary,{className:Ma.menuItemHeader},l.t(null,void 0,a(46838))),t.map(o)))}var Ia=a(64147),Ra=a(39076);class Aa{constructor(e){this._isFavoriteEnabled=i.enabled("items_favoriting"),this.handleFavorTemplate=e=>{if(!this._isFavoriteEnabled)return;const{id:t,is_default:a,favorite_date:i}=e
;null!==i?this._removeFavoriteTemplate(t,a):this._addFavoriteTemplate(t,a)},this.handleDropdownOpen=()=>{this._setState({isLoading:!0}),this._studyTemplates.invalidate(),this._studyTemplates.refreshStudyTemplateList((()=>this._setState({isLoading:!1})))},this.handleApplyTemplate=e=>{this._studyTemplates.applyTemplate(e.name)},this.handleRemoveTemplate=(e,t)=>{this._studyTemplates.deleteStudyTemplate(e.name,t)},this.handleSaveTemplate=()=>{this._studyTemplates.showSaveAsDialog()},this._studyTemplates=e;const t=this._studyTemplates.list();this._state=new Ia.WatchedValue({isLoading:!1,studyTemplatesList:t}),this._studyTemplates.getOnChange().subscribe(this,this._handleTemplatesChange),this._studyTemplates.refreshStudyTemplateList()}destroy(){this._studyTemplates.getOnChange().unsubscribe(this,this._handleTemplatesChange)}state(){return this._state.readonly()}getSortedFavoritesStudyTemplates(e){return e.filter((e=>null!==e.favorite_date)).sort(((e,t)=>null!==e.favorite_date&&null!==t.favorite_date&&e.favorite_date>t.favorite_date?1:-1))}_setState(e){this._state.setValue({...this._state.value(),...e})}_handleTemplatesChange(){this._setState({studyTemplatesList:this._studyTemplates.list()})}_removeFavoriteTemplate(e,t){const a=t?Ra.backend.favorStandardStudyTemplate:Ra.backend.favorStudyTemplate;this._replaceOldFavoriteStudyTemplate(e,null,t),a(e,!1)}_addFavoriteTemplate(e,t){const a=this.getSortedFavoritesStudyTemplates(this._state.value().studyTemplatesList),i=a.length?a[a.length-1].favorite_date:0,n=window.is_authenticated?Date.now():i+1,o=t?Ra.backend.favorStandardStudyTemplate:Ra.backend.favorStudyTemplate;this._replaceOldFavoriteStudyTemplate(e,n,t),o(e,!0)}_replaceOldFavoriteStudyTemplate(e,t,a){const i=this._state.value().studyTemplatesList.map((i=>i.id===e&&i.is_default===a?{...i,favorite_date:t}:i));this._setState({studyTemplatesList:i})}}var Na=a(49406),Fa=a(97169);function Ha(e){var t;const{className:a,name:i,onApply:o,isActive:s,href:l,target:r}=e,[h,c]=(0,L.useRovingTabindexElement)(null),d=l?"a":o?"button":"div";return n.createElement(d,{ref:h,type:o&&!l?"button":void 0,className:u(a,Fa.item,o&&Fa.accessible,"apply-common-tooltip"),onClick:function(e){e.stopPropagation(),null==o||o()},"data-tooltip":i,"aria-label":o?i:void 0,tabIndex:c,href:"a"===d?l:void 0,target:"a"===d?r:void 0},n.createElement("div",{className:u(Fa.round,s&&Fa.active)},null!==(t=(0,Na.getFirstSegmentOrCodePointString)(i))&&void 0!==t?t:" "))}var Oa=a(21233),Ua=a(71232);const Da=(0,f.registryContextType)();class La extends n.PureComponent{constructor(e,t){super(e,t),this._updateState=e=>{this.setState({...e,isActive:this.state.isActive})},this._handleApplyTemplate=e=>{this._handleClose(),this._model.handleApplyTemplate(e)},this._handleRemoveTemplate=(e,t)=>{this._handleClose(),this._model.handleRemoveTemplate(e,t)},this._handleClose=()=>{this._handleToggleDropdown(!1)},this._handleToggleDropdown=e=>{const{isActive:t}=this.state,a="boolean"==typeof e?e:!t;this.setState({isActive:a})},(0,f.validateRegistry)(t,{
studyTemplates:o.any.isRequired,templatesMenuViewStateService:o.any});const{studyTemplates:a}=t;this._model=new Aa(a),this.state={...this._model.state().value(),isActive:!1}}componentDidMount(){this._model.state().subscribe(this._updateState)}componentWillUnmount(){this._model.state().unsubscribe(this._updateState),this._model.destroy()}render(){const{studyTemplatesList:e}=this.state,{isShownQuicks:t,className:a,displayMode:i,id:o}=this.props,s=this._model.getSortedFavoritesStudyTemplates(e);return n.createElement(ka.Provider,{value:this.context.templatesMenuViewStateService||null},n.createElement(Wa,{id:o,className:a,mode:i,templates:e,favorites:s,onMenuOpen:this._model.handleDropdownOpen,onTemplateFavorite:t?this._model.handleFavorTemplate:void 0,onTemplateSelect:this._handleApplyTemplate,onTemplateRemove:this._handleRemoveTemplate,onTemplateSave:this._model.handleSaveTemplate}))}}function Wa(e){const{id:t,className:i,mode:o,templates:s,favorites:r,isMenuOpen:h,onTemplateSelect:c,onTemplateSave:u,onTemplateFavorite:m,onTemplateRemove:g}=e,b=(0,n.useRef)(null),C=(0,n.useRef)(null),S=v()(i,Ua.wrap,{[Ua.full]:"full"===o,[Ua.medium]:"medium"===o}),w="small"!==o&&m&&r.length>0;return n.createElement(p,{id:t,className:S},n.createElement(_.MatchMedia,{rule:y.DialogBreakpoints.TabletSmall},(t=>n.createElement(d.ToolbarMenuButton,{ref:b,menuReference:C,onOpen:e.onMenuOpen,isDrawer:t,drawerPosition:"Bottom",arrow:!1,content:n.createElement(V,{tag:"div",className:v()(w&&Ua.buttonWithFavorites),displayMode:o,isOpened:h,icon:Oa,forceInteractive:!0,collapseWhen:["full","medium","small"]}),onClick:k,tooltip:l.t(null,void 0,a(53395))},n.createElement(Ea,{onTemplateSave:u,onTemplateSelect:c,onTemplateRemove:f,onTemplateFavorite:m,templates:s,favorites:r,isTabletSmall:t})))),w&&n.createElement(Pa,{favorites:r,onTemplateSelect:function(e){c(e),k()}}));function f(e,t){if(t&&(0,K.isKeyboardClick)(t)&&C.current){const t=(0,se.queryMenuElements)(C.current),a=t.findIndex((t=>null!==t.closest(`[data-id="${e.id}"]`)));g(e,(()=>{var e,i;if(-1!==a&&C.current){const n=null!==(e=t[a+1])&&void 0!==e?e:t[a-1];n?n.focus():(0,se.focusFirstMenuItem)(C.current),null===(i=b.current)||void 0===i||i.update()}}))}else g(e)}function k(){0}}function Pa(e){return n.createElement(n.Fragment,null,e.favorites.map(((t,a,i)=>n.createElement(Ha,{key:t.name,name:t.name,onApply:()=>e.onTemplateSelect(t),className:v()({[Ua.first]:0===a,[Ua.last]:a===i.length-1})}))))}La.contextType=Da;a(21251);var Ba=a(77665),Va=a(96052),za=a(4417);const Za={undoHotKey:(0,X.hotKeySerialize)({keys:[(0,$.humanReadableModifiers)($.Modifiers.Mod,!1),"Z"],text:"{0} + {1}"}),redoHotKey:(0,X.hotKeySerialize)({keys:[(0,$.humanReadableModifiers)($.Modifiers.Mod,!1),"Y"],text:"{0} + {1}"})},Ka=(0,rt.mergeThemes)(g.DEFAULT_TOOLBAR_BUTTON_THEME,za),qa=(0,f.registryContextType)();class Qa extends n.PureComponent{constructor(e,t){super(e,t),this._batched=null,this._handleClickUndo=()=>{(0,Z.trackEvent)("GUI","Undo");const{chartWidgetCollection:e}=this.context;e.undoHistory.undo()},
this._handleClickRedo=()=>{(0,Z.trackEvent)("GUI","Redo");const{chartWidgetCollection:e}=this.context;e.undoHistory.redo()},(0,f.validateRegistry)(t,{chartWidgetCollection:o.any.isRequired}),this.state=this._getStateFromUndoHistory()}componentDidMount(){const{chartWidgetCollection:e}=this.context;e.undoHistory.redoStack().onChange().subscribe(this,this._onChangeStack),e.undoHistory.undoStack().onChange().subscribe(this,this._onChangeStack)}componentWillUnmount(){const{chartWidgetCollection:e}=this.context;e.undoHistory.redoStack().onChange().unsubscribe(this,this._onChangeStack),e.undoHistory.undoStack().onChange().unsubscribe(this,this._onChangeStack),this._batched=null}render(){const{id:e}=this.props,{isEnabledRedo:t,isEnabledUndo:i,redoStack:o,undoStack:s}=this.state;return n.createElement(p,{id:e},n.createElement(g.ToolbarButton,{icon:Ba,isDisabled:!i,onClick:this._handleClickUndo,theme:Ka,tooltip:i?l.t(null,{replace:{hint:s}},a(66020)):void 0,"data-tooltip-hotkey":i?Za.undoHotKey:void 0}),n.createElement(g.ToolbarButton,{icon:Va,isDisabled:!t,onClick:this._handleClickRedo,theme:Ka,tooltip:t?l.t(null,{replace:{hint:o}},a(11304)):void 0,"data-tooltip-hotkey":t?Za.redoHotKey:void 0}))}_onChangeStack(){null===this._batched&&(this._batched=Promise.resolve().then((()=>{if(null===this._batched)return;this._batched=null;const e=this._getStateFromUndoHistory();this.setState(e)})))}_getStateFromUndoHistory(){const{chartWidgetCollection:e}=this.context,t=e.undoHistory.undoStack(),a=e.undoHistory.redoStack(),i=a.head(),n=t.head();return{isEnabledRedo:!a.isEmpty(),isEnabledUndo:!t.isEmpty(),redoStack:i?i.text().translatedText():"",undoStack:n?n.text().translatedText():""}}}Qa.contextType=qa;const Ga=e=>{if((e=>"http://www.w3.org/1999/xhtml"===(null==e?void 0:e.namespaceURI))(e)&&"true"!==e.dataset.internalAllowKeyboardNavigation){e.tabIndex=-1,e.ariaDisabled="true";for(let t=0;t<e.children.length;t++)Ga(e.children.item(t))}};class ja extends n.PureComponent{constructor(){super(...arguments),this._wrapperElement=null,this._resizeObserver=null,this._mutationObserver=null,this._update=()=>{this.forceUpdate()},this._setRef=e=>{this._wrapperElement=e},this._handleMeasure=([e])=>{this.props.width.setValue(e.contentRect.width)},this._handleMutation=([e])=>{"childList"===e.type&&Ga(this.props.element)}}componentDidMount(){const{element:e,isFake:t,width:a}=this.props;!t&&this._wrapperElement?(this._resizeObserver=new ResizeObserver(this._handleMeasure),this._mutationObserver=new MutationObserver(this._handleMutation),this._wrapperElement.appendChild(e),this._resizeObserver.observe(this._wrapperElement),this._mutationObserver.observe(e,{subtree:!0,childList:!0})):a.subscribe(this._update)}componentWillUnmount(){const{width:e,isFake:t}=this.props;t&&e.unsubscribe(this._update),this._resizeObserver&&this._wrapperElement&&this._resizeObserver.unobserve(this._wrapperElement),this._mutationObserver&&(this._mutationObserver.disconnect(),this._mutationObserver=null)}render(){const{isFake:e=!1,width:t}=this.props;return n.createElement(p,{
ref:this._setRef,style:e?{width:t.value()}:void 0,"data-is-custom-header-element":!0})}}function Xa(e){const{displayMode:t,params:a}=e;return n.createElement(d.ToolbarMenuButton,{content:n.createElement(V,{collapseWhen:void 0!==a.icon?void 0:[],displayMode:t,icon:a.icon,text:a.title,"data-name":"dropdown","data-is-custom-header-element":!0}),drawerPosition:"Bottom",drawerBreakpoint:y.DialogBreakpoints.TabletSmall,arrow:!1,tooltip:a.tooltip},a.items.map(((e,t)=>n.createElement(oe.PopupMenuItem,{key:t,label:e.title,onClick:()=>e.onSelect(),"data-name":"dropdown-item"}))))}var $a=a(39351);function Ja(e){const{className:t,title:a,...i}=e;return n.createElement(z,{...i,className:u(t,$a.customTradingViewStyleButton,$a.withoutIcon),collapseWhen:[],"data-name":"custom-tradingview-styled-button",tooltip:a})}var Ya=a(60448);const ei=(0,X.hotKeySerialize)({keys:[(0,$.humanReadableModifiers)($.Modifiers.Mod,!1),"K"],text:"{0} + {1}"}),ti=(0,f.registryContextType)();class ai extends n.PureComponent{constructor(e,t){super(e),this._dialog=null,this._updateState=e=>{this.setState({isOpened:e})},this._handleClick=e=>{const{openGlobalSearch:t}=this.context;t({shouldReturnFocus:(0,K.isKeyboardClick)(e)}).then((e=>{var t;null===(t=this._dialog)||void 0===t||t.visible().unsubscribe(this._updateState),this._dialog=e,e.visible().subscribe(this._updateState)}))},(0,f.validateRegistry)(t,{openGlobalSearch:o.any.isRequired}),this.state={isOpened:!1}}componentWillUnmount(){var e;null===(e=this._dialog)||void 0===e||e.visible().unsubscribe(this._updateState)}render(){return n.createElement(j.ToolbarIconButton,{...this.props,icon:Ya,isOpened:this.state.isOpened,onClick:this._handleClick,"data-tooltip-hotkey":ei,tooltip:l.t(null,void 0,a(18243))})}}ai.contextType=ti;var ii=a(27514),ni=a(27906);function oi(){return{Bars:i.enabled("header_chart_type")?D:void 0,Compare:i.enabled("header_compare")?G:void 0,Custom:ja,CustomTradingViewStyledButton:Ja,Fullscreen:(0,ni.shouldShowFullscreen)()?ie:void 0,Indicators:i.enabled("header_indicators")?ye:void 0,Intervals:i.enabled("header_resolutions")?je:void 0,OpenPopup:tt,Properties:i.enabled("header_settings")&&i.enabled("show_chart_property_page")?nt:void 0,SaveLoad:i.enabled("header_saveload")?xt:void 0,Screenshot:i.enabled("header_screenshot")?$t:void 0,SymbolSearch:i.enabled("header_symbol_search")?la:void 0,Templates:i.enabled("study_templates")?La:void 0,Dropdown:Xa,UndoRedo:i.enabled("header_undo_redo")?Qa:void 0,Layout:undefined,QuickSearch:(0,ii.shouldShowQuickSearchOnLib)()?ai:void 0}}},27906:(e,t,a)=>{"use strict";a.d(t,{shouldShowFullscreen:()=>n});var i=a(56570);function n(){return i.enabled("header_fullscreen_button")}},27830:(e,t,a)=>{"use strict";a.d(t,{showFavoriteLayouts:()=>i});const i=!1},23076:(e,t,a)=>{"use strict";a.r(t),a.d(t,{SERIES_ICONS:()=>p});var i=a(94670),n=a(32162),o=a(39956),s=a(14083),l=a(45504),r=a(52867),h=a(41473),c=a(31246),d=a(15726),u=a(24464),v=a(71705),m=a(9450);const p={3:i,16:n,0:o,1:s,8:l,9:r,2:h,14:c,15:d,10:u,12:v,13:m}},7986:(e,t,a)=>{"use strict";a.d(t,{
convertImageNameToUrl:()=>n});var i=a(56570);function n(e){return i.enabled("charting_library_base")?"https://www.tradingview.com/x/"+e+"/":window.location.protocol+"//"+window.location.host+"/x/"+e+"/"}},7372:(e,t,a)=>{"use strict";function i(e,t){const a=document.createElement("a");a.style.display="none",a.href=t,a.download=e,a.click()}a.d(t,{downloadFile:()=>i})},29142:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M3.5 8a4.5 4.5 0 1 1 9 0 4.5 4.5 0 0 1-9 0ZM8 2a6 6 0 1 0 3.65 10.76l3.58 3.58 1.06-1.06-3.57-3.57A6 6 0 0 0 8 2Z"/></svg>'},97268:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M8.5 6A2.5 2.5 0 0 0 6 8.5V11h1V8.5C7 7.67 7.67 7 8.5 7H11V6H8.5zM6 17v2.5A2.5 2.5 0 0 0 8.5 22H11v-1H8.5A1.5 1.5 0 0 1 7 19.5V17H6zM19.5 7H17V6h2.5A2.5 2.5 0 0 1 22 8.5V11h-1V8.5c0-.83-.67-1.5-1.5-1.5zM22 19.5V17h-1v2.5c0 .83-.67 1.5-1.5 1.5H17v1h2.5a2.5 2.5 0 0 0 2.5-2.5z"/></svg>'},36992:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M17 6v2.5a2.5 2.5 0 0 0 2.5 2.5H22v-1h-2.5A1.5 1.5 0 0 1 18 8.5V6h-1zm2.5 11a2.5 2.5 0 0 0-2.5 2.5V22h1v-2.5c0-.83.67-1.5 1.5-1.5H22v-1h-2.5zm-11 1H6v-1h2.5a2.5 2.5 0 0 1 2.5 2.5V22h-1v-2.5c0-.83-.67-1.5-1.5-1.5zM11 8.5V6h-1v2.5c0 .83-.67 1.5-1.5 1.5H6v1h2.5A2.5 2.5 0 0 0 11 8.5z"/></svg>'},6198:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M13.9 14.1V22h1.2v-7.9H23v-1.2h-7.9V5h-1.2v7.9H6v1.2h7.9Z"/></svg>'},99280:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M8.5 6A2.5 2.5 0 0 0 6 8.5v11A2.5 2.5 0 0 0 8.5 22h11a2.5 2.5 0 0 0 2.5-2.5v-3h-1v3c0 .83-.67 1.5-1.5 1.5h-11A1.5 1.5 0 0 1 7 19.5v-11C7 7.67 7.67 7 8.5 7h3V6h-3zm7 1h4.8l-7.49 7.48.71.7L21 7.72v4.79h1V6h-6.5v1z"/></svg>'},60448:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M15 11v4l1-1.5 2.33-3.5.67-1h-3V4l-1 1.5L12.67 9 12 10h3v1Zm2-7v4h2a1 1 0 0 1 .83 1.55l-4 6A1 1 0 0 1 14 15v-4h-2a1 1 0 0 1-.83-1.56l4-6A1 1 0 0 1 17 4ZM5 13.5a7.5 7.5 0 0 1 6-7.35v1.02A6.5 6.5 0 1 0 18.98 13h1a7.6 7.6 0 0 1-1.83 5.44l4.7 4.7-.7.71-4.71-4.7A7.5 7.5 0 0 1 5 13.5Z"/></svg>'},21233:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="M8 7h3a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1H8a1 1 0 0 1-1-1V8a1 1 0 0 1 1-1zM6 8c0-1.1.9-2 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H8a2 2 0 0 1-2-2V8zm11-1h3a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1h-3a1 1 0 0 1-1-1V8a1 1 0 0 1 1-1zm-2 1c0-1.1.9-2 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2h-3a2 2 0 0 1-2-2V8zm-4 8H8a1 1 0 0 0-1 1v3a1 1 0 0 0 1 1h3a1 1 0 0 0 1-1v-3a1 1 0 0 0-1-1zm-3-1a2 2 0 0 0-2 2v3c0 1.1.9 2 2 2h3a2 2 0 0 0 2-2v-3a2 2 0 0 0-2-2H8zm9 1h3a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1h-3a1 1 0 0 1-1-1v-3a1 1 0 0 1 1-1zm-2 1c0-1.1.9-2 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2h-3a2 2 0 0 1-2-2v-3z"/></svg>'},94670:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="m25.35 5.35-9.5 9.5-.35.36-.35-.36-4.65-4.64-8.15 8.14-.7-.7 8.5-8.5.35-.36.35.36 4.65 4.64 9.15-9.14.7.7ZM2 21h1v1H2v-1Zm2-1H3v1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h-1v-1h1v-1h-1v-1h1v-1h-1v-1h1v-1h-1v-1h1v-1h-1v-1h1v-1h-1v-1h1V9h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v-1h-1v-1h-1v-1h-1v-1h-1v-1h-1v1H9v1H8v1H7v1H6v1H5v1H4v1Zm1 0v1H4v-1h1Zm1 0H5v-1h1v1Zm1 0v1H6v-1h1Zm0-1H6v-1h1v1Zm1 0H7v1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v-1h-1v-1h1v-1h-1v-1h1v-1h-1v-1h1v-1h-1v-1h1v-1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v-1h-1v-1h-1v-1h-1v-1h-1v-1h-1v1H9v1H8v1H7v1h1v1Zm1 0v1H8v-1h1Zm0-1H8v-1h1v1Zm1 0H9v1h1v1h1v-1h1v1h1v-1h1v1h1v-1h-1v-1h-1v-1h-1v-1h-1v-1h-1v1H9v1h1v1Zm1 0v1h-1v-1h1Zm0-1v-1h-1v1h1Zm0 0v1h1v1h1v-1h-1v-1h-1Zm6 2v-1h1v1h-1Zm2 0v1h-1v-1h1Zm0-1h-1v-1h1v1Zm1 0h-1v1h1v1h1v-1h1v1h1v-1h-1v-1h1v-1h-1v-1h1v-1h-1v-1h1v-1h-1v1h-1v1h-1v1h-1v1h1v1Zm1 0h-1v1h1v-1Zm0-1h1v1h-1v-1Zm0-1h1v-1h-1v1Zm0 0v1h-1v-1h1Zm-4 3v1h-1v-1h1Z"/></svg>'},39956:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M19 6h-1v7h-3v1h3v8h1v-3h3v-1h-3V6ZM11 7h-1v13H7v1h3v2h1V10h3V9h-3V7Z"/></svg>'},24464:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="m10.49 7.55-.42.7-2.1 3.5.86.5 1.68-2.8 1.8 2.82.84-.54-2.23-3.5-.43-.68Zm12.32 4.72-.84-.54 2.61-4 .84.54-2.61 4Zm-5.3 6.3 1.2-1.84.84.54-1.63 2.5-.43.65-.41-.65-1.6-2.5.85-.54 1.17 1.85ZM4.96 16.75l.86.52-2.4 4-.86-.52 2.4-4ZM3 14v1h1v-1H3Zm2 0h1v1H5v-1Zm2 0v1h1v-1H7Zm2 0h1v1H9v-1Zm2 0v1h1v-1h-1Zm2 0h1v1h-1v-1Zm2 0v1h1v-1h-1Zm2 0h1v1h-1v-1Zm2 0v1h1v-1h-1Zm2 0h1v1h-1v-1Zm2 0v1h1v-1h-1Zm2 0h1v1h-1v-1Z"/></svg>'},14083:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="currentColor"><path d="M17 11v6h3v-6h-3zm-.5-1h4a.5.5 0 0 1 .5.5v7a.5.5 0 0 1-.5.5h-4a.5.5 0 0 1-.5-.5v-7a.5.5 0 0 1 .5-.5z"/><path d="M18 7h1v3.5h-1zm0 10.5h1V21h-1z"/><path d="M9 8v12h3V8H9zm-.5-1h4a.5.5 0 0 1 .5.5v13a.5.5 0 0 1-.5.5h-4a.5.5 0 0 1-.5-.5v-13a.5.5 0 0 1 .5-.5z"/><path d="M10 4h1v3.5h-1zm0 16.5h1V24h-1z"/></svg>'},53707:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><g fill="none"><path stroke="currentColor" d="M11 20.5H7.5a5 5 0 1 1 .42-9.98 7.5 7.5 0 0 1 14.57 2.1 4 4 0 0 1-1 7.877H18"/><path stroke="currentColor" d="M14.5 24V12.5M11 16l3.5-3.5L18 16"/></g></svg>'},9450:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="M12 7v14h5V7h-5Zm4 1h-3v12h3V8ZM19 15v6h5v-6h-5Zm4 1h-3v4h3v-4ZM5 12h5v9H5v-9Zm1 1h3v7H6v-7Z"/></svg>'},1393:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M13.5 6a8.5 8.5 0 1 0 0 17 8.5 8.5 0 0 0 0-17zM4 14.5a9.5 9.5 0 1 1 19 0 9.5 9.5 0 0 1-19 0z"/><path fill="currentColor" d="M9 14h4v-4h1v4h4v1h-4v4h-1v-4H9v-1z"/></svg>'},45504:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="currentColor"><path d="M9 8v12h3V8H9zm-1-.502C8 7.223 8.215 7 8.498 7h4.004c.275 0 .498.22.498.498v13.004a.493.493 0 0 1-.498.498H8.498A.496.496 0 0 1 8 20.502V7.498z"/><path d="M10 4h1v3.5h-1z"/><path d="M17 6v6h3V6h-3zm-1-.5c0-.276.215-.5.498-.5h4.004c.275 0 .498.23.498.5v7c0 .276-.215.5-.498.5h-4.004a.503.503 0 0 1-.498-.5v-7z"/><path d="M18 2h1v3.5h-1z"/></svg>'},71705:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M7.5 7H7v14h5V7H7.5zM8 20V8h3v12H8zm7.5-11H15v10h5V9h-4.5zm.5 9v-8h3v8h-3z"/></svg>'},32162:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="currentColor"><path fill-rule="evenodd" d="M22 3h1v1h-1V3Zm0 2V4h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1V9h-1V8h-1V7h-1V6h-1V5h-1v1H9v1H8v1H7v1H6v1H5v1H4v1h1v1H4v1h1v-1h1v-1h1v-1h1v-1h1V9h1V8h1v1h1v1h1v1h1v1h1v1h1v-1h1v-1h1v-1h1V9h1V8h1V7h1V6h1V5h-1Zm-1 1V5h1v1h-1Zm-1 1V6h1v1h-1Zm-1 1V7h1v1h-1Zm-1 1V8h1v1h-1Zm-1 1V9h1v1h-1Zm-1 1v-1h1v1h-1Zm-1 0v-1h-1V9h-1V8h-1V7h-1V6h-1v1H9v1H8v1H7v1H6v1H5v1h1v-1h1v-1h1V9h1V8h1V7h1v1h1v1h1v1h1v1h1Zm0 0h1v1h-1v-1Zm.84 6.37 7.5-7-.68-.74-7.15 6.67-4.66-4.65-.33-.34-.36.32-5.5 5 .68.74 5.14-4.68 4.67 **********.35-.33ZM6 23H5v1h1v-1Zm0-1H5v-1h1v1Zm1 0v1H6v-1h1Zm0-1H6v-1h1v1Zm1 0v1H7v-1h1Zm0-1H7v-1h1v1Zm1 0v1H8v-1h1Zm0-1H8v-1h1v1Zm1 0v1H9v-1h1Zm0-1H9v-1h1v1Zm1 0h-1v1h1v1h1v1h1v1h1v1h1v1h1v-1h1v-1h1v-1h1v-1h1v-1h1v-1h1v-1h1v-1h1v-1h-1v-1h1v-1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v-1h-1v-1h-1v-1h-1v-1h-1v-1h-1v1h1v1Zm0 0h1v1h-1v-1Zm2 2v1h1v1h1v1h1v-1h1v-1h1v-1h1v-1h1v-1h1v-1h1v-1h1v-1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v-1h-1v-1h-1Zm0 0v-1h-1v1h1Z"/></svg>'},52867:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="currentColor"><path d="M17 11v6h3v-6h-3zm-.5-1h4a.5.5 0 0 1 .5.5v7a.5.5 0 0 1-.5.5h-4a.5.5 0 0 1-.5-.5v-7a.5.5 0 0 1 .5-.5z"/><path d="M18 7h1v3.5h-1zm0 10.5h1V21h-1z"/><path d="M9 8v11h3V8H9zm-.5-1h4a.5.5 0 0 1 .5.5v12a.5.5 0 0 1-.5.5h-4a.5.5 0 0 1-.5-.5v-12a.5.5 0 0 1 .5-.5z"/><path d="M10 4h1v5h-1zm0 14h1v5h-1zM8.5 9H10v1H8.5zM11 9h1.5v1H11zm-1 1h1v1h-1zm-1.5 1H10v1H8.5zm2.5 0h1.5v1H11zm-1 1h1v1h-1zm-1.5 1H10v1H8.5zm2.5 0h1.5v1H11zm-1 1h1v1h-1zm-1.5 1H10v1H8.5zm2.5 0h1.5v1H11zm-1 1h1v1h-1zm-1.5 1H10v1H8.5zm2.5 0h1.5v1H11z"/></svg>'},39681:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path stroke="currentColor" d="M20 17l-5 5M15 17l5 5M9 11.5h7M17.5 8a2.5 2.5 0 0 0-5 0v11a2.5 2.5 0 0 1-5 0"/></svg>'},31246:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="currentColor"><path fill-rule="evenodd" d="m18.43 15.91 6.96-8.6-.78-.62-6.96 8.6a2.49 2.49 0 0 0-2.63.2l-2.21-2.02A2.5 2.5 0 0 0 10.5 10a2.5 2.5 0 1 0 1.73 4.3l2.12 1.92a2.5 2.5 0 1 0 4.08-.31ZM10.5 14a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Zm7.5 3.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Z"/><path d="M8.37 13.8c.17.3.4.54.68.74l-5.67 6.78-.76-.64 5.75-6.88Z"/></svg>'},41473:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="m25.39 7.31-8.83 10.92-6.02-5.47-7.16 8.56-.76-.64 7.82-9.36 6 5.45L24.61 6.7l.78.62Z"/></svg>'},82436:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 21 21" width="21" height="21"><g fill="none" stroke="currentColor"><path d="M18.5 11v5.5a2 2 0 0 1-2 2h-13a2 2 0 0 1-2-2v-13a2 2 0 0 1 2-2H9"/><path stroke-linecap="square" d="M18 2l-8.5 8.5m4-9h5v5"/></g></svg>'},96052:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M18.293 13l-2.647 2.646.707.708 3.854-3.854-3.854-3.854-.707.708L18.293 12H12.5A5.5 5.5 0 0 0 7 17.5V19h1v-1.5a4.5 4.5 0 0 1 4.5-4.5h5.793z"/></svg>'},72644:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="currentColor"><path fill-rule="evenodd" clip-rule="evenodd" d="M11.118 6a.5.5 0 0 0-.447.276L9.809 8H5.5A1.5 1.5 0 0 0 4 9.5v10A1.5 1.5 0 0 0 5.5 21h16a1.5 1.5 0 0 0 1.5-1.5v-10A1.5 1.5 0 0 0 21.5 8h-4.309l-.862-1.724A.5.5 0 0 0 15.882 6h-4.764zm-1.342-.17A1.5 1.5 0 0 1 11.118 5h4.764a1.5 1.5 0 0 1 1.342.83L17.809 7H21.5A2.5 2.5 0 0 1 24 9.5v10a2.5 2.5 0 0 1-2.5 2.5h-16A2.5 2.5 0 0 1 3 19.5v-10A2.5 2.5 0 0 1 5.5 7h3.691l.585-1.17z"/><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5 18a3.5 3.5 0 1 0 0-7 3.5 3.5 0 0 0 0 7zm0 1a4.5 4.5 0 1 0 0-9 4.5 4.5 0 0 0 0 9z"/></svg>'},15726:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="currentColor"><path d="M19 5h5v1h-4v13h-6v-7h-4v12H5v-1h4V11h6v7h4V5Z"/></svg>'},77665:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M8.707 13l2.647 2.646-.707.708L6.792 12.5l3.853-3.854.708.708L8.707 12H14.5a5.5 5.5 0 0 1 5.5 5.5V19h-1v-1.5a4.5 4.5 0 0 0-4.5-4.5H8.707z"/></svg>'},33765:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"><path fill="currentColor" d="M9.707 9l4.647-4.646-.707-.708L9 8.293 4.354 3.646l-.708.708L8.293 9l-4.647 4.646.708.708L9 9.707l4.646 4.647.708-.707L9.707 9z"/></svg>'},36296:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M8 9.5H6.5a1 1 0 0 0-1 1v11a1 1 0 0 0 1 1h11a1 1 0 0 0 1-1V20m-8-1.5h11a1 1 0 0 0 1-1v-11a1 1 0 0 0-1-1h-11a1 1 0 0 0-1 1v11a1 1 0 0 0 1 1z"/></svg>'},23595:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M6.5 16v4.5a1 1 0 001 1h14a1 1 0 001-1V16M14.5 5V17m-4-3.5l4 4l4-4"/></svg>'},39146:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18" fill="none"><path fill="currentColor" d="M9 1l2.35 4.76 5.26.77-3.8 3.7.9 5.24L9 13l-4.7 2.47.9-5.23-3.8-3.71 5.25-.77L9 1z"/></svg>'},48010:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18" fill="none"><path stroke="currentColor" d="M9 2.13l1.903 3.855.116.236.26.038 4.255.618-3.079 3.001-.188.184.044.259.727 4.237-3.805-2L9 12.434l-.233.122-3.805 2.001.727-4.237.044-.26-.188-.183-3.079-3.001 4.255-.618.26-.038.116-.236L9 2.13z"/></svg>'},29414:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path stroke="currentColor" d="M19 15l2.5-2.5c1-1 1.5-3.5-.5-5.5s-4.5-1.5-5.5-.5L13 9M10 12l-2.5 2.5c-1 1-1.5 3.5.5 5.5s4.5 1.5 5.5.5L16 18M17 11l-5 5"/></svg>'},67487:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M19.75 5h3.07l-6.7 7.62L24 23h-6.17l-4.84-6.3L7.46 23H4.4l7.17-8.16L4 5h6.33l4.37 5.75L19.75 5Zm-1.24 16h1.7L9.54 7H7.7l10.8 14Z"/></svg>'}}]);