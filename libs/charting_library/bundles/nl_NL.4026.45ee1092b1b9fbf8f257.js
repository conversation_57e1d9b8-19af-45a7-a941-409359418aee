(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[4026],{53310:e=>{e.exports={en:["Re"],nl_NL:["Re"]}},94073:e=>{e.exports={en:["A"],nl_NL:["A"]}},66384:e=>{e.exports={en:["L"],nl_NL:["L"]}},85119:e=>{e.exports={en:["Dark"],nl_NL:["Dark"]}},96870:e=>{e.exports={en:["Light"],nl_NL:["Light"]}},85886:e=>{e.exports={en:["d"],nl_NL:["d"]}},44634:e=>{e.exports={en:["h"],nl_NL:["h"]}},5977:e=>{e.exports={en:["m"],nl_NL:["m"]}},21492:e=>{e.exports={en:["s"],nl_NL:["s"]}},97559:e=>{e.exports={en:["{title} copy"],nl_NL:["{title} copy"]}},38691:e=>{e.exports={en:["D"],nl_NL:["D"]}},77995:e=>{e.exports={en:["M"],nl_NL:["M"]}},93934:e=>{e.exports={en:["R"],nl_NL:["R"]}},82901:e=>{e.exports={en:["T"],nl_NL:["T"]}},7408:e=>{e.exports={en:["W"],nl_NL:["W"]}},38048:e=>{e.exports={en:["h"],nl_NL:["h"]}},68430:e=>{e.exports={en:["m"],nl_NL:["m"]}},68823:e=>{e.exports={en:["s"],nl_NL:["s"]}},2696:e=>{e.exports={en:["C"],nl_NL:["C"]}},43253:e=>{e.exports={en:["H"],nl_NL:["H"]}},61372:e=>{e.exports={en:["HL2"],nl_NL:["HL2"]}},55096:e=>{e.exports={en:["HLC3"],nl_NL:["HLC3"]}},94174:e=>{e.exports={en:["OHLC4"],nl_NL:["OHLC4"]}},89923:e=>{e.exports={en:["L"],nl_NL:["L"]}},46728:e=>{e.exports={en:["O"],nl_NL:["O"]}},32856:e=>{e.exports=Object.create(null),e.exports.Close_input={en:["Close"],nl_NL:["Close"]},e.exports.Back_input={en:["Back"],nl_NL:["Back"]},e.exports.Minimize_input={en:["Minimize"],nl_NL:["Minimize"]},e.exports["Hull MA_input"]={en:["Hull MA"],nl_NL:["Hull MA"]},e.exports.from_input={en:["from"],nl_NL:["from"]},e.exports.to_input={en:["to"],nl_NL:["to"]},e.exports["{number} item_combobox_input"]={en:["{number} item","{number} items"],nl_NL:["{number} item","{number} items"]},e.exports.Length_input={en:["Length"],nl_NL:["Length"]},e.exports.Plot_input={en:["Plot"],nl_NL:["Plot"]},e.exports.Zero_input={en:["Zero"],nl_NL:["Zero"]},e.exports.Signal_input={en:["Signal"],nl_NL:["Signal"]},e.exports.Long_input={en:["Long"],nl_NL:["Long"]},e.exports.Short_input={en:["Short"],nl_NL:["Short"]},e.exports.UpperLimit_input={en:["UpperLimit"],nl_NL:["UpperLimit"]},e.exports.LowerLimit_input={en:["LowerLimit"],nl_NL:["LowerLimit"]},e.exports.Offset_input={en:["Offset"],nl_NL:["Offset"]},e.exports.length_input={en:["length"],nl_NL:["length"]},e.exports.mult_input={en:["mult"],nl_NL:["mult"]},e.exports.short_input={en:["short"],nl_NL:["short"]},e.exports.long_input={en:["long"],nl_NL:["long"]},e.exports.Limit_input={en:["Limit"],nl_NL:["Limit"]},e.exports.Move_input={en:["Move"],nl_NL:["Move"]},e.exports.Value_input={en:["Value"],nl_NL:["Value"]},e.exports.Method_input={en:["Method"],nl_NL:["Method"]},e.exports["Values in status line_input"]={en:["Values in status line"],nl_NL:["Values in status line"]},e.exports["Labels on price scale_input"]={en:["Labels on price scale"],nl_NL:["Labels on price scale"]},e.exports["Accumulation/Distribution_input"]={en:["Accumulation/Distribution"],nl_NL:["Accumulation/Distribution"]},e.exports.ADR_B_input={en:["ADR_B"],nl_NL:["ADR_B"]},e.exports["Equality Line_input"]={
en:["Equality Line"],nl_NL:["Equality Line"]},e.exports["Window Size_input"]={en:["Window Size"],nl_NL:["Window Size"]},e.exports.Sigma_input={en:["Sigma"],nl_NL:["Sigma"]},e.exports["Aroon Up_input"]={en:["Aroon Up"],nl_NL:["Aroon Up"]},e.exports["Aroon Down_input"]={en:["Aroon Down"],nl_NL:["Aroon Down"]},e.exports.Upper_input={en:["Upper"],nl_NL:["Upper"]},e.exports.Lower_input={en:["Lower"],nl_NL:["Lower"]},e.exports.Deviation_input={en:["Deviation"],nl_NL:["Deviation"]},e.exports["Levels Format_input"]={en:["Levels Format"],nl_NL:["Levels Format"]},e.exports["Labels Position_input"]={en:["Labels Position"],nl_NL:["Labels Position"]},e.exports["0 Level Color_input"]={en:["0 Level Color"],nl_NL:["0 Level Color"]},e.exports["0.236 Level Color_input"]={en:["0.236 Level Color"],nl_NL:["0.236 Level Color"]},e.exports["0.382 Level Color_input"]={en:["0.382 Level Color"],nl_NL:["0.382 Level Color"]},e.exports["0.5 Level Color_input"]={en:["0.5 Level Color"],nl_NL:["0.5 Level Color"]},e.exports["0.618 Level Color_input"]={en:["0.618 Level Color"],nl_NL:["0.618 Level Color"]},e.exports["0.65 Level Color_input"]={en:["0.65 Level Color"],nl_NL:["0.65 Level Color"]},e.exports["0.786 Level Color_input"]={en:["0.786 Level Color"],nl_NL:["0.786 Level Color"]},e.exports["1 Level Color_input"]={en:["1 Level Color"],nl_NL:["1 Level Color"]},e.exports["1.272 Level Color_input"]={en:["1.272 Level Color"],nl_NL:["1.272 Level Color"]},e.exports["1.414 Level Color_input"]={en:["1.414 Level Color"],nl_NL:["1.414 Level Color"]},e.exports["1.618 Level Color_input"]={en:["1.618 Level Color"],nl_NL:["1.618 Level Color"]},e.exports["1.65 Level Color_input"]={en:["1.65 Level Color"],nl_NL:["1.65 Level Color"]},e.exports["2.618 Level Color_input"]={en:["2.618 Level Color"],nl_NL:["2.618 Level Color"]},e.exports["2.65 Level Color_input"]={en:["2.65 Level Color"],nl_NL:["2.65 Level Color"]},e.exports["3.618 Level Color_input"]={en:["3.618 Level Color"],nl_NL:["3.618 Level Color"]},e.exports["3.65 Level Color_input"]={en:["3.65 Level Color"],nl_NL:["3.65 Level Color"]},e.exports["4.236 Level Color_input"]={en:["4.236 Level Color"],nl_NL:["4.236 Level Color"]},e.exports["-0.236 Level Color_input"]={en:["-0.236 Level Color"],nl_NL:["-0.236 Level Color"]},e.exports["-0.382 Level Color_input"]={en:["-0.382 Level Color"],nl_NL:["-0.382 Level Color"]},e.exports["-0.618 Level Color_input"]={en:["-0.618 Level Color"],nl_NL:["-0.618 Level Color"]},e.exports["-0.65 Level Color_input"]={en:["-0.65 Level Color"],nl_NL:["-0.65 Level Color"]},e.exports.ADX_input={en:["ADX"],nl_NL:["ADX"]},e.exports["ADX Smoothing_input"]={en:["ADX Smoothing"],nl_NL:["ADX Smoothing"]},e.exports["DI Length_input"]={en:["DI Length"],nl_NL:["DI Length"]},e.exports.Smoothing_input={en:["Smoothing"],nl_NL:["Smoothing"]},e.exports.ATR_input={en:["ATR"],nl_NL:["ATR"]},e.exports.Growing_input={en:["Growing"],nl_NL:["Growing"]},e.exports.Falling_input={en:["Falling"],nl_NL:["Falling"]},e.exports["Color 0_input"]={en:["Color 0"],nl_NL:["Color 0"]},e.exports["Color 1_input"]={
en:["Color 1"],nl_NL:["Color 1"]},e.exports.Source_input={en:["Source"],nl_NL:["Source"]},e.exports.StdDev_input={en:["StdDev"],nl_NL:["StdDev"]},e.exports.Basis_input={en:["Basis"],nl_NL:["Basis"]},e.exports.Median_input={en:["Median"],nl_NL:["Median"]},e.exports["Bollinger Bands %B_input"]={en:["Bollinger Bands %B"],nl_NL:["Bollinger Bands %B"]},e.exports.Overbought_input={en:["Overbought"],nl_NL:["Overbought"]},e.exports.Oversold_input={en:["Oversold"],nl_NL:["Oversold"]},e.exports["Bollinger Bands Width_input"]={en:["Bollinger Bands Width"],nl_NL:["Bollinger Bands Width"]},e.exports["RSI Length_input"]={en:["RSI Length"],nl_NL:["RSI Length"]},e.exports["UpDown Length_input"]={en:["UpDown Length"],nl_NL:["UpDown Length"]},e.exports["ROC Length_input"]={en:["ROC Length"],nl_NL:["ROC Length"]},e.exports.MF_input={en:["MF"],nl_NL:["MF"]},e.exports.resolution_input={en:["resolution"],nl_NL:["resolution"]},e.exports["Fast Length_input"]={en:["Fast Length"],nl_NL:["Fast Length"]},e.exports["Slow Length_input"]={en:["Slow Length"],nl_NL:["Slow Length"]},e.exports["Chaikin Oscillator_input"]={en:["Chaikin Oscillator"],nl_NL:["Chaikin Oscillator"]},e.exports.P_input={en:["P"],nl_NL:["P"]},e.exports.X_input={en:["X"],nl_NL:["X"]},e.exports.Q_input={en:["Q"],nl_NL:["Q"]},e.exports.p_input={en:["p"],nl_NL:["p"]},e.exports.x_input={en:["x"],nl_NL:["x"]},e.exports.q_input={en:["q"],nl_NL:["q"]},e.exports.Price_input={en:["Price"],nl_NL:["Price"]},e.exports["Chande MO_input"]={en:["Chande MO"],nl_NL:["Chande MO"]},e.exports["Zero Line_input"]={en:["Zero Line"],nl_NL:["Zero Line"]},e.exports["Color 2_input"]={en:["Color 2"],nl_NL:["Color 2"]},e.exports["Color 3_input"]={en:["Color 3"],nl_NL:["Color 3"]},e.exports["Color 4_input"]={en:["Color 4"],nl_NL:["Color 4"]},e.exports["Color 5_input"]={en:["Color 5"],nl_NL:["Color 5"]},e.exports["Color 6_input"]={en:["Color 6"],nl_NL:["Color 6"]},e.exports["Color 7_input"]={en:["Color 7"],nl_NL:["Color 7"]},e.exports["Color 8_input"]={en:["Color 8"],nl_NL:["Color 8"]},e.exports.CHOP_input={en:["CHOP"],nl_NL:["CHOP"]},e.exports["Upper Band_input"]={en:["Upper Band"],nl_NL:["Upper Band"]},e.exports["Lower Band_input"]={en:["Lower Band"],nl_NL:["Lower Band"]},e.exports.CCI_input={en:["CCI"],nl_NL:["CCI"]},e.exports["Smoothing Line_input"]={en:["Smoothing Line"],nl_NL:["Smoothing Line"]},e.exports["Smoothing Length_input"]={en:["Smoothing Length"],nl_NL:["Smoothing Length"]},e.exports["WMA Length_input"]={en:["WMA Length"],nl_NL:["WMA Length"]},e.exports["Long RoC Length_input"]={en:["Long RoC Length"],nl_NL:["Long RoC Length"]},e.exports["Short RoC Length_input"]={en:["Short RoC Length"],nl_NL:["Short RoC Length"]},e.exports.sym_input={en:["sym"],nl_NL:["sym"]},e.exports.Symbol_input={en:["Symbol"],nl_NL:["Symbol"]},e.exports.Correlation_input={en:["Correlation"],nl_NL:["Correlation"]},e.exports.Period_input={en:["Period"],nl_NL:["Period"]},e.exports.Centered_input={en:["Centered"],nl_NL:["Centered"]},e.exports["Detrended Price Oscillator_input"]={en:["Detrended Price Oscillator"],
nl_NL:["Detrended Price Oscillator"]},e.exports.isCentered_input={en:["isCentered"],nl_NL:["isCentered"]},e.exports.DPO_input={en:["DPO"],nl_NL:["DPO"]},e.exports["ADX smoothing_input"]={en:["ADX smoothing"],nl_NL:["ADX smoothing"]},e.exports["+DI_input"]={en:["+DI"],nl_NL:["+DI"]},e.exports["-DI_input"]={en:["-DI"],nl_NL:["-DI"]},e.exports.DEMA_input={en:["DEMA"],nl_NL:["DEMA"]},e.exports["Multi timeframe_input"]={en:["Multi timeframe"],nl_NL:["Multi timeframe"]},e.exports.Timeframe_input={en:["Timeframe"],nl_NL:["Timeframe"]},e.exports["Wait for timeframe closes_input"]={en:["Wait for timeframe closes"],nl_NL:["Wait for timeframe closes"]},e.exports.Divisor_input={en:["Divisor"],nl_NL:["Divisor"]},e.exports.EOM_input={en:["EOM"],nl_NL:["EOM"]},e.exports["Elder's Force Index_input"]={en:["Elder's Force Index"],nl_NL:["Elder's Force Index"]},e.exports.Percent_input={en:["Percent"],nl_NL:["Percent"]},e.exports.Exponential_input={en:["Exponential"],nl_NL:["Exponential"]},e.exports.Average_input={en:["Average"],nl_NL:["Average"]},e.exports["Upper Percentage_input"]={en:["Upper Percentage"],nl_NL:["Upper Percentage"]},e.exports["Lower Percentage_input"]={en:["Lower Percentage"],nl_NL:["Lower Percentage"]},e.exports.Fisher_input={en:["Fisher"],nl_NL:["Fisher"]},e.exports.Trigger_input={en:["Trigger"],nl_NL:["Trigger"]},e.exports.Level_input={en:["Level"],nl_NL:["Level"]},e.exports["Trader EMA 1 length_input"]={en:["Trader EMA 1 length"],nl_NL:["Trader EMA 1 length"]},e.exports["Trader EMA 2 length_input"]={en:["Trader EMA 2 length"],nl_NL:["Trader EMA 2 length"]},e.exports["Trader EMA 3 length_input"]={en:["Trader EMA 3 length"],nl_NL:["Trader EMA 3 length"]},e.exports["Trader EMA 4 length_input"]={en:["Trader EMA 4 length"],nl_NL:["Trader EMA 4 length"]},e.exports["Trader EMA 5 length_input"]={en:["Trader EMA 5 length"],nl_NL:["Trader EMA 5 length"]},e.exports["Trader EMA 6 length_input"]={en:["Trader EMA 6 length"],nl_NL:["Trader EMA 6 length"]},e.exports["Investor EMA 1 length_input"]={en:["Investor EMA 1 length"],nl_NL:["Investor EMA 1 length"]},e.exports["Investor EMA 2 length_input"]={en:["Investor EMA 2 length"],nl_NL:["Investor EMA 2 length"]},e.exports["Investor EMA 3 length_input"]={en:["Investor EMA 3 length"],nl_NL:["Investor EMA 3 length"]},e.exports["Investor EMA 4 length_input"]={en:["Investor EMA 4 length"],nl_NL:["Investor EMA 4 length"]},e.exports["Investor EMA 5 length_input"]={en:["Investor EMA 5 length"],nl_NL:["Investor EMA 5 length"]},e.exports["Investor EMA 6 length_input"]={en:["Investor EMA 6 length"],nl_NL:["Investor EMA 6 length"]},e.exports.HV_input={en:["HV"],nl_NL:["HV"]},e.exports["Conversion Line Periods_input"]={en:["Conversion Line Periods"],nl_NL:["Conversion Line Periods"]},e.exports["Base Line Periods_input"]={en:["Base Line Periods"],nl_NL:["Base Line Periods"]},e.exports["Lagging Span_input"]={en:["Lagging Span"],nl_NL:["Lagging Span"]},e.exports["Conversion Line_input"]={en:["Conversion Line"],nl_NL:["Conversion Line"]},e.exports["Base Line_input"]={en:["Base Line"],
nl_NL:["Base Line"]},e.exports["Leading Span A_input"]={en:["Leading Span A"],nl_NL:["Lead 1"]},e.exports["Leading Span B_input"]={},e.exports["Plots Background_input"]={en:["Plots Background"],nl_NL:["Plots Background"]},e.exports["yay Color 0_input"]={en:["yay Color 0"],nl_NL:["yay Color 0"]},e.exports["yay Color 1_input"]={en:["yay Color 1"],nl_NL:["yay Color 1"]},e.exports.Multiplier_input={en:["Multiplier"],nl_NL:["Multiplier"]},e.exports["Bands style_input"]={en:["Bands style"],nl_NL:["Bands style"]},e.exports.Middle_input={en:["Middle"],nl_NL:["Middle"]},e.exports.useTrueRange_input={en:["useTrueRange"],nl_NL:["useTrueRange"]},e.exports.ROCLen1_input={en:["ROCLen1"],nl_NL:["ROCLen1"]},e.exports.ROCLen2_input={en:["ROCLen2"],nl_NL:["ROCLen2"]},e.exports.ROCLen3_input={en:["ROCLen3"],nl_NL:["ROCLen3"]},e.exports.ROCLen4_input={en:["ROCLen4"],nl_NL:["ROCLen4"]},e.exports.SMALen1_input={en:["SMALen1"],nl_NL:["SMALen1"]},e.exports.SMALen2_input={en:["SMALen2"],nl_NL:["SMALen2"]},e.exports.SMALen3_input={en:["SMALen3"],nl_NL:["SMALen3"]},e.exports.SMALen4_input={en:["SMALen4"],nl_NL:["SMALen4"]},e.exports.SigLen_input={en:["SigLen"],nl_NL:["SigLen"]},e.exports.KST_input={en:["KST"],nl_NL:["KST"]},e.exports.Sig_input={en:["Sig"],nl_NL:["Sig"]},e.exports.roclen1_input={en:["roclen1"],nl_NL:["roclen1"]},e.exports.roclen2_input={en:["roclen2"],nl_NL:["roclen2"]},e.exports.roclen3_input={en:["roclen3"],nl_NL:["roclen3"]},e.exports.roclen4_input={en:["roclen4"],nl_NL:["roclen4"]},e.exports.smalen1_input={en:["smalen1"],nl_NL:["smalen1"]},e.exports.smalen2_input={en:["smalen2"],nl_NL:["smalen2"]},e.exports.smalen3_input={en:["smalen3"],nl_NL:["smalen3"]},e.exports.smalen4_input={en:["smalen4"],nl_NL:["smalen4"]},e.exports.siglen_input={en:["siglen"],nl_NL:["siglen"]},e.exports["Upper Deviation_input"]={en:["Upper Deviation"],nl_NL:["Upper Deviation"]},e.exports["Lower Deviation_input"]={en:["Lower Deviation"],nl_NL:["Lower Deviation"]},e.exports["Use Upper Deviation_input"]={en:["Use Upper Deviation"],nl_NL:["Use Upper Deviation"]},e.exports["Use Lower Deviation_input"]={en:["Use Lower Deviation"],nl_NL:["Use Lower Deviation"]},e.exports.Count_input={en:["Count"],nl_NL:["Count"]},e.exports.Crosses_input={en:["Crosses"],nl_NL:["Crosses"]},e.exports.MOM_input={en:["MOM"],nl_NL:["MOM"]},e.exports.MA_input={en:["MA"],nl_NL:["MA"]},e.exports["Length EMA_input"]={en:["Length EMA"],nl_NL:["Length EMA"]},e.exports["Length MA_input"]={en:["Length MA"],nl_NL:["Length MA"]},e.exports["Fast length_input"]={en:["Fast length"],nl_NL:["Fast length"]},e.exports["Slow length_input"]={en:["Slow length"],nl_NL:["Slow length"]},e.exports["Signal smoothing_input"]={en:["Signal smoothing"],nl_NL:["Signal smoothing"]},e.exports["Simple ma(oscillator)_input"]={en:["Simple ma(oscillator)"],nl_NL:["Simple ma(oscillator)"]},e.exports["Simple ma(signal line)_input"]={en:["Simple ma(signal line)"],nl_NL:["Simple ma(signal line)"]},e.exports.Histogram_input={en:["Histogram"],nl_NL:["Histogram"]},e.exports.MACD_input={en:["MACD"],nl_NL:["MACD"]},
e.exports.fastLength_input={en:["fastLength"],nl_NL:["fastLength"]},e.exports.slowLength_input={en:["slowLength"],nl_NL:["slowLength"]},e.exports.signalLength_input={en:["signalLength"],nl_NL:["signalLength"]},e.exports.NV_input={en:["NV"],nl_NL:["NV"]},e.exports.OnBalanceVolume_input={en:["OnBalanceVolume"],nl_NL:["OnBalanceVolume"]},e.exports.Start_input={en:["Start"],nl_NL:["Start"]},e.exports.Increment_input={en:["Increment"],nl_NL:["Increment"]},e.exports["Max value_input"]={en:["Max value"],nl_NL:["Max value"]},e.exports.ParabolicSAR_input={en:["ParabolicSAR"],nl_NL:["ParabolicSAR"]},e.exports.start_input={en:["start"],nl_NL:["start"]},e.exports.increment_input={en:["increment"],nl_NL:["increment"]},e.exports.maximum_input={en:["maximum"],nl_NL:["maximum"]},e.exports["Short length_input"]={en:["Short length"],nl_NL:["Short length"]},e.exports["Long length_input"]={en:["Long length"],nl_NL:["Long length"]},e.exports.OSC_input={en:["OSC"],nl_NL:["OSC"]},e.exports.shortlen_input={en:["shortlen"],nl_NL:["shortlen"]},e.exports.longlen_input={en:["longlen"],nl_NL:["longlen"]},e.exports.PVT_input={en:["PVT"],nl_NL:["PVT"]},e.exports.ROC_input={en:["ROC"],nl_NL:["ROC"]},e.exports.RSI_input={en:["RSI"],nl_NL:["RSI"]},e.exports.RVGI_input={en:["RVGI"],nl_NL:["RVGI"]},e.exports.RVI_input={en:["RVI"],nl_NL:["RVI"]},e.exports["Long period_input"]={en:["Long period"],nl_NL:["Long period"]},e.exports["Short period_input"]={en:["Short period"],nl_NL:["Short period"]},e.exports["Signal line period_input"]={en:["Signal line period"],nl_NL:["Signal line period"]},e.exports.SMI_input={en:["SMI"],nl_NL:["SMI"]},e.exports["SMI Ergodic Oscillator_input"]={en:["SMI Ergodic Oscillator"],nl_NL:["SMI Ergodic Oscillator"]},e.exports.Indicator_input={en:["Indicator"],nl_NL:["Indicator"]},e.exports.Oscillator_input={en:["Oscillator"],nl_NL:["Oscillator"]},e.exports.K_input={en:["K"],nl_NL:["K"]},e.exports.D_input={en:["D"],nl_NL:["D"]},e.exports.smoothK_input={en:["smoothK"],nl_NL:["smoothK"]},e.exports.smoothD_input={en:["smoothD"],nl_NL:["smoothD"]},e.exports["%K_input"]={en:["%K"],nl_NL:["%K"]},e.exports["%D_input"]={en:["%D"],nl_NL:["%D"]},e.exports["Stochastic Length_input"]={en:["Stochastic Length"],nl_NL:["Stochastic Length"]},e.exports["RSI Source_input"]={en:["RSI Source"],nl_NL:["RSI Source"]},e.exports.lengthRSI_input={en:["lengthRSI"],nl_NL:["lengthRSI"]},e.exports.lengthStoch_input={en:["lengthStoch"],nl_NL:["lengthStoch"]},e.exports.TRIX_input={en:["TRIX"],nl_NL:["TRIX"]},e.exports.TEMA_input={en:["TEMA"],nl_NL:["TEMA"]},e.exports["Long Length_input"]={en:["Long Length"],nl_NL:["Long Length"]},e.exports["Short Length_input"]={en:["Short Length"],nl_NL:["Short Length"]},e.exports["Signal Length_input"]={en:["Signal Length"],nl_NL:["Signal Length"]},e.exports.Length1_input={en:["Length1"],nl_NL:["Length1"]},e.exports.Length2_input={en:["Length2"],nl_NL:["Length2"]},e.exports.Length3_input={en:["Length3"],nl_NL:["Length3"]},e.exports.length7_input={en:["length7"],nl_NL:["length7"]},e.exports.length14_input={en:["length14"],
nl_NL:["length14"]},e.exports.length28_input={en:["length28"],nl_NL:["length28"]},e.exports.UO_input={en:["UO"],nl_NL:["UO"]},e.exports.VWMA_input={en:["VWMA"],nl_NL:["VWMA"]},e.exports.len_input={en:["len"],nl_NL:["len"]},e.exports["VI +_input"]={en:["VI +"],nl_NL:["VI +"]},e.exports["VI -_input"]={en:["VI -"],nl_NL:["VI -"]},e.exports["%R_input"]={en:["%R"],nl_NL:["%R"]},e.exports["Jaw Length_input"]={en:["Jaw Length"],nl_NL:["Jaw Length"]},e.exports["Teeth Length_input"]={en:["Teeth Length"],nl_NL:["Teeth Length"]},e.exports["Lips Length_input"]={en:["Lips Length"],nl_NL:["Lips Length"]},e.exports.Jaw_input={en:["Jaw"],nl_NL:["Jaw"]},e.exports.Teeth_input={en:["Teeth"],nl_NL:["Teeth"]},e.exports.Lips_input={en:["Lips"],nl_NL:["Lips"]},e.exports["Jaw Offset_input"]={en:["Jaw Offset"],nl_NL:["Jaw Offset"]},e.exports["Teeth Offset_input"]={en:["Teeth Offset"],nl_NL:["Teeth Offset"]},e.exports["Lips Offset_input"]={en:["Lips Offset"],nl_NL:["Lips Offset"]},e.exports["Down fractals_input"]={en:["Down fractals"],nl_NL:["Down fractals"]},e.exports["Up fractals_input"]={en:["Up fractals"],nl_NL:["Up fractals"]},e.exports.Periods_input={en:["Periods"],nl_NL:["Periods"]},e.exports.Shapes_input={en:["Shapes"],nl_NL:["Shapes"]},e.exports["show MA_input"]={en:["show MA"],nl_NL:["show MA"]},e.exports["MA Length_input"]={en:["MA Length"],nl_NL:["MA Length"]},e.exports["Color based on previous close_input"]={en:["Color based on previous close"],nl_NL:["Color based on previous close"]},e.exports["Rows Layout_input"]={en:["Rows Layout"],nl_NL:["Rows Layout"]},e.exports["Row Size_input"]={en:["Row Size"],nl_NL:["Row Size"]},e.exports.Volume_input={en:["Volume"],nl_NL:["Volume"]},e.exports["Value Area volume_input"]={en:["Value Area volume"],nl_NL:["Value Area volume"]},e.exports["Extend Right_input"]={en:["Extend Right"],nl_NL:["Extend Right"]},e.exports["Extend POC Right_input"]={en:["Extend POC Right"],nl_NL:["Extend POC Right"]},e.exports["Extend VAH Right_input"]={en:["Extend VAH Right"],nl_NL:["Extend VAH Right"]},e.exports["Extend VAL Right_input"]={en:["Extend VAL Right"],nl_NL:["Extend VAL Right"]},e.exports["Value Area Volume_input"]={en:["Value Area Volume"],nl_NL:["Value Area Volume"]},e.exports.Placement_input={en:["Placement"],nl_NL:["Placement"]},e.exports.POC_input={en:["POC"],nl_NL:["POC"]},e.exports["Developing Poc_input"]={en:["Developing Poc"],nl_NL:["Developing Poc"]},e.exports["Up Volume_input"]={en:["Up Volume"],nl_NL:["Up Volume"]},e.exports["Down Volume_input"]={en:["Down Volume"],nl_NL:["Down Volume"]},e.exports["Value Area_input"]={en:["Value Area"],nl_NL:["Value Area"]},e.exports["Histogram Box_input"]={en:["Histogram Box"],nl_NL:["Histogram Box"]},e.exports["Value Area Up_input"]={en:["Value Area Up"],nl_NL:["Value Area Up"]},e.exports["Value Area Down_input"]={en:["Value Area Down"],nl_NL:["Value Area Down"]},e.exports["Number Of Rows_input"]={en:["Number Of Rows"],nl_NL:["Number Of Rows"]},e.exports["Ticks Per Row_input"]={en:["Ticks Per Row"],nl_NL:["Ticks Per Row"]},e.exports["Up/Down_input"]={
en:["Up/Down"],nl_NL:["Up/Down"]},e.exports.Total_input={en:["Total"],nl_NL:["Total"]},e.exports.Delta_input={en:["Delta"],nl_NL:["Delta"]},e.exports.Bar_input={en:["Bar"],nl_NL:["Bar"]},e.exports.Day_input={en:["Day"],nl_NL:["Day"]},e.exports["Deviation (%)_input"]={en:["Deviation (%)"],nl_NL:["Deviation (%)"]},e.exports.Depth_input={en:["Depth"],nl_NL:["Depth"]},e.exports["Extend to last bar_input"]={en:["Extend to last bar"],nl_NL:["Extend to last bar"]},e.exports.Simple_input={en:["Simple"],nl_NL:["Simple"]},e.exports.Weighted_input={en:["Weighted"],nl_NL:["Weighted"]},e.exports["Wilder's Smoothing_input"]={en:["Wilder's Smoothing"],nl_NL:["Wilder's Smoothing"]},e.exports["1st Period_input"]={en:["1st Period"],nl_NL:["1st Period"]},e.exports["2nd Period_input"]={en:["2nd Period"],nl_NL:["2nd Period"]},e.exports["3rd Period_input"]={en:["3rd Period"],nl_NL:["3rd Period"]},e.exports["4th Period_input"]={en:["4th Period"],nl_NL:["4th Period"]},e.exports["5th Period_input"]={en:["5th Period"],nl_NL:["5th Period"]},e.exports["6th Period_input"]={en:["6th Period"],nl_NL:["6th Period"]},e.exports["Rate of Change Lookback_input"]={en:["Rate of Change Lookback"],nl_NL:["Rate of Change Lookback"]},e.exports["Instrument 1_input"]={en:["Instrument 1"],nl_NL:["Instrument 1"]},e.exports["Instrument 2_input"]={en:["Instrument 2"],nl_NL:["Instrument 2"]},e.exports["Rolling Period_input"]={en:["Rolling Period"],nl_NL:["Rolling Period"]},e.exports["Standard Errors_input"]={en:["Standard Errors"],nl_NL:["Standard Errors"]},e.exports["Averaging Periods_input"]={en:["Averaging Periods"],nl_NL:["Averaging Periods"]},e.exports["Days Per Year_input"]={en:["Days Per Year"],nl_NL:["Days Per Year"]},e.exports["Market Closed Percentage_input"]={en:["Market Closed Percentage"],nl_NL:["Market Closed Percentage"]},e.exports["ATR Mult_input"]={en:["ATR Mult"],nl_NL:["ATR Mult"]},e.exports.VWAP_input={en:["VWAP"],nl_NL:["VWAP"]},e.exports["Anchor Period_input"]={en:["Anchor Period"],nl_NL:["Anchor Period"]},e.exports.Session_input={en:["Session"],nl_NL:["Session"]},e.exports.Week_input={en:["Week"],nl_NL:["Week"]},e.exports.Month_input={en:["Month"],nl_NL:["Month"]},e.exports.Year_input={en:["Year"],nl_NL:["Year"]},e.exports.Decade_input={en:["Decade"],nl_NL:["Decade"]},e.exports.Century_input={en:["Century"],nl_NL:["Century"]},e.exports.Sessions_input={en:["Sessions"],nl_NL:["Sessions"]},e.exports["Each (pre-market, market, post-market)_input"]={en:["Each (pre-market, market, post-market)"],nl_NL:["Each (pre-market, market, post-market)"]},e.exports["Pre-market only_input"]={en:["Pre-market only"],nl_NL:["Pre-market only"]},e.exports["Market only_input"]={en:["Market only"],nl_NL:["Market only"]},e.exports["Post-market only_input"]={en:["Post-market only"],nl_NL:["Post-market only"]},e.exports["Main chart symbol_input"]={en:["Main chart symbol"],nl_NL:["Main chart symbol"]},e.exports["Another symbol_input"]={en:["Another symbol"],nl_NL:["Another symbol"]},e.exports.Line_input={en:["Line"],nl_NL:["Lijn"]},
e.exports["Nothing selected_combobox_input"]={en:["Nothing selected"],nl_NL:["Nothing selected"]},e.exports["All items_combobox_input"]={en:["All items"],nl_NL:["All items"]},e.exports.Cancel_input={en:["Cancel"],nl_NL:["Cancel"]},e.exports.Open_input={en:["Open"],nl_NL:["Open"]},e.exports.MM_month_input={en:["MM"],nl_NL:["MM"]},e.exports.YY_year_input={en:["YY"],nl_NL:["YY"]},e.exports.Style_input={en:["Style"],nl_NL:["Style"]},e.exports["Box size assignment method_input"]={en:["Box size assignment method"],nl_NL:["Box size assignment method"]},e.exports["Color bars based on previous close_input"]={en:["Color bars based on previous close"],nl_NL:["Color bars based on previous close"]},e.exports.Candles_input={en:["Candles"],nl_NL:["Candles"]},e.exports.Borders_input={en:["Borders"],nl_NL:["Borders"]},e.exports.Wick_input={en:["Wick"],nl_NL:["Wick"]},e.exports["HLC bars_input"]={en:["HLC bars"],nl_NL:["HLC bars"]},e.exports["Price source_input"]={en:["Price source"],nl_NL:["Price source"]},e.exports.Type_input={en:["Type"],nl_NL:["Type"]},e.exports["Show real prices on price scale (instead of Heikin-Ashi price)_input"]={en:["Show real prices on price scale (instead of Heikin-Ashi price)"],nl_NL:["Show real prices on price scale (instead of Heikin-Ashi price)"]},e.exports["Up bars_input"]={en:["Up bars"],nl_NL:["Up bars"]},e.exports["Down bars_input"]={en:["Down bars"],nl_NL:["Down bars"]},e.exports["Projection up bars_input"]={en:["Projection up bars"],nl_NL:["Projection up bars"]},e.exports["Projection down bars_input"]={en:["Projection down bars"],nl_NL:["Projection down bars"]},e.exports["Projection up color_input"]={en:["Projection up color"],nl_NL:["Projection up color"]},e.exports["Projection down color_input"]={en:["Projection down color"],nl_NL:["Projection down color"]},e.exports.Fill_input={en:["Fill"],nl_NL:["Fill"]},e.exports["Up color_input"]={en:["Up color"],nl_NL:["Up color"]},e.exports["Down color_input"]={en:["Down color"],nl_NL:["Down color"]},e.exports.Traditional_input={en:["Traditional"],nl_NL:["Traditional"]},e.exports.PercentageLTP_input={en:["PercentageLTP"],nl_NL:["PercentageLTP"]},e.exports["Box size_input"]={en:["Box size"],nl_NL:["Box size"]},e.exports["Number of line_input"]={en:["Number of line"],nl_NL:["Number of line"]},e.exports["ATR length_input"]={en:["ATR length"],nl_NL:["ATR length"]},e.exports.Percentage_input={en:["Percentage"],nl_NL:["Percentage"]},e.exports["Reversal amount_input"]={en:["Reversal amount"],nl_NL:["Reversal amount"]},e.exports["Phantom bars_input"]={en:["Phantom bars"],nl_NL:["Phantom bars"]},e.exports["One step back building_input"]={en:["One step back building"],nl_NL:["One step back building"]},e.exports.Wicks_input={en:["Wicks"],nl_NL:["Wicks"]},e.exports.Range_input={en:["Range"],nl_NL:["Range"]},e.exports.All_input={en:["All"],nl_NL:["All"]},e.exports.Custom_input={en:["Custom"],nl_NL:["Custom"]},e.exports["Leading Span Periods_input"]={en:["Leading Span Periods"],nl_NL:["Lagging Span 2 Periods"]},e.exports["Leading Shift Periods_input"]={
en:["Leading Shift Periods"],nl_NL:["Leading Shift Periods"]},e.exports["ATR({atrValue})_input"]={en:["ATR({atrValue})"],nl_NL:["ATR({atrValue})"]},e.exports["PercentageLTP({percentageLTPValue}%)_input"]={en:["PercentageLTP({percentageLTPValue}%)"],nl_NL:["PercentageLTP({percentageLTPValue}%)"]}},50873:e=>{e.exports={en:["ATR({atrValue})"],nl_NL:["ATR({atrValue})"]}},28037:e=>{e.exports={en:["PercentageLTP({percentageLTPValue}%)"],nl_NL:["PercentageLTP({percentageLTPValue}%)"]}},40566:e=>{e.exports={en:["Traditional"],nl_NL:["Traditional"]}},75163:e=>{e.exports={en:["Invert scale"],nl_NL:["Invert Scale"]}},35210:e=>{e.exports={en:["Indexed to 100"],nl_NL:["Indexed to 100"]}},31340:e=>{e.exports={en:["Logarithmic"],nl_NL:["Logarithmic"]}},19405:e=>{e.exports={en:["No overlapping labels"],nl_NL:["No Overlapping Labels"]}},34954:e=>{e.exports={en:["Percent"],nl_NL:["Percent"]}},55300:e=>{e.exports={en:["Regular"],nl_NL:["Regular"]}},8029:e=>{e.exports={en:["ETH"],nl_NL:["ETH"]}},34647:e=>{e.exports={en:["Electronic trading hours"],nl_NL:["Electronic trading hours"]}},36862:e=>{e.exports={en:["Extended trading hours"],nl_NL:["Extended trading hours"]}},7807:e=>{e.exports={en:["POST"],nl_NL:["POST"]}},46273:e=>{e.exports={en:["PRE"],nl_NL:["PRE"]}},50434:e=>{e.exports={en:["Postmarket"],nl_NL:["Postmarket"]}},59330:e=>{e.exports={en:["Premarket"],nl_NL:["Premarket"]}},35342:e=>{e.exports={en:["RTH"],nl_NL:["RTH"]}},84246:e=>{e.exports={en:["Regular trading hours"],nl_NL:["Regular trading hours"]}},13132:e=>{e.exports={en:["May"],nl_NL:["Mei"]}},83477:e=>{e.exports=Object.create(null),e.exports.Technicals_study={en:["Technicals"],nl_NL:["Technicals"]},e.exports["Average Day Range_study"]={en:["Average Day Range"],nl_NL:["Average Day Range"]},e.exports["Bull Bear Power_study"]={en:["Bull Bear Power"],nl_NL:["Bull Bear Power"]},e.exports["Capital expenditures_study"]={en:["Capital expenditures"],nl_NL:["Capital expenditures"]},e.exports["Cash to debt ratio_study"]={en:["Cash to debt ratio"],nl_NL:["Cash to debt ratio"]},e.exports["Debt to EBITDA ratio_study"]={en:["Debt to EBITDA ratio"],nl_NL:["Debt to EBITDA ratio"]},e.exports["Directional Movement Index_study"]={en:["Directional Movement Index"],nl_NL:["Directional Movement Index"]},e.exports.DMI_study={en:["DMI"],nl_NL:["DMI"]},e.exports["Dividend payout ratio %_study"]={en:["Dividend payout ratio %"],nl_NL:["Dividend payout ratio %"]},e.exports["Equity to assets ratio_study"]={en:["Equity to assets ratio"],nl_NL:["Equity to assets ratio"]},e.exports["Enterprise value to EBIT ratio_study"]={en:["Enterprise value to EBIT ratio"],nl_NL:["Enterprise value to EBIT ratio"]},e.exports["Enterprise value to EBITDA ratio_study"]={en:["Enterprise value to EBITDA ratio"],nl_NL:["Enterprise value to EBITDA ratio"]},e.exports["Enterprise value to revenue ratio_study"]={en:["Enterprise value to revenue ratio"],nl_NL:["Enterprise value to revenue ratio"]},e.exports["Goodwill, net_study"]={en:["Goodwill, net"],nl_NL:["Goodwill, net"]},e.exports["Ichimoku Cloud_study"]={
en:["Ichimoku Cloud"],nl_NL:["Ichimoku Cloud"]},e.exports.Ichimoku_study={en:["Ichimoku"],nl_NL:["Ichimoku"]},e.exports["Moving Average Convergence Divergence_study"]={en:["Moving Average Convergence Divergence"],nl_NL:["Moving Average Convergence Divergence"]},e.exports["Operating income_study"]={en:["Operating income"],nl_NL:["Operating income"]},e.exports["Price to book ratio_study"]={en:["Price to book ratio"],nl_NL:["Price to book ratio"]},e.exports["Price to cash flow ratio_study"]={en:["Price to cash flow ratio"],nl_NL:["Price to cash flow ratio"]},e.exports["Price to earnings ratio_study"]={en:["Price to earnings ratio"],nl_NL:["Price to earnings ratio"]},e.exports["Price to free cash flow ratio_study"]={en:["Price to free cash flow ratio"],nl_NL:["Price to free cash flow ratio"]},e.exports["Price to sales ratio_study"]={en:["Price to sales ratio"],nl_NL:["Price to sales ratio"]},e.exports["Float shares outstanding_study"]={en:["Float shares outstanding"],nl_NL:["Float shares outstanding"]},e.exports["Total common shares outstanding_study"]={en:["Total common shares outstanding"],nl_NL:["Total common shares outstanding"]},e.exports["Volume Weighted Average Price_study"]={en:["Volume Weighted Average Price"],nl_NL:["Volume Weighted Average Price"]},e.exports["Volume Weighted Moving Average_study"]={en:["Volume Weighted Moving Average"],nl_NL:["Volume Weighted Moving Average"]},e.exports["Williams Percent Range_study"]={en:["Williams Percent Range"],nl_NL:["Williams Percent Range"]},e.exports.Doji_study={en:["Doji"],nl_NL:["Doji"]},e.exports["Spinning Top Black_study"]={en:["Spinning Top Black"],nl_NL:["Spinning Top Black"]},e.exports["Spinning Top White_study"]={en:["Spinning Top White"],nl_NL:["Spinning Top White"]},e.exports["Accounts payable_study"]={en:["Accounts payable"],nl_NL:["Accounts payable"]},e.exports["Accounts receivables, gross_study"]={en:["Accounts receivables, gross"],nl_NL:["Accounts receivables, gross"]},e.exports["Accounts receivable - trade, net_study"]={en:["Accounts receivable - trade, net"],nl_NL:["Accounts receivable - trade, net"]},e.exports.Accruals_study={en:["Accruals"],nl_NL:["Accruals"]},e.exports["Accrued payroll_study"]={en:["Accrued payroll"],nl_NL:["Accrued payroll"]},e.exports["Accumulated depreciation, total_study"]={en:["Accumulated depreciation, total"],nl_NL:["Accumulated depreciation, total"]},e.exports["Additional paid-in capital/Capital surplus_study"]={en:["Additional paid-in capital/Capital surplus"],nl_NL:["Additional paid-in capital/Capital surplus"]},e.exports["After tax other income/expense_study"]={en:["After tax other income/expense"],nl_NL:["After tax other income/expense"]},e.exports["Altman Z-score_study"]={en:["Altman Z-score"],nl_NL:["Altman Z-score"]},e.exports.Amortization_study={en:["Amortization"],nl_NL:["Amortization"]},e.exports["Amortization of intangibles_study"]={en:["Amortization of intangibles"],nl_NL:["Amortization of intangibles"]},e.exports["Amortization of deferred charges_study"]={en:["Amortization of deferred charges"],
nl_NL:["Amortization of deferred charges"]},e.exports["Asset turnover_study"]={en:["Asset turnover"],nl_NL:["Asset turnover"]},e.exports["Average basic shares outstanding_study"]={en:["Average basic shares outstanding"],nl_NL:["Average basic shares outstanding"]},e.exports["Bad debt / Doubtful accounts_study"]={en:["Bad debt / Doubtful accounts"],nl_NL:["Bad debt / Doubtful accounts"]},e.exports["Basic EPS_study"]={en:["Basic EPS"],nl_NL:["Basic EPS"]},e.exports["Basic earnings per share (Basic EPS)_study"]={en:["Basic earnings per share (Basic EPS)"],nl_NL:["Basic earnings per share (Basic EPS)"]},e.exports["Beneish M-score_study"]={en:["Beneish M-score"],nl_NL:["Beneish M-score"]},e.exports["Book value per share_study"]={en:["Book value per share"],nl_NL:["Book value per share"]},e.exports["Buyback yield %_study"]={en:["Buyback yield %"],nl_NL:["Buyback yield %"]},e.exports["Capital and operating lease obligations_study"]={en:["Capital and operating lease obligations"],nl_NL:["Capital and operating lease obligations"]},e.exports["Capital expenditures - fixed assets_study"]={en:["Capital expenditures - fixed assets"],nl_NL:["Capital expenditures - fixed assets"]},e.exports["Capital expenditures - other assets_study"]={en:["Capital expenditures - other assets"],nl_NL:["Capital expenditures - other assets"]},e.exports["Capitalized lease obligations_study"]={en:["Capitalized lease obligations"],nl_NL:["Capitalized lease obligations"]},e.exports["Cash and short term investments_study"]={en:["Cash and short term investments"],nl_NL:["Cash and short term investments"]},e.exports["Cash conversion cycle_study"]={en:["Cash conversion cycle"],nl_NL:["Cash conversion cycle"]},e.exports["Cash & equivalents_study"]={en:["Cash & equivalents"],nl_NL:["Cash & equivalents"]},e.exports["Cash from financing activities_study"]={en:["Cash from financing activities"],nl_NL:["Cash from financing activities"]},e.exports["Cash from investing activities_study"]={en:["Cash from investing activities"],nl_NL:["Cash from investing activities"]},e.exports["Cash from operating activities_study"]={en:["Cash from operating activities"],nl_NL:["Cash from operating activities"]},e.exports["Change in accounts payable_study"]={en:["Change in accounts payable"],nl_NL:["Change in accounts payable"]},e.exports["Change in accounts receivable_study"]={en:["Change in accounts receivable"],nl_NL:["Change in accounts receivable"]},e.exports["Change in accrued expenses_study"]={en:["Change in accrued expenses"],nl_NL:["Change in accrued expenses"]},e.exports["Change in inventories_study"]={en:["Change in inventories"],nl_NL:["Change in inventories"]},e.exports["Change in other assets/liabilities_study"]={en:["Change in other assets/liabilities"],nl_NL:["Change in other assets/liabilities"]},e.exports["Change in taxes payable_study"]={en:["Change in taxes payable"],nl_NL:["Change in taxes payable"]},e.exports["Changes in working capital_study"]={en:["Changes in working capital"],nl_NL:["Changes in working capital"]},e.exports["COGS to revenue ratio_study"]={
en:["COGS to revenue ratio"],nl_NL:["COGS to revenue ratio"]},e.exports["Common dividends paid_study"]={en:["Common dividends paid"],nl_NL:["Common dividends paid"]},e.exports["Common equity, total_study"]={en:["Common equity, total"],nl_NL:["Common equity, total"]},e.exports["Common stock par/Carrying value_study"]={en:["Common stock par/Carrying value"],nl_NL:["Common stock par/Carrying value"]},e.exports["Cost of goods_study"]={en:["Cost of goods"],nl_NL:["Cost of goods"]},e.exports["Cost of goods sold_study"]={en:["Cost of goods sold"],nl_NL:["Cost of goods sold"]},e.exports["Current portion of LT debt and capital leases_study"]={en:["Current portion of LT debt and capital leases"],nl_NL:["Current portion of LT debt and capital leases"]},e.exports["Current ratio_study"]={en:["Current ratio"],nl_NL:["Current ratio"]},e.exports["Days inventory_study"]={en:["Days inventory"],nl_NL:["Days inventory"]},e.exports["Days payable_study"]={en:["Days payable"],nl_NL:["Days payable"]},e.exports["Days sales outstanding_study"]={en:["Days sales outstanding"],nl_NL:["Days sales outstanding"]},e.exports["Debt to assets ratio_study"]={en:["Debt to assets ratio"],nl_NL:["Debt to assets ratio"]},e.exports["Debt to equity ratio_study"]={en:["Debt to equity ratio"],nl_NL:["Debt to equity ratio"]},e.exports["Debt to revenue ratio_study"]={en:["Debt to revenue ratio"],nl_NL:["Debt to revenue ratio"]},e.exports["Deferred income, current_study"]={en:["Deferred income, current"],nl_NL:["Deferred income, current"]},e.exports["Deferred income, non-current_study"]={en:["Deferred income, non-current"],nl_NL:["Deferred income, non-current"]},e.exports["Deferred tax assets_study"]={en:["Deferred tax assets"],nl_NL:["Deferred tax assets"]},e.exports["Deferred taxes (cash flow)_study"]={en:["Deferred taxes (cash flow)"],nl_NL:["Deferred taxes (cash flow)"]},e.exports["Deferred tax liabilities_study"]={en:["Deferred tax liabilities"],nl_NL:["Deferred tax liabilities"]},e.exports.Depreciation_study={en:["Depreciation"],nl_NL:["Depreciation"]},e.exports["Deprecation and amortization_study"]={en:["Deprecation and amortization"],nl_NL:["Deprecation and amortization"]},e.exports["Depreciation & amortization (cash flow)_study"]={en:["Depreciation & amortization (cash flow)"],nl_NL:["Depreciation & amortization (cash flow)"]},e.exports["Depreciation/depletion_study"]={en:["Depreciation/depletion"],nl_NL:["Depreciation/depletion"]},e.exports["Diluted EPS_study"]={en:["Diluted EPS"],nl_NL:["Diluted EPS"]},e.exports["Diluted earnings per share (Diluted EPS)_study"]={en:["Diluted earnings per share (Diluted EPS)"],nl_NL:["Diluted earnings per share (Diluted EPS)"]},e.exports["Diluted net income available to common stockholders_study"]={en:["Diluted net income available to common stockholders"],nl_NL:["Diluted net income available to common stockholders"]},e.exports["Diluted shares outstanding_study"]={en:["Diluted shares outstanding"],nl_NL:["Diluted shares outstanding"]},e.exports["Dilution adjustment_study"]={en:["Dilution adjustment"],
nl_NL:["Dilution adjustment"]},e.exports["Discontinued operations_study"]={en:["Discontinued operations"],nl_NL:["Discontinued operations"]},e.exports["Dividends payable_study"]={en:["Dividends payable"],nl_NL:["Dividends payable"]},e.exports["Dividends per share - common stock primary issue_study"]={en:["Dividends per share - common stock primary issue"],nl_NL:["Dividends per share - common stock primary issue"]},e.exports["Dividend yield %_study"]={en:["Dividend yield %"],nl_NL:["Dividend yield %"]},e.exports["Earnings yield_study"]={en:["Earnings yield"],nl_NL:["Earnings yield"]},e.exports.EBIT_study={en:["EBIT"],nl_NL:["EBIT"]},e.exports.EBITDA_study={en:["EBITDA"],nl_NL:["EBITDA"]},e.exports["EBITDA margin %_study"]={en:["EBITDA margin %"],nl_NL:["EBITDA margin %"]},e.exports["Effective interest rate on debt %_study"]={en:["Effective interest rate on debt %"],nl_NL:["Effective interest rate on debt %"]},e.exports["Enterprise value_study"]={en:["Enterprise value"],nl_NL:["Enterprise value"]},e.exports["EPS basic one year growth_study"]={en:["EPS basic one year growth"],nl_NL:["EPS basic one year growth"]},e.exports["EPS diluted one year growth_study"]={en:["EPS diluted one year growth"],nl_NL:["EPS diluted one year growth"]},e.exports["EPS estimates_study"]={en:["EPS estimates"],nl_NL:["EPS estimates"]},e.exports["Equity in earnings_study"]={en:["Equity in earnings"],nl_NL:["Equity in earnings"]},e.exports["Financing activities – other sources_study"]={en:["Financing activities – other sources"],nl_NL:["Financing activities – other sources"]},e.exports["Financing activities – other uses_study"]={en:["Financing activities – other uses"],nl_NL:["Financing activities – other uses"]},e.exports["Free cash flow_study"]={en:["Free cash flow"],nl_NL:["Free cash flow"]},e.exports["Free cash flow margin %_study"]={en:["Free cash flow margin %"],nl_NL:["Free cash flow margin %"]},e.exports["Fulmer H factor_study"]={en:["Fulmer H factor"],nl_NL:["Fulmer H factor"]},e.exports["Funds from operations_study"]={en:["Funds from operations"],nl_NL:["Funds from operations"]},e.exports["Goodwill to assets ratio_study"]={en:["Goodwill to assets ratio"],nl_NL:["Goodwill to assets ratio"]},e.exports["Graham's number_study"]={en:["Graham's number"],nl_NL:["Graham's number"]},e.exports["Gross margin %_study"]={en:["Gross margin %"],nl_NL:["Gross margin %"]},e.exports["Gross profit_study"]={en:["Gross profit"],nl_NL:["Gross profit"]},e.exports["Gross profit to assets ratio_study"]={en:["Gross profit to assets ratio"],nl_NL:["Gross profit to assets ratio"]},e.exports["Gross property/plant/equipment_study"]={en:["Gross property/plant/equipment"],nl_NL:["Gross property/plant/equipment"]},e.exports.Impairments_study={en:["Impairments"],nl_NL:["Impairments"]},e.exports["Income Tax Credits_study"]={en:["Income Tax Credits"],nl_NL:["Income Tax Credits"]},e.exports["Income tax, current_study"]={en:["Income tax, current"],nl_NL:["Income tax, current"]},e.exports["Income tax, current - domestic_study"]={en:["Income tax, current - domestic"],
nl_NL:["Income tax, current - domestic"]},e.exports["Income Tax, current - foreign_study"]={en:["Income Tax, current - foreign"],nl_NL:["Income Tax, current - foreign"]},e.exports["Income tax, deferred_study"]={en:["Income tax, deferred"],nl_NL:["Income tax, deferred"]},e.exports["Income tax, deferred - domestic_study"]={en:["Income tax, deferred - domestic"],nl_NL:["Income tax, deferred - domestic"]},e.exports["Income tax, deferred - foreign_study"]={en:["Income tax, deferred - foreign"],nl_NL:["Income tax, deferred - foreign"]},e.exports["Income tax payable_study"]={en:["Income tax payable"],nl_NL:["Income tax payable"]},e.exports["Interest capitalized_study"]={en:["Interest capitalized"],nl_NL:["Interest capitalized"]},e.exports["Interest coverage_study"]={en:["Interest coverage"],nl_NL:["Interest coverage"]},e.exports["Interest expense, net of interest capitalized_study"]={en:["Interest expense, net of interest capitalized"],nl_NL:["Interest expense, net of interest capitalized"]},e.exports["Interest expense on debt_study"]={en:["Interest expense on debt"],nl_NL:["Interest expense on debt"]},e.exports["Inventories - finished goods_study"]={en:["Inventories - finished goods"],nl_NL:["Inventories - finished goods"]},e.exports["Inventories - progress payments & other_study"]={en:["Inventories - progress payments & other"],nl_NL:["Inventories - progress payments & other"]},e.exports["Inventories - raw materials_study"]={en:["Inventories - raw materials"],nl_NL:["Inventories - raw materials"]},e.exports["Inventories - work in progress_study"]={en:["Inventories - work in progress"],nl_NL:["Inventories - work in progress"]},e.exports["Inventory to revenue ratio_study"]={en:["Inventory to revenue ratio"],nl_NL:["Inventory to revenue ratio"]},e.exports["Inventory turnover_study"]={en:["Inventory turnover"],nl_NL:["Inventory turnover"]},e.exports["Investing activities – other sources_study"]={en:["Investing activities – other sources"],nl_NL:["Investing activities – other sources"]},e.exports["Investing activities – other uses_study"]={en:["Investing activities – other uses"],nl_NL:["Investing activities – other uses"]},e.exports["Investments in unconsolidated subsidiaries_study"]={en:["Investments in unconsolidated subsidiaries"],nl_NL:["Investments in unconsolidated subsidiaries"]},e.exports["Issuance of long term debt_study"]={en:["Issuance of long term debt"],nl_NL:["Issuance of long term debt"]},e.exports["Issuance/retirement of debt, net_study"]={en:["Issuance/retirement of debt, net"],nl_NL:["Issuance/retirement of debt, net"]},e.exports["Issuance/retirement of long term debt_study"]={en:["Issuance/retirement of long term debt"],nl_NL:["Issuance/retirement of long term debt"]},e.exports["Issuance/retirement of other debt_study"]={en:["Issuance/retirement of other debt"],nl_NL:["Issuance/retirement of other debt"]},e.exports["Issuance/retirement of short term debt_study"]={en:["Issuance/retirement of short term debt"],nl_NL:["Issuance/retirement of short term debt"]},
e.exports["Issuance/retirement of stock, net_study"]={en:["Issuance/retirement of stock, net"],nl_NL:["Issuance/retirement of stock, net"]},e.exports["KZ index_study"]={en:["KZ index"],nl_NL:["KZ index"]},e.exports["Legal claim expense_study"]={en:["Legal claim expense"],nl_NL:["Legal claim expense"]},e.exports["Long term debt_study"]={en:["Long term debt"],nl_NL:["Long term debt"]},e.exports["Long term debt excl. lease liabilities_study"]={en:["Long term debt excl. lease liabilities"],nl_NL:["Long term debt excl. lease liabilities"]},e.exports["Long term debt to total assets ratio_study"]={en:["Long term debt to total assets ratio"],nl_NL:["Long term debt to total assets ratio"]},e.exports["Long term debt to total equity ratio_study"]={en:["Long term debt to total equity ratio"],nl_NL:["Long term debt to total equity ratio"]},e.exports["Long term investments_study"]={en:["Long term investments"],nl_NL:["Long term investments"]},e.exports["Market capitalization_study"]={en:["Market capitalization"],nl_NL:["Market capitalization"]},e.exports["Minority interest_study"]={en:["Minority interest"],nl_NL:["Minority interest"]},e.exports["Miscellaneous non-operating expense_study"]={en:["Miscellaneous non-operating expense"],nl_NL:["Miscellaneous non-operating expense"]},e.exports["Net current asset value per share_study"]={en:["Net current asset value per share"],nl_NL:["Net current asset value per share"]},e.exports["Net debt_study"]={en:["Net debt"],nl_NL:["Net debt"]},e.exports["Net income_study"]={en:["Net income"],nl_NL:["Net income"]},e.exports["Net income before discontinued operations_study"]={en:["Net income before discontinued operations"],nl_NL:["Net income before discontinued operations"]},e.exports["Net income (cash flow)_study"]={en:["Net income (cash flow)"],nl_NL:["Net income (cash flow)"]},e.exports["Net income per employee_study"]={en:["Net income per employee"],nl_NL:["Net income per employee"]},e.exports["Net intangible assets_study"]={en:["Net intangible assets"],nl_NL:["Net intangible assets"]},e.exports["Net margin %_study"]={en:["Net margin %"],nl_NL:["Net margin %"]},e.exports["Net property/plant/equipment_study"]={en:["Net property/plant/equipment"],nl_NL:["Net property/plant/equipment"]},e.exports["Non-cash items_study"]={en:["Non-cash items"],nl_NL:["Non-cash items"]},e.exports["Non-controlling/minority interest_study"]={en:["Non-controlling/minority interest"],nl_NL:["Non-controlling/minority interest"]},e.exports["Non-operating income, excl. interest expenses_study"]={en:["Non-operating income, excl. interest expenses"],nl_NL:["Non-operating income, excl. interest expenses"]},e.exports["Non-operating income, total_study"]={en:["Non-operating income, total"],nl_NL:["Non-operating income, total"]},e.exports["Non-operating interest income_study"]={en:["Non-operating interest income"],nl_NL:["Non-operating interest income"]},e.exports["Note receivable - long term_study"]={en:["Note receivable - long term"],nl_NL:["Note receivable - long term"]},e.exports["Notes payable_study"]={
en:["Notes payable"],nl_NL:["Notes payable"]},e.exports["Number of employees_study"]={en:["Number of employees"],nl_NL:["Number of employees"]},e.exports["Number of shareholders_study"]={en:["Number of shareholders"],nl_NL:["Number of shareholders"]},e.exports["Operating earnings yield %_study"]={en:["Operating earnings yield %"],nl_NL:["Operating earnings yield %"]},e.exports["Operating expenses (excl. COGS)_study"]={en:["Operating expenses (excl. COGS)"],nl_NL:["Operating expenses (excl. COGS)"]},e.exports["Operating lease liabilities_study"]={en:["Operating lease liabilities"],nl_NL:["Operating lease liabilities"]},e.exports["Operating margin %_study"]={en:["Operating margin %"],nl_NL:["Operating margin %"]},e.exports["Other COGS_study"]={en:["Other COGS"],nl_NL:["Other COGS"]},e.exports["Other common equity_study"]={en:["Other common equity"],nl_NL:["Other common equity"]},e.exports["Other current assets, total_study"]={en:["Other current assets, total"],nl_NL:["Other current assets, total"]},e.exports["Other current liabilities_study"]={en:["Other current liabilities"],nl_NL:["Other current liabilities"]},e.exports["Other cost of goods sold_study"]={en:["Other cost of goods sold"],nl_NL:["Other cost of goods sold"]},e.exports["Other exceptional charges_study"]={en:["Other exceptional charges"],nl_NL:["Other exceptional charges"]},e.exports["Other financing cash flow items, total_study"]={en:["Other financing cash flow items, total"],nl_NL:["Other financing cash flow items, total"]},e.exports["Other intangibles, net_study"]={en:["Other intangibles, net"],nl_NL:["Other intangibles, net"]},e.exports["Other investing cash flow items, total_study"]={en:["Other investing cash flow items, total"],nl_NL:["Other investing cash flow items, total"]},e.exports["Other investments_study"]={en:["Other investments"],nl_NL:["Other investments"]},e.exports["Other liabilities, total_study"]={en:["Other liabilities, total"],nl_NL:["Other liabilities, total"]},e.exports["Other long term assets, total_study"]={en:["Other long term assets, total"],nl_NL:["Other long term assets, total"]},e.exports["Other non-current liabilities, total_study"]={en:["Other non-current liabilities, total"],nl_NL:["Other non-current liabilities, total"]},e.exports["Other operating expenses, total_study"]={en:["Other operating expenses, total"],nl_NL:["Other operating expenses, total"]},e.exports["Other receivables_study"]={en:["Other receivables"],nl_NL:["Other receivables"]},e.exports["Other short term debt_study"]={en:["Other short term debt"],nl_NL:["Other short term debt"]},e.exports["Paid in capital_study"]={en:["Paid in capital"],nl_NL:["Paid in capital"]},e.exports["PEG ratio_study"]={en:["PEG ratio"],nl_NL:["PEG ratio"]},e.exports["Piotroski F-score_study"]={en:["Piotroski F-score"],nl_NL:["Piotroski F-score"]},e.exports["Preferred dividends_study"]={en:["Preferred dividends"],nl_NL:["Preferred dividends"]},e.exports["Preferred dividends paid_study"]={en:["Preferred dividends paid"],nl_NL:["Preferred dividends paid"]},
e.exports["Preferred stock, carrying value_study"]={en:["Preferred stock, carrying value"],nl_NL:["Preferred stock, carrying value"]},e.exports["Prepaid expenses_study"]={en:["Prepaid expenses"],nl_NL:["Prepaid expenses"]},e.exports["Pretax equity in earnings_study"]={en:["Pretax equity in earnings"],nl_NL:["Pretax equity in earnings"]},e.exports["Pretax income_study"]={en:["Pretax income"],nl_NL:["Pretax income"]},e.exports["Price earnings ratio forward_study"]={en:["Price earnings ratio forward"],nl_NL:["Price earnings ratio forward"]},e.exports["Price sales ratio forward_study"]={en:["Price sales ratio forward"],nl_NL:["Price sales ratio forward"]},e.exports["Price to tangible book ratio_study"]={en:["Price to tangible book ratio"],nl_NL:["Price to tangible book ratio"]},e.exports["Provision for risks & charge_study"]={en:["Provision for risks & charge"],nl_NL:["Provision for risks & charge"]},e.exports["Purchase/acquisition of business_study"]={en:["Purchase/acquisition of business"],nl_NL:["Purchase/acquisition of business"]},e.exports["Purchase of investments_study"]={en:["Purchase of investments"],nl_NL:["Purchase of investments"]},e.exports["Purchase/sale of business, net_study"]={en:["Purchase/sale of business, net"],nl_NL:["Purchase/sale of business, net"]},e.exports["Purchase/sale of investments, net_study"]={en:["Purchase/sale of investments, net"],nl_NL:["Purchase/sale of investments, net"]},e.exports["Quality ratio_study"]={en:["Quality ratio"],nl_NL:["Quality ratio"]},e.exports["Quick ratio_study"]={en:["Quick ratio"],nl_NL:["Quick ratio"]},e.exports["Reduction of long term debt_study"]={en:["Reduction of long term debt"],nl_NL:["Reduction of long term debt"]},e.exports["Repurchase of common & preferred stock_study"]={en:["Repurchase of common & preferred stock"],nl_NL:["Repurchase of common & preferred stock"]},e.exports["Research & development_study"]={en:["Research & development"],nl_NL:["Research & development"]},e.exports["Research & development to revenue ratio_study"]={en:["Research & development to revenue ratio"],nl_NL:["Research & development to revenue ratio"]},e.exports["Restructuring charge_study"]={en:["Restructuring charge"],nl_NL:["Restructuring charge"]},e.exports["Retained earnings_study"]={en:["Retained earnings"],nl_NL:["Retained earnings"]},e.exports["Return on assets %_study"]={en:["Return on assets %"],nl_NL:["Return on assets %"]},e.exports["Return on equity %_study"]={en:["Return on equity %"],nl_NL:["Return on equity %"]},e.exports["Return on equity adjusted to book value %_study"]={en:["Return on equity adjusted to book value %"],nl_NL:["Return on equity adjusted to book value %"]},e.exports["Return on invested capital %_study"]={en:["Return on invested capital %"],nl_NL:["Return on invested capital %"]},e.exports["Return on tangible assets %_study"]={en:["Return on tangible assets %"],nl_NL:["Return on tangible assets %"]},e.exports["Return on tangible equity %_study"]={en:["Return on tangible equity %"],nl_NL:["Return on tangible equity %"]},
e.exports["Revenue estimates_study"]={en:["Revenue estimates"],nl_NL:["Revenue estimates"]},e.exports["Revenue one year growth_study"]={en:["Revenue one year growth"],nl_NL:["Revenue one year growth"]},e.exports["Revenue per employee_study"]={en:["Revenue per employee"],nl_NL:["Revenue per employee"]},e.exports["Sale/maturity of investments_study"]={en:["Sale/maturity of investments"],nl_NL:["Sale/maturity of investments"]},e.exports["Sale of common & preferred stock_study"]={en:["Sale of common & preferred stock"],nl_NL:["Sale of common & preferred stock"]},e.exports["Sale of fixed assets & businesses_study"]={en:["Sale of fixed assets & businesses"],nl_NL:["Sale of fixed assets & businesses"]},e.exports["Selling/general/admin expenses, other_study"]={en:["Selling/general/admin expenses, other"],nl_NL:["Selling/general/admin expenses, other"]},e.exports["Selling/general/admin expenses, total_study"]={en:["Selling/general/admin expenses, total"],nl_NL:["Selling/general/admin expenses, total"]},e.exports["Shareholders' equity_study"]={en:["Shareholders' equity"],nl_NL:["Shareholders' equity"]},e.exports["Shares buyback ratio %_study"]={en:["Shares buyback ratio %"],nl_NL:["Shares buyback ratio %"]},e.exports["Short term debt_study"]={en:["Short term debt"],nl_NL:["Short term debt"]},e.exports["Short term debt excl. current portion of LT debt_study"]={en:["Short term debt excl. current portion of LT debt"],nl_NL:["Short term debt excl. current portion of LT debt"]},e.exports["Short term investments_study"]={en:["Short term investments"],nl_NL:["Short term investments"]},e.exports["Sloan ratio %_study"]={en:["Sloan ratio %"],nl_NL:["Sloan ratio %"]},e.exports["Springate score_study"]={en:["Springate score"],nl_NL:["Springate score"]},e.exports["Sustainable growth rate_study"]={en:["Sustainable growth rate"],nl_NL:["Sustainable growth rate"]},e.exports["Tangible book value per share_study"]={en:["Tangible book value per share"],nl_NL:["Tangible book value per share"]},e.exports["Tangible common equity ratio_study"]={en:["Tangible common equity ratio"],nl_NL:["Tangible common equity ratio"]},e.exports.Taxes_study={en:["Taxes"],nl_NL:["Taxes"]},e.exports["Tobin's Q (approximate)_study"]={en:["Tobin's Q (approximate)"],nl_NL:["Tobin's Q (approximate)"]},e.exports["Total assets_study"]={en:["Total assets"],nl_NL:["Total assets"]},e.exports["Total cash dividends paid_study"]={en:["Total cash dividends paid"],nl_NL:["Total cash dividends paid"]},e.exports["Total current assets_study"]={en:["Total current assets"],nl_NL:["Total current assets"]},e.exports["Total current liabilities_study"]={en:["Total current liabilities"],nl_NL:["Total current liabilities"]},e.exports["Total debt_study"]={en:["Total debt"],nl_NL:["Total debt"]},e.exports["Total equity_study"]={en:["Total equity"],nl_NL:["Total equity"]},e.exports["Total inventory_study"]={en:["Total inventory"],nl_NL:["Total inventory"]},e.exports["Total liabilities_study"]={en:["Total liabilities"],nl_NL:["Total liabilities"]},
e.exports["Total liabilities & shareholders' equities_study"]={en:["Total liabilities & shareholders' equities"],nl_NL:["Total liabilities & shareholders' equities"]},e.exports["Total non-current assets_study"]={en:["Total non-current assets"],nl_NL:["Total non-current assets"]},e.exports["Total non-current liabilities_study"]={en:["Total non-current liabilities"],nl_NL:["Total non-current liabilities"]},e.exports["Total operating expenses_study"]={en:["Total operating expenses"],nl_NL:["Total operating expenses"]},e.exports["Total receivables, net_study"]={en:["Total receivables, net"],nl_NL:["Total receivables, net"]},e.exports["Total revenue_study"]={en:["Total revenue"],nl_NL:["Total revenue"]},e.exports["Treasury stock - common_study"]={en:["Treasury stock - common"],nl_NL:["Treasury stock - common"]},e.exports["Unrealized gain/loss_study"]={en:["Unrealized gain/loss"],nl_NL:["Unrealized gain/loss"]},e.exports["Unusual income/expense_study"]={en:["Unusual income/expense"],nl_NL:["Unusual income/expense"]},e.exports["Zmijewski score_study"]={en:["Zmijewski score"],nl_NL:["Zmijewski score"]},e.exports["Valuation ratios_study"]={en:["Valuation ratios"],nl_NL:["Valuation ratios"]},e.exports["Profitability ratios_study"]={en:["Profitability ratios"],nl_NL:["Profitability ratios"]},e.exports["Liquidity ratios_study"]={en:["Liquidity ratios"],nl_NL:["Liquidity ratios"]},e.exports["Solvency ratios_study"]={en:["Solvency ratios"],nl_NL:["Solvency ratios"]},e.exports["Key stats_study"]={en:["Key stats"],nl_NL:["Key stats"]},e.exports["Accumulation/Distribution_study"]={en:["Accumulation/Distribution"],nl_NL:["Accumulatie/distributie"]},e.exports["Accumulative Swing Index_study"]={en:["Accumulative Swing Index"],nl_NL:["Accumulative Swing Index"]},e.exports["Advance/Decline_study"]={en:["Advance/Decline"],nl_NL:["Advance/Decline"]},e.exports["All Chart Patterns_study"]={en:["All Chart Patterns"],nl_NL:["All Chart Patterns"]},e.exports["Arnaud Legoux Moving Average_study"]={en:["Arnaud Legoux Moving Average"],nl_NL:["Arnaud Legoux Moving Average"]},e.exports.Aroon_study={en:["Aroon"],nl_NL:["Aroon"]},e.exports.ASI_study={en:["ASI"],nl_NL:["ASI"]},e.exports["Average Directional Index_study"]={en:["Average Directional Index"],nl_NL:["Average Directional Index"]},e.exports["Average True Range_study"]={en:["Average True Range"],nl_NL:["Average True Range"]},e.exports["Awesome Oscillator_study"]={en:["Awesome Oscillator"],nl_NL:["Awesome Oscillator"]},e.exports["Balance of Power_study"]={en:["Balance of Power"],nl_NL:["Balance of Power"]},e.exports["Bollinger Bands %B_study"]={en:["Bollinger Bands %B"],nl_NL:["Bollinger Bands %B"]},e.exports["Bollinger Bands Width_study"]={en:["Bollinger Bands Width"],nl_NL:["Bollinger Bands Width"]},e.exports["Bollinger Bands_study"]={en:["Bollinger Bands"],nl_NL:["Bollinger Bands"]},e.exports["Chaikin Money Flow_study"]={en:["Chaikin Money Flow"],nl_NL:["Chaikin Money Flow"]},e.exports["Chaikin Oscillator_study"]={en:["Chaikin Oscillator"],nl_NL:["Chaikin Oscillator"]},
e.exports["Chande Kroll Stop_study"]={en:["Chande Kroll Stop"],nl_NL:["Chande Kroll Stop"]},e.exports["Chande Momentum Oscillator_study"]={en:["Chande Momentum Oscillator"],nl_NL:["Chande Momentum Oscillator"]},e.exports["Chop Zone_study"]={en:["Chop Zone"],nl_NL:["Chop Zone"]},e.exports["Choppiness Index_study"]={en:["Choppiness Index"],nl_NL:["Choppiness Index"]},e.exports["Commodity Channel Index_study"]={en:["Commodity Channel Index"],nl_NL:["Commodity Channel Index"]},e.exports["Connors RSI_study"]={en:["Connors RSI"],nl_NL:["Connors RSI"]},e.exports["Coppock Curve_study"]={en:["Coppock Curve"],nl_NL:["Coppock Curve"]},e.exports["Correlation Coefficient_study"]={en:["Correlation Coefficient"],nl_NL:["Correlation Coefficient"]},e.exports.CRSI_study={en:["CRSI"],nl_NL:["CRSI"]},e.exports["Detrended Price Oscillator_study"]={en:["Detrended Price Oscillator"],nl_NL:["Detrended Price Oscillator"]},e.exports["Directional Movement_study"]={en:["Directional Movement"],nl_NL:["Directional Movement"]},e.exports["Donchian Channels_study"]={en:["Donchian Channels"],nl_NL:["Donchian Channels"]},e.exports["Double EMA_study"]={en:["Double EMA"],nl_NL:["Double EMA"]},e.exports["Ease Of Movement_study"]={en:["Ease Of Movement"],nl_NL:["Ease Of Movement"]},e.exports["Elder Force Index_study"]={en:["Elder Force Index"],nl_NL:["Elder's Force Index"]},e.exports["EMA Cross_study"]={en:["EMA Cross"],nl_NL:["EMA Cross"]},e.exports.Envelopes_study={en:["Envelopes"],nl_NL:["Envelopes"]},e.exports["Fisher Transform_study"]={en:["Fisher Transform"],nl_NL:["Fisher Transform"]},e.exports["Fixed Range_study"]={en:["Fixed Range"],nl_NL:["Fixed Range"]},e.exports["Fixed Range Volume Profile_study"]={en:["Fixed Range Volume Profile"],nl_NL:["Fixed Range Volume Profile"]},e.exports["Guppy Multiple Moving Average_study"]={en:["Guppy Multiple Moving Average"],nl_NL:["Guppy Multiple Moving Average"]},e.exports["Historical Volatility_study"]={en:["Historical Volatility"],nl_NL:["Historical Volatility"]},e.exports["Hull Moving Average_study"]={en:["Hull Moving Average"],nl_NL:["Hull Moving Average"]},e.exports["Keltner Channels_study"]={en:["Keltner Channels"],nl_NL:["Keltner Channels"]},e.exports["Klinger Oscillator_study"]={en:["Klinger Oscillator"],nl_NL:["Klinger Oscillator"]},e.exports["Know Sure Thing_study"]={en:["Know Sure Thing"],nl_NL:["Know Sure Thing"]},e.exports["Least Squares Moving Average_study"]={en:["Least Squares Moving Average"],nl_NL:["Least Squares Moving Average"]},e.exports["Linear Regression Curve_study"]={en:["Linear Regression Curve"],nl_NL:["Linear Regression Curve"]},e.exports["MA Cross_study"]={en:["MA Cross"],nl_NL:["MA Cross"]},e.exports["MA with EMA Cross_study"]={en:["MA with EMA Cross"],nl_NL:["MA with EMA Cross"]},e.exports["MA/EMA Cross_study"]={en:["MA/EMA Cross"],nl_NL:["MA/EMA Cross"]},e.exports.MACD_study={en:["MACD"],nl_NL:["MACD"]},e.exports["Mass Index_study"]={en:["Mass Index"],nl_NL:["Mass Index"]},e.exports["McGinley Dynamic_study"]={en:["McGinley Dynamic"],nl_NL:["McGinley Dynamic"]},
e.exports.Median_study={en:["Median"],nl_NL:["Mediaan"]},e.exports.Momentum_study={en:["Momentum"],nl_NL:["Momentum"]},e.exports["Money Flow_study"]={en:["Money Flow"],nl_NL:["Money Flow"]},e.exports["Moving Average Channel_study"]={en:["Moving Average Channel"],nl_NL:["Moving Average Channel"]},e.exports["Moving Average Exponential_study"]={en:["Moving Average Exponential"],nl_NL:["Moving Average Exponential"]},e.exports["Moving Average Weighted_study"]={en:["Moving Average Weighted"],nl_NL:["Moving Average Weighted"]},e.exports["Moving Average Simple_study"]={en:["Moving Average Simple"],nl_NL:["Moving Average Simple"]},e.exports["Net Volume_study"]={en:["Net Volume"],nl_NL:["Net Volume"]},e.exports["On Balance Volume_study"]={en:["On Balance Volume"],nl_NL:["On Balance Volume"]},e.exports["Parabolic SAR_study"]={en:["Parabolic SAR"],nl_NL:["Parabolic SAR"]},e.exports["Pivot Points Standard_study"]={en:["Pivot Points Standard"],nl_NL:["Pivot Points Standard"]},e.exports["Periodic Volume Profile_study"]={en:["Periodic Volume Profile"],nl_NL:["Periodic Volume Profile"]},e.exports["Price Channel_study"]={en:["Price Channel"],nl_NL:["Price Channel"]},e.exports["Price Oscillator_study"]={en:["Price Oscillator"],nl_NL:["Price Oscillator"]},e.exports["Price Volume Trend_study"]={en:["Price Volume Trend"],nl_NL:["Price Volume Trend"]},e.exports["Rate Of Change_study"]={en:["Rate Of Change"],nl_NL:["Rate Of Change"]},e.exports["Relative Strength Index_study"]={en:["Relative Strength Index"],nl_NL:["Relative Strength Index"]},e.exports["Relative Vigor Index_study"]={en:["Relative Vigor Index"],nl_NL:["Relative Vigor Index"]},e.exports["Relative Volatility Index_study"]={en:["Relative Volatility Index"],nl_NL:["Relative Volatility Index"]},e.exports["Relative Volume at Time_study"]={en:["Relative Volume at Time"],nl_NL:["Relative Volume at Time"]},e.exports["Session Volume_study"]={en:["Session Volume"],nl_NL:["Session Volume"]},e.exports["Session Volume HD_study"]={en:["Session Volume HD"],nl_NL:["Session Volume HD"]},e.exports["Session Volume Profile_study"]={en:["Session Volume Profile"],nl_NL:["Session Volume Profile"]},e.exports["Session Volume Profile HD_study"]={en:["Session Volume Profile HD"],nl_NL:["Session Volume Profile HD"]},e.exports["SMI Ergodic Indicator/Oscillator_study"]={en:["SMI Ergodic Indicator/Oscillator"],nl_NL:["SMI Ergodic Indicator/Oscillator"]},e.exports["Smoothed Moving Average_study"]={en:["Smoothed Moving Average"],nl_NL:["Smoothed Moving Average"]},e.exports["Stochastic Momentum Index_study"]={en:["Stochastic Momentum Index"],nl_NL:["Stochastic Momentum Index"]},e.exports.Stoch_study={en:["Stoch"],nl_NL:["Stoch"]},e.exports["Stochastic RSI_study"]={en:["Stochastic RSI"],nl_NL:["Stochastic RSI"]},e.exports.Stochastic_study={en:["Stochastic"],nl_NL:["Stochastic"]},e.exports["Time Weighted Average Price_study"]={en:["Time Weighted Average Price"],nl_NL:["Time Weighted Average Price"]},e.exports["Triple EMA_study"]={en:["Triple EMA"],nl_NL:["Triple EMA"]},e.exports.TRIX_study={en:["TRIX"],
nl_NL:["TRIX"]},e.exports["True Strength Indicator_study"]={en:["True Strength Indicator"],nl_NL:["True Strength Indicator"]},e.exports["Ultimate Oscillator_study"]={en:["Ultimate Oscillator"],nl_NL:["Ultimate Oscillator"]},e.exports["Visible Range_study"]={en:["Visible Range"],nl_NL:["Visible Range"]},e.exports["Visible Range Volume Profile_study"]={en:["Visible Range Volume Profile"],nl_NL:["Visible Range Volume Profile"]},e.exports["Volume Oscillator_study"]={en:["Volume Oscillator"],nl_NL:["Volume Oscillator"]},e.exports.Volume_study={en:["Volume"],nl_NL:["Volume"]},e.exports.Vol_study={en:["Vol"],nl_NL:["Vol"]},e.exports["Vortex Indicator_study"]={en:["Vortex Indicator"],nl_NL:["Vortex Indicator"]},e.exports.VWAP_study={en:["VWAP"],nl_NL:["VWAP"]},e.exports.VWMA_study={en:["VWMA"],nl_NL:["VWMA"]},e.exports["Williams %R_study"]={en:["Williams %R"],nl_NL:["Williams %R"]},e.exports["Williams Alligator_study"]={en:["Williams Alligator"],nl_NL:["Williams Alligator"]},e.exports["Williams Fractal_study"]={en:["Williams Fractal"],nl_NL:["Williams Fractal"]},e.exports["Zig Zag_study"]={en:["Zig Zag"],nl_NL:["Zig Zag"]},e.exports["24-hour Volume_study"]={en:["24-hour Volume"],nl_NL:["24-hour Volume"]},e.exports["Ease of Movement_study"]={en:["Ease of Movement"],nl_NL:["Ease Of Movement"]},e.exports["Elders Force Index_study"]={en:["Elders Force Index"],nl_NL:["Elders Force Index"]},e.exports.Envelope_study={en:["Envelope"],nl_NL:["Envelope"]},e.exports.Gaps_study={en:["Gaps"],nl_NL:["Gaps"]},e.exports["Linear Regression Channel_study"]={en:["Linear Regression Channel"],nl_NL:["Linear Regression Channel"]},e.exports["Moving Average Ribbon_study"]={en:["Moving Average Ribbon"],nl_NL:["Moving Average Ribbon"]},e.exports["Multi-Time Period Charts_study"]={en:["Multi-Time Period Charts"],nl_NL:["Multi-Time Period Charts"]},e.exports["Open Interest_study"]={en:["Open Interest"],nl_NL:["Open Interest"]},e.exports["Rob Booker - Intraday Pivot Points_study"]={en:["Rob Booker - Intraday Pivot Points"],nl_NL:["Rob Booker - Intraday Pivot Points"]},e.exports["Rob Booker - Knoxville Divergence_study"]={en:["Rob Booker - Knoxville Divergence"],nl_NL:["Rob Booker - Knoxville Divergence"]},e.exports["Rob Booker - Missed Pivot Points_study"]={en:["Rob Booker - Missed Pivot Points"],nl_NL:["Rob Booker - Missed Pivot Points"]},e.exports["Rob Booker - Reversal_study"]={en:["Rob Booker - Reversal"],nl_NL:["Rob Booker - Reversal"]},e.exports["Rob Booker - Ziv Ghost Pivots_study"]={en:["Rob Booker - Ziv Ghost Pivots"],nl_NL:["Rob Booker - Ziv Ghost Pivots"]},e.exports.Supertrend_study={en:["Supertrend"],nl_NL:["Supertrend"]},e.exports["Technical Ratings_study"]={en:["Technical Ratings"],nl_NL:["Technical Ratings"]},e.exports["True Strength Index_study"]={en:["True Strength Index"],nl_NL:["True Strength Index"]},e.exports["Up/Down Volume_study"]={en:["Up/Down Volume"],nl_NL:["Up/Down Volume"]},e.exports["Visible Average Price_study"]={en:["Visible Average Price"],nl_NL:["Visible Average Price"]},e.exports["Williams Fractals_study"]={
en:["Williams Fractals"],nl_NL:["Williams Fractals"]},e.exports["Keltner Channels Strategy_study"]={en:["Keltner Channels Strategy"],nl_NL:["Keltner Channels Strategy"]},e.exports["Rob Booker - ADX Breakout_study"]={en:["Rob Booker - ADX Breakout"],nl_NL:["Rob Booker - ADX Breakout"]},e.exports["Supertrend Strategy_study"]={en:["Supertrend Strategy"],nl_NL:["Supertrend Strategy"]},e.exports["Technical Ratings Strategy_study"]={en:["Technical Ratings Strategy"],nl_NL:["Technical Ratings Strategy"]},e.exports["Auto Anchored Volume Profile_study"]={en:["Auto Anchored Volume Profile"],nl_NL:["Auto Anchored Volume Profile"]},e.exports["Auto Fib Extension_study"]={en:["Auto Fib Extension"],nl_NL:["Auto Fib Extension"]},e.exports["Auto Fib Retracement_study"]={en:["Auto Fib Retracement"],nl_NL:["Auto Fib Retracement"]},e.exports["Auto Pitchfork_study"]={en:["Auto Pitchfork"],nl_NL:["Auto Pitchfork"]},e.exports["Bearish Flag Chart Pattern_study"]={en:["Bearish Flag Chart Pattern"],nl_NL:["Bearish Flag Chart Pattern"]},e.exports["Bullish Flag Chart Pattern_study"]={en:["Bullish Flag Chart Pattern"],nl_NL:["Bullish Flag Chart Pattern"]},e.exports["Bearish Pennant Chart Pattern_study"]={en:["Bearish Pennant Chart Pattern"],nl_NL:["Bearish Pennant Chart Pattern"]},e.exports["Bullish Pennant Chart Pattern_study"]={en:["Bullish Pennant Chart Pattern"],nl_NL:["Bullish Pennant Chart Pattern"]},e.exports["Double Bottom Chart Pattern_study"]={en:["Double Bottom Chart Pattern"],nl_NL:["Double Bottom Chart Pattern"]},e.exports["Double Top Chart Pattern_study"]={en:["Double Top Chart Pattern"],nl_NL:["Double Top Chart Pattern"]},e.exports["Elliott Wave Chart Pattern_study"]={en:["Elliott Wave Chart Pattern"],nl_NL:["Elliott Wave Chart Pattern"]},e.exports["Falling Wedge Chart Pattern_study"]={en:["Falling Wedge Chart Pattern"],nl_NL:["Falling Wedge Chart Pattern"]},e.exports["Head And Shoulders Chart Pattern_study"]={},e.exports["Inverse Head And Shoulders Chart Pattern_study"]={},e.exports["Rectangle Chart Pattern_study"]={en:["Rectangle Chart Pattern"],nl_NL:["Rectangle Chart Pattern"]},e.exports["Rising Wedge Chart Pattern_study"]={en:["Rising Wedge Chart Pattern"],nl_NL:["Rising Wedge Chart Pattern"]},e.exports["Triangle Chart Pattern_study"]={en:["Triangle Chart Pattern"],nl_NL:["Triangle Chart Pattern"]},e.exports["Triple Bottom Chart Pattern_study"]={en:["Triple Bottom Chart Pattern"],nl_NL:["Triple Bottom Chart Pattern"]},e.exports["Triple Top Chart Pattern_study"]={en:["Triple Top Chart Pattern"],nl_NL:["Triple Top Chart Pattern"]},e.exports["VWAP Auto Anchored_study"]={en:["VWAP Auto Anchored"],nl_NL:["VWAP Auto Anchored"]},e.exports["*All Candlestick Patterns*_study"]={en:["*All Candlestick Patterns*"],nl_NL:["*All Candlestick Patterns*"]},e.exports["Abandoned Baby - Bearish_study"]={en:["Abandoned Baby - Bearish"],nl_NL:["Abandoned Baby - Bearish"]},e.exports["Abandoned Baby - Bullish_study"]={en:["Abandoned Baby - Bullish"],nl_NL:["Abandoned Baby - Bullish"]},e.exports["Dark Cloud Cover - Bearish_study"]={
en:["Dark Cloud Cover - Bearish"],nl_NL:["Dark Cloud Cover - Bearish"]},e.exports["Doji Star - Bearish_study"]={en:["Doji Star - Bearish"],nl_NL:["Doji Star - Bearish"]},e.exports["Doji Star - Bullish_study"]={en:["Doji Star - Bullish"],nl_NL:["Doji Star - Bullish"]},e.exports["Downside Tasuki Gap - Bearish_study"]={en:["Downside Tasuki Gap - Bearish"],nl_NL:["Downside Tasuki Gap - Bearish"]},e.exports["Dragonfly Doji - Bullish_study"]={en:["Dragonfly Doji - Bullish"],nl_NL:["Dragonfly Doji - Bullish"]},e.exports["Engulfing - Bearish_study"]={en:["Engulfing - Bearish"],nl_NL:["Engulfing - Bearish"]},e.exports["Engulfing - Bullish_study"]={en:["Engulfing - Bullish"],nl_NL:["Engulfing - Bullish"]},e.exports["Evening Doji Star - Bearish_study"]={en:["Evening Doji Star - Bearish"],nl_NL:["Evening Doji Star - Bearish"]},e.exports["Evening Star - Bearish_study"]={en:["Evening Star - Bearish"],nl_NL:["Evening Star - Bearish"]},e.exports["Falling Three Methods - Bearish_study"]={en:["Falling Three Methods - Bearish"],nl_NL:["Falling Three Methods - Bearish"]},e.exports["Falling Window - Bearish_study"]={en:["Falling Window - Bearish"],nl_NL:["Falling Window - Bearish"]},e.exports["Gravestone Doji - Bearish_study"]={en:["Gravestone Doji - Bearish"],nl_NL:["Gravestone Doji - Bearish"]},e.exports["Hammer - Bullish_study"]={en:["Hammer - Bullish"],nl_NL:["Hammer - Bullish"]},e.exports["Hanging Man - Bearish_study"]={en:["Hanging Man - Bearish"],nl_NL:["Hanging Man - Bearish"]},e.exports["Harami - Bearish_study"]={en:["Harami - Bearish"],nl_NL:["Harami - Bearish"]},e.exports["Harami - Bullish_study"]={en:["Harami - Bullish"],nl_NL:["Harami - Bullish"]},e.exports["Harami Cross - Bearish_study"]={en:["Harami Cross - Bearish"],nl_NL:["Harami Cross - Bearish"]},e.exports["Harami Cross - Bullish_study"]={en:["Harami Cross - Bullish"],nl_NL:["Harami Cross - Bullish"]},e.exports["Inverted Hammer - Bullish_study"]={en:["Inverted Hammer - Bullish"],nl_NL:["Inverted Hammer - Bullish"]},e.exports["Kicking - Bearish_study"]={en:["Kicking - Bearish"],nl_NL:["Kicking - Bearish"]},e.exports["Kicking - Bullish_study"]={en:["Kicking - Bullish"],nl_NL:["Kicking - Bullish"]},e.exports["Long Lower Shadow - Bullish_study"]={en:["Long Lower Shadow - Bullish"],nl_NL:["Long Lower Shadow - Bullish"]},e.exports["Long Upper Shadow - Bearish_study"]={en:["Long Upper Shadow - Bearish"],nl_NL:["Long Upper Shadow - Bearish"]},e.exports["Marubozu Black - Bearish_study"]={en:["Marubozu Black - Bearish"],nl_NL:["Marubozu Black - Bearish"]},e.exports["Marubozu White - Bullish_study"]={en:["Marubozu White - Bullish"],nl_NL:["Marubozu White - Bullish"]},e.exports["Morning Doji Star - Bullish_study"]={en:["Morning Doji Star - Bullish"],nl_NL:["Morning Doji Star - Bullish"]},e.exports["Morning Star - Bullish_study"]={en:["Morning Star - Bullish"],nl_NL:["Morning Star - Bullish"]},e.exports["On Neck - Bearish_study"]={en:["On Neck - Bearish"],nl_NL:["On Neck - Bearish"]},e.exports["Piercing - Bullish_study"]={en:["Piercing - Bullish"],nl_NL:["Piercing - Bullish"]},
e.exports["Rising Three Methods - Bullish_study"]={en:["Rising Three Methods - Bullish"],nl_NL:["Rising Three Methods - Bullish"]},e.exports["Rising Window - Bullish_study"]={en:["Rising Window - Bullish"],nl_NL:["Rising Window - Bullish"]},e.exports["Shooting Star - Bearish_study"]={en:["Shooting Star - Bearish"],nl_NL:["Shooting Star - Bearish"]},e.exports["Three Black Crows - Bearish_study"]={en:["Three Black Crows - Bearish"],nl_NL:["Three Black Crows - Bearish"]},e.exports["Three White Soldiers - Bullish_study"]={en:["Three White Soldiers - Bullish"],nl_NL:["Three White Soldiers - Bullish"]},e.exports["Tri-Star - Bearish_study"]={en:["Tri-Star - Bearish"],nl_NL:["Tri-Star - Bearish"]},e.exports["Tri-Star - Bullish_study"]={en:["Tri-Star - Bullish"],nl_NL:["Tri-Star - Bullish"]},e.exports["Tweezer Top - Bearish_study"]={en:["Tweezer Top - Bearish"],nl_NL:["Tweezer Top - Bearish"]},e.exports["Upside Tasuki Gap - Bullish_study"]={en:["Upside Tasuki Gap - Bullish"],nl_NL:["Upside Tasuki Gap - Bullish"]},e.exports.SuperTrend_study={en:["SuperTrend"],nl_NL:["SuperTrend"]},e.exports["Average Price_study"]={en:["Average Price"],nl_NL:["Average Price"]},e.exports["Typical Price_study"]={en:["Typical Price"],nl_NL:["Typical Price"]},e.exports["Median Price_study"]={en:["Median Price"],nl_NL:["Median Price"]},e.exports["Money Flow Index_study"]={en:["Money Flow Index"],nl_NL:["Money Flow Index"]},e.exports["Moving Average Double_study"]={en:["Moving Average Double"],nl_NL:["Moving Average Double"]},e.exports["Moving Average Triple_study"]={en:["Moving Average Triple"],nl_NL:["Moving Average Triple"]},e.exports["Moving Average Adaptive_study"]={en:["Moving Average Adaptive"],nl_NL:["Moving Average Adaptive"]},e.exports["Moving Average Hamming_study"]={en:["Moving Average Hamming"],nl_NL:["Moving Average Hamming"]},e.exports["Moving Average Modified_study"]={en:["Moving Average Modified"],nl_NL:["Moving Average Modified"]},e.exports["Moving Average Multiple_study"]={en:["Moving Average Multiple"],nl_NL:["Moving Average Multiple"]},e.exports["Linear Regression Slope_study"]={en:["Linear Regression Slope"],nl_NL:["Linear Regression Slope"]},e.exports["Standard Error_study"]={en:["Standard Error"],nl_NL:["Standard Error"]},e.exports["Standard Error Bands_study"]={en:["Standard Error Bands"],nl_NL:["Standard Error Bands"]},e.exports["Correlation - Log_study"]={en:["Correlation - Log"],nl_NL:["Correlation - Log"]},e.exports["Standard Deviation_study"]={en:["Standard Deviation"],nl_NL:["Standard Deviation"]},e.exports["Chaikin Volatility_study"]={en:["Chaikin Volatility"],nl_NL:["Chaikin Volatility"]},e.exports["Volatility Close-to-Close_study"]={en:["Volatility Close-to-Close"],nl_NL:["Volatility Close-to-Close"]},e.exports["Volatility Zero Trend Close-to-Close_study"]={en:["Volatility Zero Trend Close-to-Close"],nl_NL:["Volatility Zero Trend Close-to-Close"]},e.exports["Volatility O-H-L-C_study"]={en:["Volatility O-H-L-C"],nl_NL:["Volatility O-H-L-C"]},e.exports["Volatility Index_study"]={en:["Volatility Index"],
nl_NL:["Volatility Index"]},e.exports["Trend Strength Index_study"]={en:["Trend Strength Index"],nl_NL:["Trend Strength Index"]},e.exports["Majority Rule_study"]={en:["Majority Rule"],nl_NL:["Majority Rule"]},e.exports["Advance Decline Line_study"]={en:["Advance Decline Line"],nl_NL:["Advance Decline Line"]},e.exports["Advance Decline Ratio_study"]={en:["Advance Decline Ratio"],nl_NL:["Advance Decline Ratio"]},e.exports["Advance/Decline Ratio (Bars)_study"]={en:["Advance/Decline Ratio (Bars)"],nl_NL:["Advance/Decline Ratio (Bars)"]},e.exports["BarUpDn Strategy_study"]={en:["BarUpDn Strategy"],nl_NL:["BarUpDn Strategy"]},e.exports["Bollinger Bands Strategy directed_study"]={en:["Bollinger Bands Strategy directed"],nl_NL:["Bollinger Bands Strategy directed"]},e.exports["Bollinger Bands Strategy_study"]={en:["Bollinger Bands Strategy"],nl_NL:["Bollinger Bands Strategy"]},e.exports.ChannelBreakOutStrategy_study={en:["ChannelBreakOutStrategy"],nl_NL:["ChannelBreakOutStrategy"]},e.exports.Compare_study={en:["Compare"],nl_NL:["Vergelijken"]},e.exports["Conditional Expressions_study"]={en:["Conditional Expressions"],nl_NL:["Conditional Expressions"]},e.exports.ConnorsRSI_study={en:["ConnorsRSI"],nl_NL:["ConnorsRSI"]},e.exports["Consecutive Up/Down Strategy_study"]={en:["Consecutive Up/Down Strategy"],nl_NL:["Consecutive Up/Down Strategy"]},e.exports["Cumulative Volume Index_study"]={en:["Cumulative Volume Index"],nl_NL:["Cumulative Volume Index"]},e.exports["Divergence Indicator_study"]={en:["Divergence Indicator"],nl_NL:["Divergence Indicator"]},e.exports["Greedy Strategy_study"]={en:["Greedy Strategy"],nl_NL:["Greedy Strategy"]},e.exports["InSide Bar Strategy_study"]={en:["InSide Bar Strategy"],nl_NL:["InSide Bar Strategy"]},e.exports["Keltner Channel Strategy_study"]={en:["Keltner Channel Strategy"],nl_NL:["Keltner Channel Strategy"]},e.exports["Linear Regression_study"]={en:["Linear Regression"],nl_NL:["Linear Regression"]},e.exports["MACD Strategy_study"]={en:["MACD Strategy"],nl_NL:["MACD Strategy"]},e.exports["Momentum Strategy_study"]={en:["Momentum Strategy"],nl_NL:["Momentum Strategy"]},e.exports["Moon Phases_study"]={en:["Moon Phases"],nl_NL:["Moon Phases"]},e.exports["Moving Average Convergence/Divergence_study"]={en:["Moving Average Convergence/Divergence"],nl_NL:["Moving Average Convergence/Divergence"]},e.exports["MovingAvg Cross_study"]={en:["MovingAvg Cross"],nl_NL:["MovingAvg Cross"]},e.exports["MovingAvg2Line Cross_study"]={en:["MovingAvg2Line Cross"],nl_NL:["MovingAvg2Line Cross"]},e.exports["OutSide Bar Strategy_study"]={en:["OutSide Bar Strategy"],nl_NL:["OutSide Bar Strategy"]},e.exports.Overlay_study={en:["Overlay"],nl_NL:["Overlay"]},e.exports["Parabolic SAR Strategy_study"]={en:["Parabolic SAR Strategy"],nl_NL:["Parabolic SAR Strategy"]},e.exports["Pivot Extension Strategy_study"]={en:["Pivot Extension Strategy"],nl_NL:["Pivot Extension Strategy"]},e.exports["Pivot Points High Low_study"]={en:["Pivot Points High Low"],nl_NL:["Pivot Points High Low"]},e.exports["Pivot Reversal Strategy_study"]={
en:["Pivot Reversal Strategy"],nl_NL:["Pivot Reversal Strategy"]},e.exports["Price Channel Strategy_study"]={en:["Price Channel Strategy"],nl_NL:["Price Channel Strategy"]},e.exports["RSI Strategy_study"]={en:["RSI Strategy"],nl_NL:["RSI Strategy"]},e.exports["SMI Ergodic Indicator_study"]={en:["SMI Ergodic Indicator"],nl_NL:["SMI Ergodic Indicator"]},e.exports["SMI Ergodic Oscillator_study"]={en:["SMI Ergodic Oscillator"],nl_NL:["SMI Ergodic Oscillator"]},e.exports["Stochastic Slow Strategy_study"]={en:["Stochastic Slow Strategy"],nl_NL:["Stochastic Slow Strategy"]},e.exports["Volatility Stop_study"]={en:["Volatility Stop"],nl_NL:["Volatility Stop"]},e.exports["Volty Expan Close Strategy_study"]={en:["Volty Expan Close Strategy"],nl_NL:["Volty Expan Close Strategy"]},e.exports["Woodies CCI_study"]={en:["Woodies CCI"],nl_NL:["Woodies CCI"]},e.exports["Anchored Volume Profile_study"]={en:["Anchored Volume Profile"],nl_NL:["Anchored Volume Profile"]},e.exports["Trading Sessions_study"]={en:["Trading Sessions"],nl_NL:["Trading Sessions"]},e.exports["Cup and Handle Chart Pattern_study"]={en:["Cup and Handle Chart Pattern"],nl_NL:["Cup and Handle Chart Pattern"]},e.exports["Inverted Cup and Handle Chart Pattern_study"]={en:["Inverted Cup and Handle Chart Pattern"],nl_NL:["Inverted Cup and Handle Chart Pattern"]},e.exports["Head and Shoulders Chart Pattern_study"]={en:["Head and Shoulders Chart Pattern"],nl_NL:["Head and Shoulders Chart Pattern"]},e.exports["Inverted Head and Shoulders Chart Pattern_study"]={en:["Inverted Head and Shoulders Chart Pattern"],nl_NL:["Inverted Head and Shoulders Chart Pattern"]}},89633:e=>{e.exports={en:["Anchored Volume Profile"],nl_NL:["Anchored Volume Profile"]}},25705:e=>{e.exports={en:["Fixed Range Volume Profile"],nl_NL:["Fixed Range Volume Profile"]}},24261:e=>{e.exports={en:["Vol"],nl_NL:["Vol"]}},51077:e=>{e.exports={en:["Minor"],nl_NL:["Minor"]}},922:e=>{e.exports={en:["Minute"],nl_NL:["Minute"]}},91405:e=>{e.exports={en:["Text"],nl_NL:["Text"]}},78972:e=>{e.exports={en:["Couldn't copy"],nl_NL:["Couldn't copy"]}},10615:e=>{e.exports={en:["Couldn't cut"],nl_NL:["Couldn't cut"]}},81518:e=>{e.exports={en:["Couldn't paste"],nl_NL:["Couldn't paste"]}},83140:e=>{e.exports={en:["Countdown to bar close"],nl_NL:["Countdown To Bar Close"]}},10871:e=>{e.exports={en:["Colombo"],nl_NL:["Colombo"]}},55761:e=>{e.exports={en:["Columns"],nl_NL:["Kolommen"]}},9818:e=>{e.exports={en:["Comment"],nl_NL:["Reactie"]}},53942:e=>{e.exports={en:["Compare or Add Symbol"],nl_NL:["Vergelijk of voeg een symbool toe"]}},12086:e=>{e.exports={en:["Compilation error"],nl_NL:["Compilation error"]}},48141:e=>{e.exports={en:["Confirm Inputs"],nl_NL:["Confirm Inputs"]}},38917:e=>{e.exports={en:["Copenhagen"],nl_NL:["Copenhagen"]}},49680:e=>{e.exports={en:["Copy"],nl_NL:["Kopiëren"]}},66134:e=>{e.exports={en:["Copy Chart Layout"],nl_NL:["Kopieer grafiek lay-out"]}},63553:e=>{e.exports={en:["Copy price"],nl_NL:["Copy price"]}},65736:e=>{e.exports={en:["Cairo"],nl_NL:["Cairo"]}},25381:e=>{e.exports={en:["Callout"],
nl_NL:["Aanroepen"]}},45054:e=>{e.exports={en:["Candles"],nl_NL:["Candles"]}},30948:e=>{e.exports={en:["Caracas"],nl_NL:["Caracas"]}},70409:e=>{e.exports={en:["Casablanca"],nl_NL:["Casablanca"]}},37276:e=>{e.exports={en:["Change"],nl_NL:["Change"]}},85124:e=>{e.exports={en:["Change Symbol"],nl_NL:["Verander Symbool"]}},2569:e=>{e.exports={en:["Change interval"],nl_NL:["Verander interval"]}},9687:e=>{e.exports={en:["Change interval. Press number or comma"],nl_NL:["Change interval. Press number or comma"]}},36332:e=>{e.exports={en:["Change symbol. Start typing symbol name"],nl_NL:["Change symbol. Start typing symbol name"]}},48566:e=>{e.exports={en:["Change scale currency"],nl_NL:["Change scale currency"]}},85110:e=>{e.exports={en:["Change scale unit"],nl_NL:["Change scale unit"]}},56275:e=>{e.exports={en:["Chart #{index}"],nl_NL:["Chart #{index}"]}},39950:e=>{e.exports={en:["Chart Properties"],nl_NL:["Grafiek eigenschappen"]}},98856:e=>{e.exports={en:["Chart by TradingView"],nl_NL:["Chart by TradingView"]}},1136:e=>{e.exports={en:["Chart for {symbol}, {interval}"],nl_NL:["Chart for {symbol}, {interval}"]}},69804:e=>{e.exports={en:["Chart image copied to clipboard {emoji}"],nl_NL:["Chart image copied to clipboard {emoji}"]}},6655:e=>{e.exports={en:["Chart image embed code copied to clipboard {emoji}"],nl_NL:["Chart image embed code copied to clipboard {emoji}"]}},36549:e=>{e.exports={en:["Chatham Islands"],nl_NL:["Chatham Islands"]}},72452:e=>{e.exports={en:["Chicago"],nl_NL:["Chicago"]}},50349:e=>{e.exports={en:["Chongqing"],nl_NL:["Chongqing"]}},91944:e=>{e.exports={en:["Circle"],nl_NL:["Circle"]}},14985:e=>{e.exports={en:["Click to set a point"],nl_NL:["Click to set a point"]}},12537:e=>{e.exports={en:["Clone"],nl_NL:["Kloon"]}},62578:e=>{e.exports={en:["Close"],nl_NL:["Slot"]}},264:e=>{e.exports={en:["Create limit order"],nl_NL:["Create limit order"]}},6969:e=>{e.exports={en:["Cross"],nl_NL:["Kruis"]}},74334:e=>{e.exports={en:["Cross Line"],nl_NL:["Cross Line"]}},59396:e=>{e.exports={en:["Currencies"],nl_NL:["Valuta"]}},20177:e=>{e.exports={en:["Current interval and above"],nl_NL:["Current interval and above"]}},494:e=>{e.exports={en:["Current interval and below"],nl_NL:["Current interval and below"]}},60668:e=>{e.exports={en:["Current interval only"],nl_NL:["Current interval only"]}},78609:e=>{e.exports={en:["Curve"],nl_NL:["Curve"]}},87380:e=>{e.exports={en:["Cycle"],nl_NL:["Cycle"]}},84031:e=>{e.exports={en:["Cyclic Lines"],nl_NL:["Cyclische lijnen"]}},93191:e=>{e.exports={en:["Cypher Pattern"],nl_NL:["Cypher Pattern"]}},7219:e=>{e.exports={en:["A layout with that name already exists"],nl_NL:["A layout with that name already exists"]}},67635:e=>{e.exports={en:["A layout with that name already exists. Do you want to overwrite it?"],nl_NL:["A layout with that name already exists. Do you want to overwrite it?"]}},46712:e=>{e.exports={en:["ABCD Pattern"],nl_NL:["ABCD patroon"]}},36485:e=>{e.exports={en:["Amsterdam"],nl_NL:["Amsterdam"]}},42630:e=>{e.exports={en:["Anchorage"],nl_NL:["Anchorage"]}},63209:e=>{
e.exports={en:["Anchored Note"],nl_NL:["Geankerde notitie"]}},42669:e=>{e.exports={en:["Anchored Text"],nl_NL:["Geankerde tekst"]}},84541:e=>{e.exports={en:["Anchored VWAP"],nl_NL:["Anchored VWAP"]}},77401:e=>{e.exports={en:["Access error"],nl_NL:["Access error"]}},46501:e=>{e.exports={en:["Add Symbol"],nl_NL:["Voeg symbool toe"]}},69709:e=>{e.exports={en:["Add alert on {title}"],nl_NL:["Add alert on {title}"]}},89295:e=>{e.exports={en:["Add alert on {title} at {price}"],nl_NL:["Add alert on {title} at {price}"]}},2439:e=>{e.exports={en:["Add financial metric for {instrumentName}"],nl_NL:["Add financial metric for {instrumentName}"]}},35088:e=>{e.exports={en:["Add indicator/strategy on {studyTitle}"],nl_NL:["Add indicator/strategy on {studyTitle}"]}},39873:e=>{e.exports={en:["Add text note for {symbol}"],nl_NL:["Add Text Note for {symbol}"]}},35679:e=>{e.exports={en:["Add this financial metric to entire layout"],nl_NL:["Add this financial metric to entire layout"]}},10996:e=>{e.exports={en:["Add this financial metric to favorites"],nl_NL:["Add this financial metric to favorites"]}},26090:e=>{e.exports={en:["Add this indicator to entire layout"],nl_NL:["Add this indicator to entire layout"]}},92957:e=>{e.exports={en:["Add this indicator to favorites"],nl_NL:["Add this indicator to favorites"]}},95754:e=>{e.exports={en:["Add this strategy to entire layout"],nl_NL:["Add this strategy to entire layout"]}},39010:e=>{e.exports={en:["Add this symbol to entire layout"],nl_NL:["Add this symbol to entire layout"]}},426:e=>{e.exports={en:["Adelaide"],nl_NL:["Adelaide"]}},40452:e=>{e.exports={en:["Always invisible"],nl_NL:["Always Invisible"]}},36299:e=>{e.exports={en:["Always visible"],nl_NL:["Always Visible"]}},81442:e=>{e.exports={en:["All indicators and drawing tools"],nl_NL:["All Indicators And Drawing Tools"]}},58026:e=>{e.exports={en:["All intervals"],nl_NL:["All intervals"]}},78358:e=>{e.exports={en:["Apply default"],nl_NL:["Apply Default"]}},22437:e=>{e.exports={en:["Apply these indicators to entire layout"],nl_NL:["Apply these indicators to entire layout"]}},27072:e=>{e.exports={en:["Apr"],nl_NL:["apr"]}},59324:e=>{e.exports={en:["Arc"],nl_NL:["Boog"]}},34456:e=>{e.exports={en:["Area"],nl_NL:["Gebied"]}},11858:e=>{e.exports={en:["Arrow"],nl_NL:["Pijl"]}},34247:e=>{e.exports={en:["Arrow Down"],nl_NL:["Arrow Down"]}},36352:e=>{e.exports={en:["Arrow Marker"],nl_NL:["Arrow Marker"]}},73193:e=>{e.exports={en:["Arrow Mark Down"],nl_NL:["Pijl teken beneden"]}},1949:e=>{e.exports={en:["Arrow Mark Left"],nl_NL:["Pijl teken links"]}},86275:e=>{e.exports={en:["Arrow Mark Right"],nl_NL:["Pijl teken rechts"]}},62453:e=>{e.exports={en:["Arrow Mark Up"],nl_NL:["Pijl teken omhoog"]}},77231:e=>{e.exports={en:["Arrow Up"],nl_NL:["Arrow Up"]}},98128:e=>{e.exports={en:["Astana"],nl_NL:["Astana"]}},63627:e=>{e.exports={en:["Ashgabat"],nl_NL:["Ashkhabad"]}},72445:e=>{e.exports={en:["At close"],nl_NL:["At close"]}},73702:e=>{e.exports={en:["Athens"],nl_NL:["Athene"]}},21469:e=>{e.exports={en:["Auto"],nl_NL:["Auto"]}},24157:e=>{e.exports={
en:["Auto (fits data to screen)"],nl_NL:["Auto (Fits Data To Screen)"]}},46450:e=>{e.exports={en:["Aug"],nl_NL:["aug"]}},21841:e=>{e.exports={en:["Average close price label"],nl_NL:["Average close price label"]}},16138:e=>{e.exports={en:["Average close price line"],nl_NL:["Average close price line"]}},73025:e=>{e.exports={en:["Avg"],nl_NL:["Avg"]}},73905:e=>{e.exports={en:["Bogota"],nl_NL:["Bogota"]}},90594:e=>{e.exports={en:["Bahrain"],nl_NL:["Bahrain"]}},70540:e=>{e.exports={en:["Balloon"],nl_NL:["Ballon"]}},47045:e=>{e.exports={en:["Bangkok"],nl_NL:["Bangkok"]}},76651:e=>{e.exports={en:["Bar Replay isn't available for this chart type. Do you want to exit Bar Replay?"],nl_NL:["Bar Replay isn't available for this chart type. Do you want to exit Bar Replay?"]}},68054:e=>{e.exports={en:["Bar Replay isn't available for this time interval. Do you want to exit Bar Replay?"],nl_NL:["Bar Replay isn't available for this time interval. Do you want to exit Bar Replay?"]}},27377:e=>{e.exports={en:["Bars"],nl_NL:["Bars"]}},81994:e=>{e.exports={en:["Bars Pattern"],nl_NL:["Bars patroon"]}},59213:e=>{e.exports={en:["Baseline"],nl_NL:["Baseline"]}},71797:e=>{e.exports={en:["Belgrade"],nl_NL:["Belgrade"]}},64313:e=>{e.exports={en:["Berlin"],nl_NL:["Berlijn"]}},43539:e=>{e.exports={en:["Brush"],nl_NL:["Borstel"]}},91499:e=>{e.exports={en:["Brussels"],nl_NL:["Brussels"]}},70876:e=>{e.exports={en:["Bratislava"],nl_NL:["Bratislava"]}},55481:e=>{e.exports={en:["Bring forward"],nl_NL:["Breng naar voren"]}},17293:e=>{e.exports={en:["Bring to front"],nl_NL:["Breng naar voren"]}},79336:e=>{e.exports={en:["Brisbane"],nl_NL:["Brisbane"]}},33672:e=>{e.exports={en:["Bucharest"],nl_NL:["Bucharest"]}},20313:e=>{e.exports={en:["Budapest"],nl_NL:["Budapest"]}},25282:e=>{e.exports={en:["Buenos Aires"],nl_NL:["Buenos Aires"]}},46768:e=>{e.exports={en:["By TradingView"],nl_NL:["By TradingView"]}},54280:e=>{e.exports={en:["Go to date"],nl_NL:["Go to date"]}},74975:e=>{e.exports={en:["Go to {lineToolName}"],nl_NL:["Go to {lineToolName}"]}},15462:e=>{e.exports={en:["Got it"],nl_NL:["Got it"]}},47460:e=>{e.exports={en:["Gann Box"],nl_NL:["Gann box"]}},48683:e=>{e.exports={en:["Gann Fan"],nl_NL:["Gann waaier"]}},44763:e=>{e.exports={en:["Gann Square"],nl_NL:["Gann vierkant"]}},60707:e=>{e.exports={en:["Gann Square Fixed"],nl_NL:["Gann Square Fixed"]}},46808:e=>{e.exports={en:["Ghost Feed"],nl_NL:["Ghost Feed"]}},57726:e=>{e.exports={en:["Grand supercycle"],nl_NL:["Grand Supercycle"]}},34914:e=>{e.exports={en:["Do you really want to delete Study Template '{name}' ?"],nl_NL:["Do you really want to delete Study Template '{name}' ?"]}},77125:e=>{e.exports={en:["Double Curve"],nl_NL:["Double Curve"]}},9430:e=>{e.exports={en:["Double-click any edge to reset layout grid"],nl_NL:["Double-click any edge to reset layout grid"]}},75296:e=>{e.exports={en:["Double-click to finish Path"],nl_NL:["Double-click to finish Path"]}},17409:e=>{e.exports={en:["Double-click to finish Polyline"],nl_NL:["Double-click to finish Polyline"]}},57131:e=>{e.exports={
en:["Data Provided by"],nl_NL:["Data Provided by"]}},62154:e=>{e.exports={en:["Date"],nl_NL:["Datum"]}},85444:e=>{e.exports={en:["Date Range"],nl_NL:["Datum reikwijdte"]}},47017:e=>{e.exports={en:["Date and Price Range"],nl_NL:["Date and Price Range"]}},32084:e=>{e.exports={en:["Dec"],nl_NL:["dec"]}},23403:e=>{e.exports={en:["Degree"],nl_NL:["Degree"]}},27358:e=>{e.exports={en:["Denver"],nl_NL:["Denver"]}},24959:e=>{e.exports={en:["Dhaka"],nl_NL:["Dhaka"]}},15179:e=>{e.exports={en:["Diamond"],nl_NL:["Diamond"]}},91544:e=>{e.exports={en:["Disjoint Channel"],nl_NL:["Disjoint Channel"]}},70132:e=>{e.exports={en:["Displacement"],nl_NL:["Displacement"]}},93864:e=>{e.exports={en:["Drawings toolbar"],nl_NL:["Drawings Toolbar"]}},96890:e=>{e.exports={en:["Draw Horizontal Line at"],nl_NL:["Draw Horizontal Line at"]}},23650:e=>{e.exports={en:["Dubai"],nl_NL:["Dubai"]}},79716:e=>{e.exports={en:["Dublin"],nl_NL:["Dublin"]}},73456:e=>{e.exports={en:["Emoji"],nl_NL:["Emoji"]}},9541:e=>{e.exports={en:["Enter a new chart layout name"],nl_NL:["Voer een nieuwe grafiek lay-out naam in"]}},80943:e=>{e.exports={en:["Elliott Correction Wave (ABC)"],nl_NL:["Elliott Correction Wave (ABC)"]}},75112:e=>{e.exports={en:["Elliott Double Combo Wave (WXY)"],nl_NL:["Elliott Double Combo Wave (WXY)"]}},61114:e=>{e.exports={en:["Elliott Impulse Wave (12345)"],nl_NL:["Elliott Impulse Wave (12345)"]}},72359:e=>{e.exports={en:["Elliott Triangle Wave (ABCDE)"],nl_NL:["Elliott Triangle Wave (ABCDE)"]}},76129:e=>{e.exports={en:["Elliott Triple Combo Wave (WXYXZ)"],nl_NL:["Elliott Triple Combo Wave (WXYXZ)"]}},78996:e=>{e.exports={en:["Ellipse"],nl_NL:["Ovaal"]}},52788:e=>{e.exports={en:["Extended Line"],nl_NL:["Extended Line"]}},86905:e=>{e.exports={en:["Exchange"],nl_NL:["Beurs"]}},19271:e=>{e.exports={en:["Existing pane above"],nl_NL:["Existing Pane Above"]}},46545:e=>{e.exports={en:["Existing pane below"],nl_NL:["Existing Pane Below"]}},20138:e=>{e.exports={en:["Forecast"],nl_NL:["Voorspelling"]}},2507:e=>{e.exports={en:["Feb"],nl_NL:["feb"]}},59005:e=>{e.exports={en:["Fib Channel"],nl_NL:["Fib kanaal"]}},82330:e=>{e.exports={en:["Fib Circles"],nl_NL:["Fib cirkels"]}},55986:e=>{e.exports={en:["Fib Retracement"],nl_NL:["Fib teruggang"]}},33880:e=>{e.exports={en:["Fib Speed Resistance Arcs"],nl_NL:["Fib snelheid weerstandsbogen"]}},2395:e=>{e.exports={en:["Fib Speed Resistance Fan"],nl_NL:["Fib snelheid weerstandswaaier"]}},39014:e=>{e.exports={en:["Fib Spiral"],nl_NL:["Fib spiraal"]}},30622:e=>{e.exports={en:["Fib Time Zone"],nl_NL:["Fib tijdszone"]}},85042:e=>{e.exports={en:["Fib Wedge"],nl_NL:["Fib wig"]}},33885:e=>{e.exports={en:["Flag"],nl_NL:["Flag"]}},14600:e=>{e.exports={en:["Flag Mark"],nl_NL:["Vlag markering"]}},45051:e=>{e.exports={en:["Flat Top/Bottom"],nl_NL:["Vlakke top/bodem"]}},63271:e=>{e.exports={en:["Flipped"],nl_NL:["Omgedraaid"]}},39643:e=>{e.exports={en:["Fraction part is invalid."],nl_NL:["Fraction part is invalid."]}},24077:e=>{e.exports={en:["Fundamental studies are no longer available on charts"],
nl_NL:["Fundamental studies are no longer available on charts"]}},31561:e=>{e.exports={en:["Kolkata"],nl_NL:["Kolkata"]}},54533:e=>{e.exports={en:["Kathmandu"],nl_NL:["Kathmandu"]}},83490:e=>{e.exports={en:["Kagi"],nl_NL:["Kagi"]}},70913:e=>{e.exports={en:["Karachi"],nl_NL:["Karachi"]}},76614:e=>{e.exports={en:["Kuwait"],nl_NL:["Kuwait"]}},38561:e=>{e.exports={en:["Kuala Lumpur"],nl_NL:["Kuala Lumpur"]}},99906:e=>{e.exports={en:["HLC area"],nl_NL:["HLC area"]}},34491:e=>{e.exports={en:["Ho Chi Minh"],nl_NL:["Ho Chi Minh"]}},13459:e=>{e.exports={en:["Hollow candles"],nl_NL:["Lege candles"]}},48861:e=>{e.exports={en:["Hong Kong"],nl_NL:["Hong Kong"]}},79668:e=>{e.exports={en:["Honolulu"],nl_NL:["Honolulu"]}},21795:e=>{e.exports={en:["Horizontal Line"],nl_NL:["Horizontale lijn"]}},25487:e=>{e.exports={en:["Horizontal Ray"],nl_NL:["Horizontale straal"]}},21928:e=>{e.exports={en:["Head and Shoulders"],nl_NL:["Head and Shoulders"]}},63876:e=>{e.exports={en:["Heikin Ashi"],nl_NL:["Heikin Ashi"]}},48203:e=>{e.exports={en:["Helsinki"],nl_NL:["Helsinki"]}},27298:e=>{e.exports={en:["Hide"],nl_NL:["Verbergen"]}},47074:e=>{e.exports={en:["Hide all"],nl_NL:["Hide all"]}},52563:e=>{e.exports={en:["Hide all drawings"],nl_NL:["Hide all drawings"]}},90763:e=>{e.exports={en:["Hide all drawings and indicators"],nl_NL:["Hide all drawings and indicators"]}},18216:e=>{e.exports={en:["Hide all drawings, indicators, positions & orders"],nl_NL:["Hide all drawings, indicators, positions & orders"]}},78525:e=>{e.exports={en:["Hide all indicators"],nl_NL:["Hide all indicators"]}},42164:e=>{e.exports={en:["Hide all positions & orders"],nl_NL:["Hide all positions & orders"]}},3217:e=>{e.exports={en:["Hide drawings"],nl_NL:["Hide drawings"]}},97878:e=>{e.exports={en:["Hide events on chart"],nl_NL:["Hide Events on Chart"]}},72351:e=>{e.exports={en:["Hide indicators"],nl_NL:["Hide indicators"]}},28345:e=>{e.exports={en:["Hide marks on bars"],nl_NL:["Verberg markeringen op bars"]}},92226:e=>{e.exports={en:["Hide positions & orders"],nl_NL:["Hide positions & orders"]}},78254:e=>{e.exports={en:["High"],nl_NL:["Hoog"]}},98236:e=>{e.exports={en:["High-low"],nl_NL:["High-low"]}},99479:e=>{e.exports={en:["High and low price labels"],nl_NL:["High and low price labels"]}},33766:e=>{e.exports={en:["High and low price lines"],nl_NL:["High and low price lines"]}},69476:e=>{e.exports={en:["Highlighter"],nl_NL:["Highlighter"]}},72819:e=>{e.exports={en:["Histogram is too large, please increase {boldHighlightStart}Row Size{boldHighlightEnd} in the settings window."],nl_NL:["Histogram is too large, please increase {boldHighlightStart}Row Size{boldHighlightEnd} in the settings window."]}},94966:e=>{e.exports={en:["Histogram is too large, please increase {boldHighlightStart}Ticks Per Row{boldHighlightEnd} in the settings window."],nl_NL:["Histogram is too large, please increase {boldHighlightStart}Ticks Per Row{boldHighlightEnd} in the settings window."]}},66751:e=>{e.exports={
en:["Histogram is too large, please reduce {boldHighlightStart}Row Size{boldHighlightEnd} in the settings window."],nl_NL:["Histogram is too large, please reduce {boldHighlightStart}Row Size{boldHighlightEnd} in the settings window."]}},68065:e=>{e.exports={en:["Image"],nl_NL:["Image"]}},80185:e=>{e.exports={en:["Intervals less than {resolution} are not supported for {ticker}."],nl_NL:["Intervals less than {resolution} are not supported for {ticker}."]}},10268:e=>{e.exports={en:["Intermediate"],nl_NL:["Intermediate"]}},14285:e=>{e.exports={en:["Invalid Symbol"],nl_NL:["Onjuist symbool"]}},52969:e=>{e.exports={en:["Invalid symbol"],nl_NL:["Invalid symbol"]}},37189:e=>{e.exports={en:["Invert scale"],nl_NL:["Invert Scale"]}},89999:e=>{e.exports={en:["Indexed to 100"],nl_NL:["Indexed to 100"]}},46850:e=>{e.exports={en:["Indicators value labels"],nl_NL:["Indicators value labels"]}},54418:e=>{e.exports={en:["Indicators name labels"],nl_NL:["Indicators name labels"]}},40490:e=>{e.exports={en:["Indicators, Metrics and Strategies. Press slash"],nl_NL:["Indicators, Metrics and Strategies. Press slash"]}},15992:e=>{e.exports={en:["Info Line"],nl_NL:["Info Line"]}},87829:e=>{e.exports={en:["Insert indicator"],nl_NL:["Voeg indicator toe"]}},91612:e=>{e.exports={en:["Inside"],nl_NL:["Binnen"]}},41686:e=>{e.exports={en:["Inside Pitchfork"],nl_NL:["Interne hooivork"]}},37913:e=>{e.exports={en:["Icon"],nl_NL:["Icoon"]}},78326:e=>{e.exports={en:["Istanbul"],nl_NL:["Istanbul"]}},39585:e=>{e.exports={en:["Johannesburg"],nl_NL:["Johannesburg"]}},14995:e=>{e.exports={en:["Jakarta"],nl_NL:["Jakarta"]}},62310:e=>{e.exports={en:["Jan"],nl_NL:["jan"]}},36057:e=>{e.exports={en:["Jerusalem"],nl_NL:["Jerusalem"]}},53786:e=>{e.exports={en:["Jul"],nl_NL:["jul"]}},429:e=>{e.exports={en:["Jun"],nl_NL:["jun"]}},67560:e=>{e.exports={en:["Juneau"],nl_NL:["Juneau"]}},62329:e=>{e.exports={en:["On the left"],nl_NL:["On the left"]}},55813:e=>{e.exports={en:["On the right"],nl_NL:["On the right"]}},64818:e=>{e.exports={en:["Only {availableResolutions} intervals are supported for {ticker}."],nl_NL:["Only {availableResolutions} intervals are supported for {ticker}."]}},21064:e=>{e.exports={en:["Oops!"],nl_NL:["Oops!"]}},51221:e=>{e.exports={en:["Object Tree"],nl_NL:["Object Tree"]}},12179:e=>{e.exports={en:["Oct"],nl_NL:["Okt"]}},16610:e=>{e.exports={en:["Open"],nl_NL:["Openen"]}},46005:e=>{e.exports={en:["Original"],nl_NL:["Origineel"]}},75722:e=>{e.exports={en:["Oslo"],nl_NL:["Oslo"]}},65318:e=>{e.exports={en:["Low"],nl_NL:["Laag"]}},55382:e=>{e.exports={en:["Load layout. Press period"],nl_NL:["Load layout. Press period"]}},5837:e=>{e.exports={en:["Lock"],nl_NL:["Op slot"]}},79777:e=>{e.exports={en:["Lock/unlock"],nl_NL:["Vergrendel/ontgrendel"]}},66005:e=>{e.exports={en:["Lock vertical cursor line by time"],nl_NL:["Lock vertical cursor line by time"]}},14017:e=>{e.exports={en:["Lock price to bar ratio"],nl_NL:["Lock Price To Bar Ratio"]}},16170:e=>{e.exports={en:["Logarithmic"],nl_NL:["Logarithmic"]}},19439:e=>{e.exports={en:["London"],nl_NL:["Londen"]}},
74832:e=>{e.exports={en:["Long Position"],nl_NL:["Long positie"]}},28733:e=>{e.exports={en:["Los Angeles"],nl_NL:["Los Angeles"]}},85924:e=>{e.exports={en:["Label Down"],nl_NL:["Label Down"]}},52402:e=>{e.exports={en:["Label Up"],nl_NL:["Label Up"]}},5119:e=>{e.exports={en:["Labels"],nl_NL:["Labels"]}},19931:e=>{e.exports={en:["Lagos"],nl_NL:["Lagos"]}},63815:e=>{e.exports={en:["Last day change"],nl_NL:["Last day change"]}},59444:e=>{e.exports={en:["Lima"],nl_NL:["Lima"]}},3554:e=>{e.exports={en:["Line"],nl_NL:["Lijn"]}},9394:e=>{e.exports={en:["Line with markers"],nl_NL:["Met markeringen"]}},43588:e=>{e.exports={en:["Line break"],nl_NL:["Line break"]}},56982:e=>{e.exports={en:["Lines"],nl_NL:["Lines"]}},37367:e=>{e.exports={en:["Link to the chart image copied to clipboard {emoji}"],nl_NL:["Link to the chart image copied to clipboard {emoji}"]}},53375:e=>{e.exports={en:["Lisbon"],nl_NL:["Lisbon"]}},81038:e=>{e.exports={en:["Luxembourg"],nl_NL:["Luxembourg"]}},60663:e=>{e.exports={en:["Move the point to position the anchor then tap to place"],nl_NL:["Move the point to position the anchor then tap to place"]}},35049:e=>{e.exports={en:["Move to"],nl_NL:["Move to"]}},26493:e=>{e.exports={en:["Move scale to left"],nl_NL:["Move scale to left"]}},40789:e=>{e.exports={en:["Move scale to right"],nl_NL:["Move scale to right"]}},70382:e=>{e.exports={en:["Modified Schiff"],nl_NL:["Aangepaste Schiff"]}},57681:e=>{e.exports={en:["Modified Schiff Pitchfork"],nl_NL:["Aangepaste Schiff hooivork"]}},93907:e=>{e.exports={en:["More settings"]}},64039:e=>{e.exports={en:["Moscow"],nl_NL:["Moskou"]}},52066:e=>{e.exports={en:["Madrid"],nl_NL:["Madrid"]}},38365:e=>{e.exports={en:["Malta"],nl_NL:["Malta"]}},48991:e=>{e.exports={en:["Manila"],nl_NL:["Manila"]}},92767:e=>{e.exports={en:["Mar"],nl_NL:["mrt"]}},73332:e=>{e.exports={en:["Mexico City"],nl_NL:["Mexico City"]}},88314:e=>{e.exports={en:["Merge all scales into one"],nl_NL:["Merge all scales into one"]}},54215:e=>{e.exports={en:["Mixed"],nl_NL:["Mixed"]}},24866:e=>{e.exports={en:["Micro"],nl_NL:["Micro"]}},87957:e=>{e.exports={en:["Millennium"],nl_NL:["Millennium"]}},14724:e=>{e.exports={en:["Minuette"],nl_NL:["Minuette"]}},78273:e=>{e.exports={en:["Minuscule"],nl_NL:["Minuscule"]}},28941:e=>{e.exports={en:["Mirrored"],nl_NL:["Gespiegeld"]}},9865:e=>{e.exports={en:["Muscat"],nl_NL:["Muscat"]}},96935:e=>{e.exports={en:["N/A"],nl_NL:["N/A"]}},36252:e=>{e.exports={en:["No data here"],nl_NL:["No data here"]}},11254:e=>{e.exports={en:["No scale (fullscreen)"],nl_NL:["No Scale (Fullscreen)"]}},9140:e=>{e.exports={en:["No sync"],nl_NL:["No sync"]}},50910:e=>{e.exports={en:["No volume data"],nl_NL:["No volume data"]}},94389:e=>{e.exports={en:["Note"],nl_NL:["Notitie"]}},26899:e=>{e.exports={en:["Nov"],nl_NL:["nov"]}},67891:e=>{e.exports={en:["Norfolk Island"],nl_NL:["Norfolk Island"]}},40977:e=>{e.exports={en:["Nairobi"],nl_NL:["Nairobi"]}},40544:e=>{e.exports={en:["New York"],nl_NL:["New York"]}},66103:e=>{e.exports={en:["New Zealand"],nl_NL:["New Zealand"]}},15512:e=>{e.exports={
en:["New pane above"],nl_NL:["New pane above"]}},52160:e=>{e.exports={en:["New pane below"],nl_NL:["New pane below"]}},94600:e=>{e.exports={en:["Nicosia"],nl_NL:["Nicosia"]}},73013:e=>{e.exports={en:["Something went wrong"],nl_NL:["Something went wrong"]}},4509:e=>{e.exports={en:["Something went wrong. Please try again later."],nl_NL:["Something went wrong. Please try again later."]}},43047:e=>{e.exports={en:["Save New Chart Layout"],nl_NL:["Sla nieuwe grafiek lay-out op"]}},76266:e=>{e.exports={en:["Save as"],nl_NL:["Opslaan als"]}},55502:e=>{e.exports={en:["San Salvador"],nl_NL:["San Salvador"]}},30231:e=>{e.exports={en:["Santiago"],nl_NL:["Santiago"]}},91912:e=>{e.exports={en:["Sao Paulo"],nl_NL:["São Paulo"]}},43931:e=>{e.exports={en:["Scale currency"],nl_NL:["Scale currency"]}},43758:e=>{e.exports={en:["Scale price chart only"],nl_NL:["Schaal alleen prijsgrafiek"]}},40012:e=>{e.exports={en:["Scale unit"],nl_NL:["Scale unit"]}},69904:e=>{e.exports={en:["Schiff"],nl_NL:["Schiff"]}},42608:e=>{e.exports={en:["Schiff Pitchfork"],nl_NL:["Schiff hooivork"]}},76078:e=>{e.exports={en:["Script may be not updated if you leave the page."],nl_NL:["Script may be not updated if you leave the page."]}},32514:e=>{e.exports={en:["Settings"],nl_NL:["Instellingen"]}},70784:e=>{e.exports={en:["Second fraction part is invalid."],nl_NL:["Second fraction part is invalid."]}},75594:e=>{e.exports={en:["Security info"],nl_NL:["Security info"]}},21973:e=>{e.exports={en:["Send to back"],nl_NL:["Stuur naar achteren"]}},71179:e=>{e.exports={en:["Send backward"],nl_NL:["Stuur naar achteren"]}},26820:e=>{e.exports={en:["Seoul"],nl_NL:["Seoul"]}},6816:e=>{e.exports={en:["Sep"],nl_NL:["sep"]}},94031:e=>{e.exports={en:["Session"],nl_NL:["Session"]}},83298:e=>{e.exports={en:["Session volume profile"],nl_NL:["Session volume profile"]}},66707:e=>{e.exports={en:["Session breaks"],nl_NL:["Sessie onderbrekingen"]}},1852:e=>{e.exports={en:["Shanghai"],nl_NL:["Shanghai"]}},8075:e=>{e.exports={en:["Short Position"],nl_NL:["Short positie"]}},98334:e=>{e.exports={en:["Show"],nl_NL:["Toon"]}},85891:e=>{e.exports={en:["Show all drawings"],nl_NL:["Show all drawings"]}},25881:e=>{e.exports={en:["Show all drawings and indicators"],nl_NL:["Show all drawings and indicators"]}},86738:e=>{e.exports={en:["Show all drawings, indicators, positions & orders"],nl_NL:["Show all drawings, indicators, positions & orders"]}},98753:e=>{e.exports={en:["Show all indicators"],nl_NL:["Show all indicators"]}},55418:e=>{e.exports={en:["Show all ideas"],nl_NL:["Show All Ideas"]}},20506:e=>{e.exports={en:["Show all positions & orders"],nl_NL:["Show all positions & orders"]}},33158:e=>{e.exports={en:["Show continuous contract switch"],nl_NL:["Show continuous contract switch"]}},81465:e=>{e.exports={en:["Show contract expiration"],nl_NL:["Show contract expiration"]}},29449:e=>{e.exports={en:["Show dividends"],nl_NL:["Show Dividends"]}},37113:e=>{e.exports={en:["Show earnings"],nl_NL:["Show Earnings"]}},10261:e=>{e.exports={en:["Show ideas of followed users"],
nl_NL:["Show Ideas of Followed Users"]}},68112:e=>{e.exports={en:["Show latest news and Minds"],nl_NL:["Show latest news and Minds"]}},44020:e=>{e.exports={en:["Show my ideas only"],nl_NL:["Show My Ideas Only"]}},50849:e=>{e.exports={en:["Show splits"],nl_NL:["Show Splits"]}},67751:e=>{e.exports={en:["Signpost"],nl_NL:["Signpost"]}},77377:e=>{e.exports={en:["Singapore"],nl_NL:["Singapore"]}},39090:e=>{e.exports={en:["Sine Line"],nl_NL:["Sine Line"]}},66205:e=>{e.exports={en:["Square"],nl_NL:["Square"]}},86146:e=>{e.exports={en:["Studies limit exceeded: {number} studies per layout.\nPlease, remove some studies."],nl_NL:["Studies limit exceeded: {number} studies per layout.\nPlease, remove some studies."]}},92516:e=>{e.exports={en:["Style"],nl_NL:["Stijl"]}},61507:e=>{e.exports={en:["Stack on the left"],nl_NL:["Stack on the left"]}},97800:e=>{e.exports={en:["Stack on the right"],nl_NL:["Stack on the right"]}},85166:e=>{e.exports={en:["Start using keyboard navigation mode. Press {shortcut}"],nl_NL:["Start using keyboard navigation mode. Press {shortcut}"]}},4035:e=>{e.exports={en:["Stay in drawing mode"],nl_NL:["Blijf in teken modus"]}},69217:e=>{e.exports={en:["Step line"],nl_NL:["Step Line"]}},43114:e=>{e.exports={en:["Sticker"],nl_NL:["Sticker"]}},86716:e=>{e.exports={en:["Stockholm"],nl_NL:["Stockholm"]}},1145:e=>{e.exports={en:["Submicro"],nl_NL:["Submicro"]}},63375:e=>{e.exports={en:["Submillennium"],nl_NL:["Submillennium"]}},30585:e=>{e.exports={en:["Subminuette"],nl_NL:["Subminuten"]}},67948:e=>{e.exports={en:["Supercycle"],nl_NL:["Supercycle"]}},3348:e=>{e.exports={en:["Supermillennium"],nl_NL:["Supermillennium"]}},18905:e=>{e.exports={en:["Switch to {resolution}"],nl_NL:["Switch to {resolution}"]}},31622:e=>{e.exports={en:["Sydney"],nl_NL:["Sydney"]}},70963:e=>{e.exports={en:["Symbol Error"],nl_NL:["Symbol Error"]}},32390:e=>{e.exports={en:["Symbol name label"],nl_NL:["Symbol Name Label"]}},10127:e=>{e.exports={en:["Symbol last price label"],nl_NL:["Symbol Last Value Label"]}},39079:e=>{e.exports={en:["Sync globally"],nl_NL:["Sync globally"]}},46607:e=>{e.exports={en:["Sync in layout"],nl_NL:["Sync To All Charts"]}},76519:e=>{e.exports={en:["Point & figure"],nl_NL:["Point & Figure"]}},39949:e=>{e.exports={en:["Polyline"],nl_NL:["Polygoon"]}},371:e=>{e.exports={en:["Path"],nl_NL:["Path"]}},59256:e=>{e.exports={en:["Parallel Channel"],nl_NL:["Parallel kanaal"]}},61879:e=>{e.exports={en:["Paris"],nl_NL:["Parijs"]}},35140:e=>{e.exports={en:["Paste"],nl_NL:["Paste"]}},6919:e=>{e.exports={en:["Percent"],nl_NL:["Percent"]}},24436:e=>{e.exports={en:["Perth"],nl_NL:["Perth"]}},14055:e=>{e.exports={en:["Phoenix"],nl_NL:["Phoenix"]}},34156:e=>{e.exports={en:["Pitchfan"],nl_NL:["Pitch waaier"]}},19634:e=>{e.exports={en:["Pitchfork"],nl_NL:["Hooivork"]}},33110:e=>{e.exports={en:["Pin to new left scale"],nl_NL:["Pin to new left scale"]}},28280:e=>{e.exports={en:["Pin to new right scale"],nl_NL:["Pin to new right scale"]}},14115:e=>{e.exports={en:["Pin to left scale"],nl_NL:["Pin to left scale"]}},72046:e=>{e.exports={
en:["Pin to left scale (hidden)"],nl_NL:["Pin to left scale (hidden)"]}},81054:e=>{e.exports={en:["Pin to right scale"],nl_NL:["Pin to right scale"]}},16986:e=>{e.exports={en:["Pin to right scale (hidden)"],nl_NL:["Pin to right scale (hidden)"]}},60035:e=>{e.exports={en:["Pin to scale (now left)"],nl_NL:["Pin to scale (now left)"]}},94210:e=>{e.exports={en:["Pin to scale (now no scale)"],nl_NL:["Pin to scale (now no scale)"]}},10761:e=>{e.exports={en:["Pin to scale (now right)"],nl_NL:["Pin to scale (now right)"]}},76150:e=>{e.exports={en:["Pin to scale (now {label})"],nl_NL:["Pin to scale (now {label})"]}},29436:e=>{e.exports={en:["Pin to scale {label}"],nl_NL:["Pin to scale {label}"]}},2165:e=>{e.exports={en:["Pin to scale {label} (hidden)"],nl_NL:["Pin to scale {label} (hidden)"]}},90095:e=>{e.exports={en:["Pinned to left scale"],nl_NL:["Pinned to left scale"]}},32538:e=>{e.exports={en:["Pinned to left scale (hidden)"],nl_NL:["Pinned to left scale (hidden)"]}},44579:e=>{e.exports={en:["Pinned to right scale"],nl_NL:["Pinned to right scale"]}},94559:e=>{e.exports={en:["Pinned to right scale (hidden)"],nl_NL:["Pinned to right scale (hidden)"]}},12645:e=>{e.exports={en:["Pinned to scale {label}"],nl_NL:["Pinned to scale {label}"]}},3564:e=>{e.exports={en:["Pinned to scale {label} (hidden)"],nl_NL:["Pinned to scale {label} (hidden)"]}},71566:e=>{e.exports={en:["Plus button"],nl_NL:["Plus button"]}},28298:e=>{e.exports={en:["Please give us a clipboard writing permission in your browser or press {keystroke}"],nl_NL:["Please give us a clipboard writing permission in your browser or press {keystroke}"]}},81248:e=>{e.exports={en:["Prague"],nl_NL:["Prague"]}},81712:e=>{e.exports={en:["Press and hold {key} while zooming to maintain the chart position"],nl_NL:["Press and hold {key} while zooming to maintain the chart position"]}},91282:e=>{e.exports={en:["Price Label"],nl_NL:["Prijs label"]}},97512:e=>{e.exports={en:["Price Note"],nl_NL:["Price Note"]}},68941:e=>{e.exports={en:["Price Range"],nl_NL:["Prijs gebied"]}},66123:e=>{e.exports={en:["Price format is invalid."],nl_NL:["Price format is invalid."]}},72926:e=>{e.exports={en:["Price line"],nl_NL:["Prijs lijn"]}},59189:e=>{e.exports={en:["Primary"],nl_NL:["Primair"]}},75747:e=>{e.exports={en:["Projection"],nl_NL:["Projectie"]}},55801:e=>{e.exports={en:["Published on {customer}, {date}"],nl_NL:["Published on {customer}, {date}"]}},28756:e=>{e.exports={en:["Qatar"],nl_NL:["Qatar"]}},57959:e=>{e.exports={en:["Quick search. Press {shortcut}"],nl_NL:["Quick search. Press {shortcut}"]}},56820:e=>{e.exports={en:["Rotated Rectangle"],nl_NL:["Gedraaide rechthoek"]}},52961:e=>{e.exports={en:["Rome"],nl_NL:["Rome"]}},50318:e=>{e.exports={en:["Ray"],nl_NL:["Straal"]}},55169:e=>{e.exports={en:["Range"],nl_NL:["Range"]}},13386:e=>{e.exports={en:["Reykjavik"],nl_NL:["Reykjavik"]}},26001:e=>{e.exports={en:["Rectangle"],nl_NL:["Vierkant"]}},48236:e=>{e.exports={en:["Redo"],nl_NL:["Opnieuw"]}},2460:e=>{e.exports={en:["Regression Trend"],nl_NL:["Regressie trend"]}},67410:e=>{e.exports={
en:["Remove"],nl_NL:["Verwijder"]}},96374:e=>{e.exports={en:["Remove drawings"],nl_NL:["Remove drawings"]}},99984:e=>{e.exports={en:["Remove indicators"],nl_NL:["Remove Indicators"]}},3061:e=>{e.exports={en:["Remove this financial metric from favorites"],nl_NL:["Remove this financial metric from favorites"]}},58764:e=>{e.exports={en:["Remove this indicator from favorites"],nl_NL:["Remove this indicator from favorites"]}},22584:e=>{e.exports={en:["Rename Chart Layout"],nl_NL:["Hernoem grafiek lay-out"]}},88130:e=>{e.exports={en:["Renko"],nl_NL:["Renko"]}},75246:e=>{e.exports={en:["Reset chart view"],nl_NL:["Reset chart view"]}},88853:e=>{e.exports={en:["Reset points"],nl_NL:["Reset points"]}},15332:e=>{e.exports={en:["Reset price scale"],nl_NL:["Reset Price Scale"]}},54170:e=>{e.exports={en:["Reset time scale"],nl_NL:["Reset Time Scale"]}},37974:e=>{e.exports={en:["Riyadh"],nl_NL:["Riyadh"]}},94022:e=>{e.exports={en:["Riga"],nl_NL:["Riga"]}},60630:e=>{e.exports={en:["Runtime error"],nl_NL:["Runtime error"]}},66719:e=>{e.exports={en:["Warning"],nl_NL:["Warning"]}},5959:e=>{e.exports={en:["Warsaw"],nl_NL:["Warschau"]}},94465:e=>{e.exports={en:["Toggle auto scale"],nl_NL:["Toggle auto scale"]}},46992:e=>{e.exports={en:["Toggle log scale"],nl_NL:["Toggle log scale"]}},98549:e=>{e.exports={en:["Tokelau"],nl_NL:["Tokelau"]}},69122:e=>{e.exports={en:["Tokyo"],nl_NL:["Tokio"]}},10095:e=>{e.exports={en:["Toronto"],nl_NL:["Toronto"]}},11034:e=>{e.exports={en:["Taipei"],nl_NL:["Taipei"]}},79995:e=>{e.exports={en:["Tallinn"],nl_NL:["Tallinn"]}},6686:e=>{e.exports={en:["Tehran"],nl_NL:["Teheran"]}},93553:e=>{e.exports={en:["Template"],nl_NL:["Template"]}},81657:e=>{e.exports={en:["The data vendor doesn't provide volume data for this symbol."],nl_NL:["The data vendor doesn't provide volume data for this symbol."]}},49947:e=>{e.exports={en:["The publication preview could not be loaded. Please disable your browser extensions and try again."],nl_NL:["The publication preview could not be loaded. Please disable your browser extensions and try again."]}},59519:e=>{e.exports={en:["This indicator cannot be applied to another indicator."],nl_NL:["This indicator cannot be applied to another indicator."]}},18260:e=>{e.exports={en:["This script contains an error. Please contact its author."],nl_NL:["This script contains an error. Please contact its author."]}},76989:e=>{e.exports={en:["This script is invite-only. To request access, please contact its author."],nl_NL:["This script is invite-only. To request access, please contact its author."]}},47773:e=>{e.exports={en:["This symbol is only available on {linkStart}TradingView{linkEnd}."],nl_NL:["The symbol available only on {linkStart}TradingView{linkEnd}."]}},46982:e=>{e.exports={en:["Three Drives Pattern"],nl_NL:["Three Drives Pattern"]}},24821:e=>{e.exports={en:["Ticks"],nl_NL:["Ticks"]}},80254:e=>{e.exports={en:["Tick-based intervals are not available for {ticker}."],nl_NL:["Tick-based intervals are not available for {ticker}."]}},12806:e=>{e.exports={en:["Time"],nl_NL:["Tijd"]}},20909:e=>{
e.exports={en:["Time zone"],nl_NL:["Tijdszone"]}},46852:e=>{e.exports={en:["Time Cycles"],nl_NL:["Time Cycles"]}},17809:e=>{e.exports={en:["Time Price Opportunity"],nl_NL:["Time Price Opportunity"]}},66823:e=>{e.exports={en:["Trade"],nl_NL:["Trade"]}},7697:e=>{e.exports={en:["TradingView is interactive and has commands to use with a screen reader. The following is a list of keyboard commands available to interact on the platform"],nl_NL:["TradingView is interactive and has commands to use with a screen reader. The following is a list of keyboard commands available to interact on the platform"]}},35757:e=>{e.exports={en:["Trend Angle"],nl_NL:["Trend hoek"]}},97339:e=>{e.exports={en:["Trend Line"],nl_NL:["Trendlijn"]}},80583:e=>{e.exports={en:["Trend-Based Fib Extension"],nl_NL:["Trend gebaseerde Fib extensie"]}},72159:e=>{e.exports={en:["Trend-Based Fib Time"],nl_NL:["Trend gebaseerde Fib tijd"]}},1671:e=>{e.exports={en:["Triangle"],nl_NL:["Driehoek"]}},76152:e=>{e.exports={en:["Triangle Down"],nl_NL:["Triangle Down"]}},90148:e=>{e.exports={en:["Triangle Pattern"],nl_NL:["Driehoek patroon"]}},21236:e=>{e.exports={en:["Triangle Up"],nl_NL:["Triangle Up"]}},21007:e=>{e.exports={en:["Tunis"],nl_NL:["Tunis"]}},1833:e=>{e.exports={en:["UTC"],nl_NL:["UTC+0"]}},14804:e=>{e.exports={en:["Undo"],nl_NL:["Ongedaan maken"]}},15432:e=>{e.exports={en:["Units"],nl_NL:["Units"]}},11768:e=>{e.exports={en:["Unknown error"],nl_NL:["Unknown error"]}},99894:e=>{e.exports={en:["Unlock"],nl_NL:["Unlock"]}},75546:e=>{e.exports={en:["Unsupported interval"],nl_NL:["Unsupported interval"]}},8580:e=>{e.exports={en:["User-defined error"],nl_NL:["User-defined error"]}},40693:e=>{e.exports={en:["Volume Profile Fixed Range"],nl_NL:["Volume Profile Fixed Range"]}},39903:e=>{e.exports={en:["Volume Profile indicator available only on our upgraded plans."],nl_NL:["Volume Profile indicator available only on our upgraded plans."]}},93722:e=>{e.exports={en:["Volume candles"],nl_NL:["Volume candles"]}},69156:e=>{e.exports={en:["Volume data is not provided in BIST MIXED data plan."],nl_NL:["Volume data is not provided in BIST MIXED data plan."]}},92763:e=>{e.exports={en:["Volume footprint"],nl_NL:["Volume footprint"]}},32838:e=>{e.exports={en:["Vancouver"],nl_NL:["Vancouver"]}},29535:e=>{e.exports={en:["Vertical Line"],nl_NL:["Verticale Lijn"]}},23160:e=>{e.exports={en:["Vienna"],nl_NL:["Vienna"]}},60534:e=>{e.exports={en:["Vilnius"],nl_NL:["Vilnius"]}},40091:e=>{e.exports={en:["Visibility"],nl_NL:["Visibility"]}},54853:e=>{e.exports={en:["Visibility on intervals"],nl_NL:["Visibility on intervals"]}},10309:e=>{e.exports={en:["Visible on mouse over"],nl_NL:["Visible on Mouse Over"]}},4077:e=>{e.exports={en:["Visual order"],nl_NL:["Visuele volgorde"]}},11316:e=>{e.exports={en:["X Cross"],nl_NL:["X Cross"]}},42231:e=>{e.exports={en:["XABCD Pattern"],nl_NL:["XABC patroon"]}},25059:e=>{e.exports={en:["You cannot see this pivot timeframe on this resolution"],nl_NL:["You cannot see this pivot timeframe on this resolution"]}},53168:e=>{e.exports={en:["Yangon"],
nl_NL:["Yangon"]}},62859:e=>{e.exports={en:["Zurich"],nl_NL:["Zurich"]}},47977:e=>{e.exports={en:["change Elliott degree"],nl_NL:["change Elliott degree"]}},61557:e=>{e.exports={en:["change no overlapping labels"],nl_NL:["change no overlapping labels"]}},76852:e=>{e.exports={en:["change average close price label visibility"],nl_NL:["change average close price label visibility"]}},1022:e=>{e.exports={en:["change average close price line visibility"],nl_NL:["change average close price line visibility"]}},69362:e=>{e.exports={en:["change bid and ask labels visibility"],nl_NL:["change bid and ask labels visibility"]}},52919:e=>{e.exports={en:["change bid and ask lines visibility"],nl_NL:["change bid and ask lines visibility"]}},32302:e=>{e.exports={en:["change currency"],nl_NL:["change currency"]}},68846:e=>{e.exports={en:["change chart layout to {title}"],nl_NL:["change chart layout to {title}"]}},18867:e=>{e.exports={en:["change continuous contract switch visibility"],nl_NL:["change continuous contract switch visibility"]}},39383:e=>{e.exports={en:["change countdown to bar close visibility"],nl_NL:["change countdown to bar close visibility"]}},16979:e=>{e.exports={en:["change date range"],nl_NL:["change date range"]}},53929:e=>{e.exports={en:["change dividends visibility"],nl_NL:["change dividends visibility"]}},6119:e=>{e.exports={en:["change events visibility on chart"],nl_NL:["change events visibility on chart"]}},6819:e=>{e.exports={en:["change earnings visibility"],nl_NL:["change earnings visibility"]}},85532:e=>{e.exports={en:["change futures contract expiration visibility"],nl_NL:["change futures contract expiration visibility"]}},24226:e=>{e.exports={en:["change high and low price labels visibility"],nl_NL:["change high and low price labels visibility"]}},80692:e=>{e.exports={en:["change high and low price lines visibility"],nl_NL:["change high and low price lines visibility"]}},24893:e=>{e.exports={en:["change indicators name labels visibility"],nl_NL:["change indicators name labels visibility"]}},64729:e=>{e.exports={en:["change indicators value labels visibility"],nl_NL:["change indicators value labels visibility"]}},50243:e=>{e.exports={en:["change latest news and Minds visibility"],nl_NL:["change latest news and Minds visibility"]}},88849:e=>{e.exports={en:["change linking group"],nl_NL:["change linking group"]}},14691:e=>{e.exports={en:["change pane height"],nl_NL:["change pane height"]}},96379:e=>{e.exports={en:["change plus button visibility"],nl_NL:["change plus button visibility"]}},30870:e=>{e.exports={en:["change pre/post market price label visibility"],nl_NL:["change pre/post market price label visibility"]}},11718:e=>{e.exports={en:["change pre/post market price line visibility"],nl_NL:["change pre/post market price line visibility"]}},58419:e=>{e.exports={en:["change previous close price line visibility"],nl_NL:["change previous close price line visibility"]}},8662:e=>{e.exports={en:["change price line visibility"],nl_NL:["change price line visibility"]}},2509:e=>{e.exports={
en:["change price to bar ratio"],nl_NL:["change price to bar ratio"]}},32829:e=>{e.exports={en:["change resolution"],nl_NL:["change resolution"]}},35400:e=>{e.exports={en:["change symbol"],nl_NL:["Change symbol"]}},73357:e=>{e.exports={en:["change symbol labels visibility"],nl_NL:["change symbol labels visibility"]}},67453:e=>{e.exports={en:["change symbol last value visibility"],nl_NL:["change symbol last value visibility"]}},4729:e=>{e.exports={en:["change symbol previous close value visibility"],nl_NL:["change symbol previous close value visibility"]}},87041:e=>{e.exports={en:["change session"],nl_NL:["change session"]}},38413:e=>{e.exports={en:["change session breaks visibility"],nl_NL:["change session breaks visibility"]}},49965:e=>{e.exports={en:["change series style"],nl_NL:["change series style"]}},47474:e=>{e.exports={en:["change splits visibility"],nl_NL:["change splits visibility"]}},20137:e=>{e.exports={en:["change timezone"],nl_NL:["change timezone"]}},85975:e=>{e.exports={en:["change unit"],nl_NL:["change unit"]}},1924:e=>{e.exports={en:["change visibility"],nl_NL:["change visibility"]}},84331:e=>{e.exports={en:["change visibility at current interval"],nl_NL:["change visibility at current interval"]}},45800:e=>{e.exports={en:["change visibility at current interval and above"],nl_NL:["change visibility at current interval and above"]}},75645:e=>{e.exports={en:["change visibility at current interval and below"],nl_NL:["change visibility at current interval and below"]}},57916:e=>{e.exports={en:["change visibility at all intervals"],nl_NL:["change visibility at all intervals"]}},98463:e=>{e.exports={en:["change {title} style"],nl_NL:["change {title} style"]}},57122:e=>{e.exports={en:["change {title} text"],nl_NL:["change {title} text"]}},63058:e=>{e.exports={en:["change {pointIndex} point"],nl_NL:["change {pointIndex} point"]}},94566:e=>{e.exports={en:["charts by TradingView"],nl_NL:["charts by TradingView"]}},32943:e=>{e.exports={en:["clone line tools"],nl_NL:["clone line tools"]}},46219:e=>{e.exports={en:["create line tools group"],nl_NL:["create line tools group"]}},95394:e=>{e.exports={en:["create line tools group from selection"],nl_NL:["create line tools group from selection"]}},12898:e=>{e.exports={en:["create {tool}"],nl_NL:["create {tool}"]}},94227:e=>{e.exports={en:["cut sources"],nl_NL:["cut sources"]}},11500:e=>{e.exports={en:["cut {title}"],nl_NL:["cut {title}"]}},12570:e=>{e.exports={en:["add line tool {lineTool} to group {name}"],nl_NL:["add line tool {lineTool} to group {name}"]}},21162:e=>{e.exports={en:["add line tool(s) to group {group}"],nl_NL:["add line tool(s) to group {group}"]}},67608:e=>{e.exports={en:["add this financial metric to entire layout"],nl_NL:["add this financial metric to entire layout"]}},96677:e=>{e.exports={en:["add this indicator to entire layout"],nl_NL:["add this indicator to entire layout"]}},58156:e=>{e.exports={en:["add this strategy to entire layout"],nl_NL:["add this strategy to entire layout"]}},79290:e=>{e.exports={en:["add this symbol to entire layout"],
nl_NL:["add this symbol to entire layout"]}},68231:e=>{e.exports={en:["apply chart theme"],nl_NL:["apply chart theme"]}},99551:e=>{e.exports={en:["apply all chart properties"],nl_NL:["apply all chart properties"]}},89720:e=>{e.exports={en:["apply drawing template"],nl_NL:["apply drawing template"]}},27851:e=>{e.exports={en:["apply factory defaults to selected sources"],nl_NL:["apply factory defaults to selected sources"]}},70507:e=>{e.exports={en:["apply indicators to entire layout"],nl_NL:["apply indicators to entire layout"]}},69604:e=>{e.exports={en:["apply study template {template}"],nl_NL:["apply study template {template}"]}},86708:e=>{e.exports={en:["apply toolbars theme"],nl_NL:["apply toolbars theme"]}},1979:e=>{e.exports={en:["bring group {title} forward"],nl_NL:["bring group {title} forward"]}},53159:e=>{e.exports={en:["bring {title} to front"],nl_NL:["bring {title} to front"]}},41966:e=>{e.exports={en:["bring {title} forward"],nl_NL:["bring {title} forward"]}},44676:e=>{e.exports={en:["by TradingView"],nl_NL:["by TradingView"]}},58850:e=>{e.exports={en:["date range lock"],nl_NL:["date range lock"]}},35111:e=>{e.exports={en:["erase level line"],nl_NL:["erase level line"]}},99395:e=>{e.exports={en:["exclude line tools from group {group}"],nl_NL:["exclude line tools from group {group}"]}},82349:e=>{e.exports={en:["flip bars pattern"],nl_NL:["flip bars pattern"]}},13017:e=>{e.exports={en:["hide {title}"],nl_NL:["hide {title}"]}},62249:e=>{e.exports={en:["hide marks on bars"],nl_NL:["Verberg markeringen op bars"]}},56558:e=>{e.exports={en:["interval lock"],nl_NL:["interval lock"]}},6830:e=>{e.exports={en:["invert scale"],nl_NL:["invert scale"]}},48818:e=>{e.exports={en:["insert {title}"],nl_NL:["insert {title}"]}},56307:e=>{e.exports={en:["insert {title} after {targetTitle}"],nl_NL:["insert {title} after {targetTitle}"]}},32960:e=>{e.exports={en:["insert {title} after {target}"],nl_NL:["insert {title} after {target}"]}},57106:e=>{e.exports={en:["insert {title} before {target}"],nl_NL:["insert {title} before {target}"]}},46229:e=>{e.exports={en:["insert {title} before {targetTitle}"],nl_NL:["insert {title} before {targetTitle}"]}},43364:e=>{e.exports={en:["load default drawing template"],nl_NL:["load default drawing template"]}},62011:e=>{e.exports={en:["loading..."],nl_NL:["laden..."]}},76104:e=>{e.exports={en:["lock {title}"],nl_NL:["lock {title}"]}},20453:e=>{e.exports={en:["lock group {group}"],nl_NL:["lock group {group}"]}},18942:e=>{e.exports={en:["lock objects"],nl_NL:["lock objects"]}},98277:e=>{e.exports={en:["move"],nl_NL:["move"]}},58228:e=>{e.exports={en:["move {title} to new left scale"],nl_NL:["move {title} to new left scale"]}},77482:e=>{e.exports={en:["move {title} to new right scale"],nl_NL:["move {title} to new right scale"]}},64077:e=>{e.exports={en:["move all scales to left"],nl_NL:["move all scales to left"]}},19013:e=>{e.exports={en:["move all scales to right"],nl_NL:["move all scales to right"]}},52510:e=>{e.exports={en:["move drawing(s)"],nl_NL:["move drawing(s)"]}},79209:e=>{e.exports={
en:["move left"],nl_NL:["move left"]}},60114:e=>{e.exports={en:["move right"],nl_NL:["move right"]}},44854:e=>{e.exports={en:["move scale"],nl_NL:["move scale"]}},10625:e=>{e.exports={en:["make {title} no scale (Full screen)"],nl_NL:["make {title} no scale (Full screen)"]}},76709:e=>{e.exports={en:["make group {group} invisible"],nl_NL:["make group {group} invisible"]}},45987:e=>{e.exports={en:["make group {group} visible"],nl_NL:["make group {group} visible"]}},78055:e=>{e.exports={en:["merge down"],nl_NL:["merge down"]}},41866:e=>{e.exports={en:["merge to pane"],nl_NL:["merge to pane"]}},52458:e=>{e.exports={en:["merge up"],nl_NL:["merge up"]}},20965:e=>{e.exports={en:["mirror bars pattern"],nl_NL:["mirror bars pattern"]}},90091:e=>{e.exports={en:["n/a"],nl_NL:["n/a"]}},94981:e=>{e.exports={en:["scale price"],nl_NL:["scale price"]}},63796:e=>{e.exports={en:["scale price chart only"],nl_NL:["Schaal alleen prijsgrafiek"]}},70771:e=>{e.exports={en:["scale time"],nl_NL:["scale time"]}},42070:e=>{e.exports={en:["scroll"],nl_NL:["scroll"]}},87840:e=>{e.exports={en:["scroll time"],nl_NL:["scroll time"]}},82241:e=>{e.exports={en:["set price scale selection strategy to {title}"],nl_NL:["set price scale selection strategy to {title}"]}},40962:e=>{e.exports={en:["send {title} backward"],nl_NL:["send {title} backward"]}},5005:e=>{e.exports={en:["send {title} to back"],nl_NL:["send {title} to back"]}},69546:e=>{e.exports={en:["send group {title} backward"],nl_NL:["send group {title} backward"]}},63934:e=>{e.exports={en:["share line tools globally"],nl_NL:["share line tools globally"]}},90221:e=>{e.exports={en:["share line tools in layout"],nl_NL:["share line tools in layout"]}},13336:e=>{e.exports={en:["show all ideas"],nl_NL:["show all ideas"]}},91395:e=>{e.exports={en:["show ideas of followed users"],nl_NL:["show ideas of followed users"]}},57460:e=>{e.exports={en:["show my ideas only"],nl_NL:["show my ideas only"]}},4114:e=>{e.exports={en:["stay in drawing mode"],nl_NL:["stay in drawing mode"]}},3350:e=>{e.exports={en:["stop syncing drawing"],nl_NL:["stop syncing drawing"]}},49183:e=>{e.exports={en:["stop syncing line tool(s)"],nl_NL:["stop syncing line tool(s)"]}},53278:e=>{e.exports={en:["symbol lock"],nl_NL:["symbol lock"]}},91677:e=>{e.exports={en:["sync time"],nl_NL:["sync time"]}},3140:e=>{e.exports={en:["powered by"],nl_NL:["mogelijke gemaakt door"]}},92800:e=>{e.exports={en:["powered by TradingView"],nl_NL:["powered by TradingView"]}},62192:e=>{e.exports={en:["paste drawing"],nl_NL:["paste drawing"]}},1064:e=>{e.exports={en:["paste indicator"],nl_NL:["paste indicator"]}},57010:e=>{e.exports={en:["paste {title}"],nl_NL:["paste {title}"]}},78690:e=>{e.exports={en:["pin to left scale"],nl_NL:["pin to left scale"]}},7495:e=>{e.exports={en:["pin to right scale"],nl_NL:["pin to right scale"]}},81566:e=>{e.exports={en:["pin to scale {label}"],nl_NL:["pin to scale {label}"]}},2618:e=>{e.exports={en:["rearrange panes"],nl_NL:["rearrange panes"]}},43172:e=>{e.exports={en:["remove all studies"],nl_NL:["remove all studies"]}},
56253:e=>{e.exports={en:["remove all studies and drawing tools"],nl_NL:["remove all studies and drawing tools"]}},27171:e=>{e.exports={en:["remove deselected empty line tools"],nl_NL:["remove deselected empty line tools"]}},30538:e=>{e.exports={en:["remove drawings"],nl_NL:["remove drawings"]}},1193:e=>{e.exports={en:["remove drawings group"],nl_NL:["remove drawings group"]}},38199:e=>{e.exports={en:["remove line data sources"],nl_NL:["remove line data sources"]}},93333:e=>{e.exports={en:["remove pane"],nl_NL:["remove pane"]}},94543:e=>{e.exports={en:["remove {title}"],nl_NL:["remove {title}"]}},41430:e=>{e.exports={en:["removing line tools group {name}"],nl_NL:["removing line tools group {name}"]}},80491:e=>{e.exports={en:["rename group {group} to {newName}"],nl_NL:["rename group {group} to {newName}"]}},85366:e=>{e.exports={en:["reset layout sizes"],nl_NL:["reset layout sizes"]}},3323:e=>{e.exports={en:["reset scales"],nl_NL:["reset scales"]}},17336:e=>{e.exports={en:["reset time scale"],nl_NL:["Reset Time Scale"]}},47418:e=>{e.exports={en:["resize layout"],nl_NL:["resize layout"]}},85815:e=>{e.exports={en:["restore defaults"],nl_NL:["restore defaults"]}},96881:e=>{e.exports={en:["restore study defaults"],nl_NL:["restore study defaults"]}},42240:e=>{e.exports={en:["toggle auto scale"],nl_NL:["toggle auto scale"]}},46054:e=>{e.exports={en:["toggle collapsed pane state"],nl_NL:["toggle collapsed pane state"]}},24736:e=>{e.exports={en:["toggle indexed to 100 scale"],nl_NL:["toggle indexed to 100 scale"]}},49695:e=>{e.exports={en:["toggle lock scale"],nl_NL:["toggle lock scale"]}},49403:e=>{e.exports={en:["toggle log scale"],nl_NL:["toggle log scale"]}},98994:e=>{e.exports={en:["toggle percentage scale"],nl_NL:["Toggle Percentage Scale"]}},80688:e=>{e.exports={en:["toggle regular scale"],nl_NL:["toggle regular scale"]}},46807:e=>{e.exports={en:["track time"],nl_NL:["track time"]}},8040:e=>{e.exports={en:["turn line tools sharing off"],nl_NL:["turn line tools sharing off"]}},23230:e=>{e.exports={en:["unlock objects"],nl_NL:["unlock objects"]}},74590:e=>{e.exports={en:["unlock group {group}"],nl_NL:["unlock group {group}"]}},12525:e=>{e.exports={en:["unlock {title}"],nl_NL:["unlock {title}"]}},81576:e=>{e.exports={en:["unmerge to new bottom pane"],nl_NL:["unmerge to new bottom pane"]}},79443:e=>{e.exports={en:["unmerge up"],nl_NL:["unmerge up"]}},46453:e=>{e.exports={en:["unmerge down"],nl_NL:["unmerge down"]}},94656:e=>{e.exports={en:["{chartStyle} chart type isn't currently available for tick-based intervals."],nl_NL:["{chartStyle} chart type isn't currently available for tick-based intervals."]}},41643:e=>{e.exports={en:["{count} bars"],nl_NL:["{count} bars"]}},83470:e=>{e.exports={en:["{symbol} financials by TradingView"],nl_NL:["{symbol} financials by TradingView"]}},40947:e=>{e.exports={en:["{userName} published on {customer}, {date}"],nl_NL:["{userName} published on {customer}, {date}"]}},91084:e=>{e.exports={en:["zoom"],nl_NL:["zoom"]}},49856:e=>{e.exports={en:["zoom in"],nl_NL:["zoom in"]}},73638:e=>{e.exports={
en:["zoom out"],nl_NL:["zoom out"]}},41807:e=>{e.exports={en:["day","days"],nl_NL:["day","days"]}},42328:e=>{e.exports={en:["hour","hours"],nl_NL:["hour","hours"]}},98393:e=>{e.exports={en:["month","months"],nl_NL:["month","months"]}},78318:e=>{e.exports={en:["minute","minutes"],nl_NL:["minute","minutes"]}},33232:e=>{e.exports={en:["second","seconds"],nl_NL:["second","seconds"]}},89937:e=>{e.exports={en:["range","ranges"],nl_NL:["range","ranges"]}},48898:e=>{e.exports={en:["week","weeks"],nl_NL:["week","weeks"]}},11913:e=>{e.exports={en:["tick","ticks"],nl_NL:["tick","ticks"]}},58590:e=>{e.exports={en:["{count}m","{count}m"],nl_NL:["{count}m","{count}m"]}},47801:e=>{e.exports={en:["{count}d","{count}d"],nl_NL:["{count}d","{count}d"]}},46766:e=>{e.exports={en:["{count}y","{count}y"],nl_NL:["{count}y","{count}y"]}},56316:e=>{e.exports=Object.create(null),e.exports["#AAPL-symbol-description"]={en:["Apple Inc"],nl_NL:["APPLE INC"]},e.exports["#AUDCAD-symbol-description"]={en:["Australian Dollar/Canadian Dollar"],nl_NL:["Australian Dollar/Canadian Dollar"]},e.exports["#AUDCHF-symbol-description"]={en:["Australian Dollar / Swiss Franc"],nl_NL:["Australian Dollar/Swiss Franc"]},e.exports["#AUDJPY-symbol-description"]={en:["Australian Dollar / Japanese Yen"],nl_NL:["Australian Dollar/Japanese Yen"]},e.exports["#AUDNZD-symbol-description"]={en:["Australian Dollar / New Zealand Dollar"],nl_NL:["Australian Dollar/New Zealand Dollar"]},e.exports["#AUDRUB-symbol-description"]={en:["Australian Dollar / Russian Ruble"],nl_NL:["AUSTRALIAN DOLLAR / RUSSIAN RUBLE"]},e.exports["#AUDUSD-symbol-description"]={en:["Australian Dollar / U.S. Dollar"],nl_NL:["Australian Dollar/U.S. Dollar"]},e.exports["#BRLJPY-symbol-description"]={en:["Brazilian Real / Japanese Yen"],nl_NL:["Brazilian Real / Japanese Yen"]},e.exports["#BTCCAD-symbol-description"]={en:["Bitcoin / Canadian Dollar"],nl_NL:["Bitcoin / Canadian Dollar"]},e.exports["#BTCCNY-symbol-description"]={en:["Bitcoin / Chinese Yuan"],nl_NL:["Bitcoin / Chinese Yuan"]},e.exports["#BTCEUR-symbol-description"]={en:["Bitcoin / Euro"],nl_NL:["Bitcoin / Euro"]},e.exports["#BTCKRW-symbol-description"]={en:["Bitcoin / South Korean Won"],nl_NL:["Bitcoin / South Korean Won"]},e.exports["#BTCRUR-symbol-description"]={en:["Bitcoin / Ruble"],nl_NL:["Bitcoin / Ruble"]},e.exports["#BTCUSD-symbol-description"]={en:["Bitcoin / U.S. Dollar"],nl_NL:["Bitcoin / Dollar"]},e.exports["#BVSP-symbol-description"]={en:["Brazil Bovespa Index"],nl_NL:["Brazil Bovespa Index"]},e.exports["#CADJPY-symbol-description"]={en:["Canadian Dollar / Japanese Yen"],nl_NL:["Canadian Dollar/Japanese Yen"]},e.exports["#CHFJPY-symbol-description"]={en:["Swiss Franc / Japanese Yen"],nl_NL:["Swiss Franc/Japanese Yen"]},e.exports["#COPPER-symbol-description"]={en:["CFDs on Copper"],nl_NL:["Copper"]},e.exports["#ES1-symbol-description"]={en:["S&P 500 E-Mini Futures"],nl_NL:["S&P 500 E-Mini Futures"]},e.exports["#ESP35-symbol-description"]={en:["IBEX 35 Index"],nl_NL:["IBEX 35 Index"]},e.exports["#EUBUND-symbol-description"]={
en:["Euro Bund"],nl_NL:["Euro Bund"]},e.exports["#EURAUD-symbol-description"]={en:["Euro / Australian Dollar"],nl_NL:["Euro Fx/Australian Dollar"]},e.exports["#EURBRL-symbol-description"]={en:["Euro / Brazilian Real"],nl_NL:["Euro / Brazilian Real"]},e.exports["#EURCAD-symbol-description"]={en:["Euro / Canadian Dollar"],nl_NL:["Euro Fx/Canadian Dollar"]},e.exports["#EURCHF-symbol-description"]={en:["Euro / Swiss Franc"],nl_NL:["Euro Fx/Swiss Franc"]},e.exports["#EURGBP-symbol-description"]={en:["Euro / British Pound"],nl_NL:["Euro Fx/British Pound"]},e.exports["#EURJPY-symbol-description"]={en:["Euro / Japanese Yen"],nl_NL:["Euro Fx/Japanese Yen"]},e.exports["#EURNZD-symbol-description"]={en:["Euro / New Zealand Dollar"],nl_NL:["Euro Fx/New Zealand Dollar"]},e.exports["#EURRUB-symbol-description"]={en:["Euro / Russian Ruble"],nl_NL:["EURO / RUSSIAN RUBLE"]},e.exports["#EURRUB_TOM-symbol-description"]={en:["Euro / Russian Ruble TOM"],nl_NL:["EUR/RUB TOM"]},e.exports["#EURSEK-symbol-description"]={en:["Euro / Swedish Krona"],nl_NL:["Euro / Swedish Krona"]},e.exports["#EURTRY-symbol-description"]={en:["Euro / Turkish Lira"],nl_NL:["Euro Fx/Turkish New Lira"]},e.exports["#EURUSD-symbol-description"]={en:["Euro / U.S. Dollar"],nl_NL:["Euro Fx/U.S. Dollar"]},e.exports["#EUSTX50-symbol-description"]={en:["Euro Stoxx 50 Index"],nl_NL:["Euro Stoxx 50 index of European listed shares"]},e.exports["#FRA40-symbol-description"]={en:["CAC 40 Index"],nl_NL:["CAC 40 Index"]},e.exports["#GB10-symbol-description"]={en:["UK Government Bonds 10 yr"],nl_NL:["UK Government Bonds 10 yr"]},e.exports["#GBPAUD-symbol-description"]={en:["British Pound / Australian Dollar"],nl_NL:["British Pound/Australian Dollar"]},e.exports["#GBPCAD-symbol-description"]={en:["British Pound / Canadian Dollar"],nl_NL:["British Pound/Canadian Dollar"]},e.exports["#GBPCHF-symbol-description"]={en:["British Pound / Swiss Franc"],nl_NL:["British Pound/Swiss Franc"]},e.exports["#GBPEUR-symbol-description"]={en:["British Pound / Euro"],nl_NL:["POUND STERLING / EURO"]},e.exports["#GBPJPY-symbol-description"]={en:["British Pound / Japanese Yen"],nl_NL:["British Pound/Japanese Yen"]},e.exports["#GBPNZD-symbol-description"]={en:["British Pound / New Zealand Dollar"],nl_NL:["British Pound/New Zealand Dollar"]},e.exports["#GBPRUB-symbol-description"]={en:["British Pound / Russian Ruble"],nl_NL:["Pound Sterling / Russian Ruble"]},e.exports["#GBPUSD-symbol-description"]={en:["British Pound / U.S. Dollar"],nl_NL:["British Pound/U.S. Dollar"]},e.exports["#GER30-symbol-description"]={en:["DAX Index"],nl_NL:["DAX index of German listed shares"]},e.exports["#GOOGL-symbol-description"]={en:["Alphabet Inc (Google) Class A"],nl_NL:["GOOGLE INC"]},e.exports["#ITA40-symbol-description"]={en:["FTSE MIB Index"],nl_NL:["FTSE MIB index of Italian listed shares"]},e.exports["#JPN225-symbol-description"]={en:["Nikkei 225 Index"],nl_NL:["Nikkei 225 Index"]},e.exports["#JPYKRW-symbol-description"]={en:["Japanese Yen / South Korean Won"],nl_NL:["YEN / WON"]},
e.exports["#JPYRUB-symbol-description"]={en:["Japanese Yen / Russian Ruble"],nl_NL:["YEN / RUSSIAN RUBLE"]},e.exports["#KA1-symbol-description"]={en:["Sugar #11 Futures"],nl_NL:["Sugar #11 Futures"]},e.exports["#KG1-symbol-description"]={en:["Cotton Futures"],nl_NL:["Cotton Futures"]},e.exports["#KT1-symbol-description"]={en:["Key Tronic Corр."],nl_NL:["Key Tronic Corр."]},e.exports["#LKOH-symbol-description"]={en:["LUKOIL"],nl_NL:["LUKOIL"]},e.exports["#LTCBTC-symbol-description"]={en:["Litecoin / Bitcoin"],nl_NL:["Litecoin / Bitcoin"]},e.exports["#MGNT-symbol-description"]={en:["Magnit"],nl_NL:["MAGNIT"]},e.exports["#MICEX-symbol-description"]={en:["MICEX Index"],nl_NL:["MICEX INDEX"]},e.exports["#MNOD_ME.EQRP-symbol-description"]={en:["ADR GMK NORILSKIYNIKEL ORD SHS [REPO]"],nl_NL:["ADR GMK NORILSKIYNIKEL ORD SHS [REPO]"]},e.exports["#MSFT-symbol-description"]={en:["Microsoft Corp."],nl_NL:["MICROSOFT CORP"]},e.exports["#NAS100-symbol-description"]={en:["US 100 Cash CFD"],nl_NL:["NASDAQ 100 index of US listed shares"]},e.exports["#NGAS-symbol-description"]={en:["Natural Gas (Henry Hub)"],nl_NL:["Natural Gas (Henry Hub)"]},e.exports["#NKY-symbol-description"]={en:["Nikkei 225 Index"],nl_NL:["Nikkei 225 Index"]},e.exports["#NZDJPY-symbol-description"]={en:["New Zealand Dollar / Japanese Yen"],nl_NL:["New Zealand Dollar/Japanese Yen"]},e.exports["#NZDUSD-symbol-description"]={en:["New Zealand Dollar / U.S. Dollar"],nl_NL:["New Zealand Dollar/U.S. Dollar"]},e.exports["#RB1-symbol-description"]={en:["RBOB Gasoline Futures"],nl_NL:["RBOB Gasoline Futures"]},e.exports["#RTS-symbol-description"]={en:["Russian RTS Index"],nl_NL:["Russian RTS Index"]},e.exports["#SBER-symbol-description"]={en:["SBERBANK"],nl_NL:["SBERBANK"]},e.exports["#SPX500-symbol-description"]={en:["S&P 500 Index"],nl_NL:["S&P 500 index of US listed shares"]},e.exports["#TWTR-symbol-description"]={en:["Twitter Inc"],nl_NL:["TWITTER INC"]},e.exports["#UK100-symbol-description"]={en:["FTSE 100 Index"],nl_NL:["FTSE 100 Index"]},e.exports["#USDBRL-symbol-description"]={en:["U.S. Dollar / Brazilian Real"],nl_NL:["U.S. Dollar / Brazilian Real"]},e.exports["#USDCAD-symbol-description"]={en:["U.S. Dollar / Canadian Dollar"],nl_NL:["U.S. Dollar/Canadian Dollar"]},e.exports["#USDCHF-symbol-description"]={en:["U.S. Dollar / Swiss Franc"],nl_NL:["U.S. Dollar/Swiss Franc"]},e.exports["#USDCNY-symbol-description"]={en:["U.S. Dollar / Chinese Yuan"],nl_NL:["U.S. Dollar / Yuan Renminbi"]},e.exports["#USDDKK-symbol-description"]={en:["U.S. Dollar / Danish Krone"],nl_NL:["US DOLLAR / DANISH KRONE"]},e.exports["#USDHKD-symbol-description"]={en:["U.S. Dollar / Hong Kong Dollar"],nl_NL:["U.S. Dollar/Hong Kong Dollar"]},e.exports["#USDIDR-symbol-description"]={en:["U.S. Dollar / Rupiah"],nl_NL:["U.S. Dollar / Rupiah"]},e.exports["#USDINR-symbol-description"]={en:["U.S. Dollar / Indian Rupee"],nl_NL:["U.S. Dollar / Indian Rupee"]},e.exports["#USDJPY-symbol-description"]={en:["U.S. Dollar / Japanese Yen"],nl_NL:["U.S. Dollar/Japanese Yen"]},
e.exports["#USDKRW-symbol-description"]={en:["U.S. Dollar / South Korean"],nl_NL:["US DOLLAR / WON"]},e.exports["#USDMXN-symbol-description"]={en:["U.S. Dollar / Mexican Peso"],nl_NL:["U.S. Dollar / Mexican Peso"]},e.exports["#USDPHP-symbol-description"]={en:["U.S. Dollar / Philippine peso"],nl_NL:["U.S. Dollar / Philippine peso"]},e.exports["#USDRUB-symbol-description"]={en:["U.S. Dollar / Russian Ruble"],nl_NL:["US DOLLAR / RUSSIAN RUBLE"]},e.exports["#USDRUB_TOM-symbol-description"]={en:["U.S. Dollar / Russian Ruble TOM"],nl_NL:["USD/RUB TOM"]},e.exports["#USDSEK-symbol-description"]={en:["U.S. Dollar / Swedish Krona"],nl_NL:["U.S. Dollar/Swedish Krona"]},e.exports["#USDSGD-symbol-description"]={en:["U.S. Dollar / Singapore Dollar"],nl_NL:["US DOLLAR / SINGAPORE DOLLAR"]},e.exports["#USDTRY-symbol-description"]={en:["U.S. Dollar / Turkish Lira"],nl_NL:["U.S. Dollar/Turkish New Lira"]},e.exports["#VTBR-symbol-description"]={en:["VTB"],nl_NL:["VTB"]},e.exports["#XAGUSD-symbol-description"]={en:["Silver / U.S. Dollar"],nl_NL:["Silver/U.S. Dollar"]},e.exports["#XAUUSD-symbol-description"]={en:["Gold Spot / U.S. Dollar"],nl_NL:["Gold / U.S. Dollar"]},e.exports["#XPDUSD-symbol-description"]={en:["CFDs on Palladium"],nl_NL:["CFDs on Palladium"]},e.exports["#XPTUSD-symbol-description"]={en:["Platinum / U.S. Dollar"],nl_NL:["Platinum/U.S. Dollar"]},e.exports["#ZS1-symbol-description"]={en:["Soybean Futures - ECBT"],nl_NL:["Soybean Futures - ECBT"]},e.exports["#ZW1-symbol-description"]={en:["Wheat Futures - ECBT"],nl_NL:["Wheat Futures - ECBT"]},e.exports["#BTCGBP-symbol-description"]={en:["Bitcoin / British Pound"],nl_NL:["Bitcoin / British Pound"]},e.exports["#MICEXINDEXCF-symbol-description"]={en:["MOEX Russia Index"],nl_NL:["MICEX Index"]},e.exports["#BTCAUD-symbol-description"]={en:["Bitcoin / Australian Dollar"],nl_NL:["Bitcoin / Australian Dollar"]},e.exports["#BTCJPY-symbol-description"]={en:["Bitcoin / Japanese Yen"],nl_NL:["Bitcoin / Japanese Yen"]},e.exports["#BTCBRL-symbol-description"]={en:["Bitcoin / Brazilian Real"],nl_NL:["Bitcoin / Brazilian Real"]},e.exports["#PT10-symbol-description"]={en:["Portugal Government Bonds 10 yr"],nl_NL:["Portugal Government Bonds 10 yr"]},e.exports["#TXSX-symbol-description"]={en:["TSX 60 Index"],nl_NL:["TSX 60 Index"]},e.exports["#VIXC-symbol-description"]={en:["TSX 60 VIX Index"],nl_NL:["TSX 60 VIX"]},e.exports["#USDPLN-symbol-description"]={en:["U.S. Dollar / Polish Zloty"],nl_NL:["USD/PLN"]},e.exports["#EURPLN-symbol-description"]={en:["Euro / Polish Zloty"],nl_NL:["EUR/PLN"]},e.exports["#BTCPLN-symbol-description"]={en:["Bitcoin / Polish Zloty"],nl_NL:["Bitcoin / Polish Zloty"]},e.exports["#CAC40-symbol-description"]={en:["CAC 40 Index"],nl_NL:["CAC 40"]},e.exports["#XBTCAD-symbol-description"]={en:["Bitcoin / Canadian Dollar"],nl_NL:["Bitcoin / Canadian Dollar"]},e.exports["#ITI2!-symbol-description"]={en:["Iron Ore Futures"],nl_NL:["Iron Ore Futures"]},e.exports["#ITIF2018-symbol-description"]={en:["Iron Ore Futures"],nl_NL:["Iron Ore Futures"]},
e.exports["#ITIF2019-symbol-description"]={en:["Iron Ore Futures"],nl_NL:["Iron Ore Futures"]},e.exports["#ITIF2020-symbol-description"]={en:["Iron Ore Futures"],nl_NL:["Iron Ore Futures"]},e.exports["#ITIG2018-symbol-description"]={en:["Iron Ore Futures"],nl_NL:["Iron Ore Futures"]},e.exports["#ITIG2019-symbol-description"]={en:["Iron Ore Futures"],nl_NL:["Iron Ore Futures"]},e.exports["#ITIG2020-symbol-description"]={en:["Iron Ore Futures"],nl_NL:["Iron Ore Futures"]},e.exports["#ITIH2018-symbol-description"]={en:["Iron Ore Futures"],nl_NL:["Iron Ore Futures"]},e.exports["#ITIH2019-symbol-description"]={en:["Iron Ore Futures"],nl_NL:["Iron Ore Futures"]},e.exports["#ITIH2020-symbol-description"]={en:["Iron Ore Futures"],nl_NL:["Iron Ore Futures"]},e.exports["#ITIJ2018-symbol-description"]={en:["Iron Ore Futures"],nl_NL:["Iron Ore Futures"]},e.exports["#ITIJ2019-symbol-description"]={en:["Iron Ore Futures"],nl_NL:["Iron Ore Futures"]},e.exports["#ITIJ2020-symbol-description"]={en:["Iron Ore Futures"],nl_NL:["Iron Ore Futures"]},e.exports["#ITIK2018-symbol-description"]={en:["Iron Ore Futures"],nl_NL:["Iron Ore Futures"]},e.exports["#ITIK2019-symbol-description"]={en:["Iron Ore Futures"],nl_NL:["Iron Ore Futures"]},e.exports["#ITIK2020-symbol-description"]={en:["Iron Ore Futures"],nl_NL:["Iron Ore Futures"]},e.exports["#ITIM2017-symbol-description"]={en:["Iron Ore Futures"],nl_NL:["Iron Ore Futures"]},e.exports["#ITIM2018-symbol-description"]={en:["Iron Ore Futures"],nl_NL:["Iron Ore Futures"]},e.exports["#ITIM2019-symbol-description"]={en:["Iron Ore Futures"],nl_NL:["Iron Ore Futures"]},e.exports["#ITIM2020-symbol-description"]={en:["Iron Ore Futures"],nl_NL:["Iron Ore Futures"]},e.exports["#ITIN2017-symbol-description"]={en:["Iron Ore Futures"],nl_NL:["Iron Ore Futures"]},e.exports["#ITIN2018-symbol-description"]={en:["Iron Ore Futures"],nl_NL:["Iron Ore Futures"]},e.exports["#ITIN2019-symbol-description"]={en:["Iron Ore Futures"],nl_NL:["Iron Ore Futures"]},e.exports["#ITIN2020-symbol-description"]={en:["Iron Ore Futures"],nl_NL:["Iron Ore Futures"]},e.exports["#ITIQ2017-symbol-description"]={en:["Iron Ore Futures"],nl_NL:["Iron Ore Futures"]},e.exports["#ITIQ2018-symbol-description"]={en:["Iron Ore Futures"],nl_NL:["Iron Ore Futures"]},e.exports["#ITIQ2019-symbol-description"]={en:["Iron Ore Futures"],nl_NL:["Iron Ore Futures"]},e.exports["#ITIQ2020-symbol-description"]={en:["Iron Ore Futures"],nl_NL:["Iron Ore Futures"]},e.exports["#ITIU2017-symbol-description"]={en:["Iron Ore Futures"],nl_NL:["Iron Ore Futures"]},e.exports["#ITIU2018-symbol-description"]={en:["Iron Ore Futures"],nl_NL:["Iron Ore Futures"]},e.exports["#ITIU2019-symbol-description"]={en:["Iron Ore Futures"],nl_NL:["Iron Ore Futures"]},e.exports["#ITIU2020-symbol-description"]={en:["Iron Ore Futures"],nl_NL:["Iron Ore Futures"]},e.exports["#ITIV2017-symbol-description"]={en:["Iron Ore Futures"],nl_NL:["Iron Ore Futures"]},e.exports["#ITIV2018-symbol-description"]={en:["Iron Ore Futures"],nl_NL:["Iron Ore Futures"]},
e.exports["#ITIV2019-symbol-description"]={en:["Iron Ore Futures"],nl_NL:["Iron Ore Futures"]},e.exports["#ITIV2020-symbol-description"]={en:["Iron Ore Futures"],nl_NL:["Iron Ore Futures"]},e.exports["#ITIX2017-symbol-description"]={en:["Iron Ore Futures"],nl_NL:["Iron Ore Futures"]},e.exports["#ITIX2018-symbol-description"]={en:["Iron Ore Futures"],nl_NL:["Iron Ore Futures"]},e.exports["#ITIX2019-symbol-description"]={en:["Iron Ore Futures"],nl_NL:["Iron Ore Futures"]},e.exports["#ITIX2020-symbol-description"]={en:["Iron Ore Futures"],nl_NL:["Iron Ore Futures"]},e.exports["#ITIZ2017-symbol-description"]={en:["Iron Ore Futures"],nl_NL:["Iron Ore Futures"]},e.exports["#ITIZ2018-symbol-description"]={en:["Iron Ore Futures"],nl_NL:["Iron Ore Futures"]},e.exports["#ITIZ2019-symbol-description"]={en:["Iron Ore Futures"],nl_NL:["Iron Ore Futures"]},e.exports["#ITIZ2020-symbol-description"]={en:["Iron Ore Futures"],nl_NL:["Iron Ore Futures"]},e.exports["#AMEX:GXF-symbol-description"]={en:["Global x FTSE Nordic Region ETF"],nl_NL:["Global x FTSE Nordic Region ETF"]},e.exports["#ASX:XAF-symbol-description"]={en:["S&P/ASX All Australian 50 Index"],nl_NL:["S&P/ASX All Australian 50 Index"]},e.exports["#ASX:XAT-symbol-description"]={en:["S&P/ASX All Australian 200 Index"],nl_NL:["S&P/ASX All Australian 200 Index"]},e.exports["#BIST:XU100-symbol-description"]={en:["BIST 100 Index"],nl_NL:["BIST 100 Index"]},e.exports["#GPW:WIG20-symbol-description"]={en:["WIG20 Index"],nl_NL:["WIG20 Index"]},e.exports["#INDEX:JKSE-symbol-description"]={en:["Jakarta Composite Index"],nl_NL:["Jakarta Composite Index"]},e.exports["#INDEX:KLSE-symbol-description"]={en:["Bursa Malaysia KLCI Index"],nl_NL:["Bursa Malaysia KLCI Index"]},e.exports["#INDEX:NZD-symbol-description"]={en:["NZX 50 Index"],nl_NL:["NZX 50 Index"]},e.exports["#INDEX:STI-symbol-description"]={en:["STI Index"],nl_NL:["STI Index"]},e.exports["#INDEX:XLY0-symbol-description"]={en:["Shanghai Composite Index"],nl_NL:["Shanghai Composite Index"]},e.exports["#MOEX:MICEXINDEXCF-symbol-description"]={en:["MOEX Russia Index"],nl_NL:["MOEX Russia Index"]},e.exports["#NYMEX:KT1!-symbol-description"]={en:["Coffee Futures"],nl_NL:["Coffee Futures"]},e.exports["#OANDA:NATGASUSD-symbol-description"]={en:["CFDs on Natural Gas"],nl_NL:["CFDs on Natural Gas"]},e.exports["#OANDA:USDPLN-symbol-description"]={en:["U.S. Dollar / Polish Zloty"],nl_NL:["U.S. Dollar / Polish Zloty"]},e.exports["#TSX:TX60-symbol-description"]={en:["S&P/TSX 60 Index"],nl_NL:["S&P/TSX 60 Index"]},e.exports["#TSX:VBU-symbol-description"]={en:["Vanguard U.S. Aggregate Bond Index ETF (CAD-hedged) UN"],nl_NL:["Vanguard U.S. Aggregate Bond Index ETF (CAD-hedged) UN"]},e.exports["#TSX:VIXC-symbol-description"]={en:["S&P/TSX 60 VIX Index"],nl_NL:["S&P/TSX 60 VIX Index"]},e.exports["#TVC:CAC40-symbol-description"]={en:["CAC 40 Index"],nl_NL:["CAC 40 Index"]},e.exports["#TVC:ES10-symbol-description"]={en:["Spain Government Bonds 10 YR"],nl_NL:["Spain Government Bonds 10 YR"]},e.exports["#TVC:EUBUND-symbol-description"]={
en:["Euro Bund"],nl_NL:["Euro Bund"]},e.exports["#TVC:GB02-symbol-description"]={en:["UK Government Bonds 2 YR"],nl_NL:["UK Government Bonds 2 YR"]},e.exports["#TVC:GB10-symbol-description"]={en:["UK Government Bonds 10 YR"],nl_NL:["UK Government Bonds 10 YR"]},e.exports["#TVC:GOLD-symbol-description"]={en:["CFDs on Gold (US$ / OZ)"],nl_NL:["CFDs on Gold (US$ / OZ)"]},e.exports["#TVC:ID03-symbol-description"]={en:["Indonesia Government Bonds 3 YR"],nl_NL:["Indonesia Government Bonds 3 YR"]},e.exports["#TVC:ID10-symbol-description"]={en:["Indonesia Government Bonds 10 YR"],nl_NL:["Indonesia Government Bonds 10 YR"]},e.exports["#TVC:PALLADIUM-symbol-description"]={en:["CFDs on Palladium (US$ / OZ)"],nl_NL:["CFDs on Palladium (US$ / OZ)"]},e.exports["#TVC:PT10-symbol-description"]={en:["Portugal Government Bonds 10 YR"],nl_NL:["Portugal Government Bonds 10 YR"]},e.exports["#TVC:SILVER-symbol-description"]={en:["CFDs on Silver (US$ / OZ)"],nl_NL:["CFDs on Silver (US$ / OZ)"]},e.exports["#TSX:TSX-symbol-description"]={en:["S&P/TSX Composite Index"],nl_NL:["S&P/TSX Composite Index"]},e.exports["#OANDA:CH20CHF-symbol-description"]={en:["Swiss 20 Index"],nl_NL:["Swiss 20 Index"]},e.exports["#TVC:SHCOMP-symbol-description"]={en:["Shanghai Composite Index"],nl_NL:["Shanghai Composite Index"]},e.exports["#NZX:ALLC-symbol-description"]={en:["S&P/NZX All Index (Capital Index)"],nl_NL:["S&P/NZX All Index (Capital Index)"]},e.exports["#AMEX:SHYG-symbol-description"]={en:["Shares 0-5 YEAR High Yield Corporate Bond ETF"],nl_NL:["Shares 0-5 YEAR High Yield Corporate Bond ETF"]},e.exports["#TVC:AU10-symbol-description"]={en:["Australia Government Bonds 10 YR"],nl_NL:["Australia Government Bonds 10 YR"]},e.exports["#TVC:CN10-symbol-description"]={en:["China Government Bonds 10 YR"],nl_NL:["China Government Bonds 10 YR"]},e.exports["#TVC:KR10-symbol-description"]={en:["Korea Government Bonds 10 YR"],nl_NL:["Korea Government Bonds 10 YR"]},e.exports["#NYMEX:RB1!-symbol-description"]={en:["RBOB Gasoline Futures"],nl_NL:["RBOB Gasoline Futures"]},e.exports["#NYMEX:HO1!-symbol-description"]={en:["NY Harbor ULSD Futures"],nl_NL:["NY Harbor ULSD Futures"]},e.exports["#NYMEX:AEZ1!-symbol-description"]={en:["NY Ethanol Futures"],nl_NL:["NY Ethanol Futures"]},e.exports["#OANDA:XCUUSD-symbol-description"]={en:["CFDs on Copper (US$ / lb)"],nl_NL:["CFDs on Copper (US$ / lb)"]},e.exports["#COMEX:ZA1!-symbol-description"]={en:["Zinc Futures"],nl_NL:["Zinc Futures"]},e.exports["#CBOT:ZW1!-symbol-description"]={en:["Wheat Futures"],nl_NL:["Wheat Futures"]},e.exports["#NYMEX:KA1!-symbol-description"]={en:["Sugar #11 Futures"],nl_NL:["Sugar #11 Futures"]},e.exports["#CBOT:QBC1!-symbol-description"]={en:["Corn Futures"],nl_NL:["Corn Futures"]},e.exports["#CME:E61!-symbol-description"]={en:["Euro Futures"],nl_NL:["Euro Futures"]},e.exports["#CME:B61!-symbol-description"]={en:["British Pound Futures"],nl_NL:["British Pound Futures"]},e.exports["#CME:QJY1!-symbol-description"]={en:["Japanese Yen Futures"],nl_NL:["Japanese Yen Futures"]},
e.exports["#CME:A61!-symbol-description"]={en:["Australian Dollar Futures"],nl_NL:["Australian Dollar Futures"]},e.exports["#CME:D61!-symbol-description"]={en:["Canadian Dollar Futures"],nl_NL:["Canadian Dollar Futures"]},e.exports["#CME:SP1!-symbol-description"]={en:["S&P 500 Futures"],nl_NL:["S&P 500 Futures"]},e.exports["#CME_MINI:NQ1!-symbol-description"]={en:["NASDAQ 100 E-mini Futures"],nl_NL:["NASDAQ 100 E-mini Futures"]},e.exports["#CBOT_MINI:YM1!-symbol-description"]={en:["E-mini Dow Jones ($5) Futures"],nl_NL:["E-mini Dow Jones ($5) Futures"]},e.exports["#CME:NY1!-symbol-description"]={en:["NIKKEI 225 Futures"],nl_NL:["NIKKEI 225 Futures"]},e.exports["#EUREX:DY1!-symbol-description"]={en:["DAX Index"],nl_NL:["DAX Index"]},e.exports["#CME:IF1!-symbol-description"]={en:["IBOVESPA Index Futures-US$"],nl_NL:["IBOVESPA Index Futures-US$"]},e.exports["#CBOT:TY1!-symbol-description"]={en:["10 Year T-Note Futures"],nl_NL:["10 Year T-Note Futures"]},e.exports["#CBOT:FV1!-symbol-description"]={en:["5 Year T-Note Futures"],nl_NL:["5 Year T-Note Futures"]},e.exports["#CBOT:ZE1!-symbol-description"]={en:["Treasury Notes - 3 Year Futures"],nl_NL:["Treasury Notes - 3 Year Futures"]},e.exports["#CBOT:TU1!-symbol-description"]={en:["2 Year T-Note Futures"],nl_NL:["2 Year T-Note Futures"]},e.exports["#CBOT:FF1!-symbol-description"]={en:["30-Day FED Funds Interest Rate Futures"],nl_NL:["30-Day FED Funds Interest Rate Futures"]},e.exports["#CBOT:US1!-symbol-description"]={en:["T-Bond Futures"],nl_NL:["T-Bond Futures"]},e.exports["#TVC:EXY-symbol-description"]={en:["Euro Currency Index"],nl_NL:["Euro Currency Index"]},e.exports["#TVC:JXY-symbol-description"]={en:["Japanese Yen Currency Index"],nl_NL:["Japanese Yen Currency Index"]},e.exports["#TVC:BXY-symbol-description"]={en:["British Pound Currency Index"],nl_NL:["British Pound Currency Index"]},e.exports["#TVC:AXY-symbol-description"]={en:["Australian Dollar Currency Index"],nl_NL:["Australian Dollar Currency Index"]},e.exports["#TVC:CXY-symbol-description"]={en:["Canadian Dollar Currency Index"],nl_NL:["Canadian Dollar Currency Index"]},e.exports["#FRED:GDP-symbol-description"]={en:["Gross Domestic Product, 1 Decimal"],nl_NL:["Gross Domestic Product, 1 Decimal"]},e.exports["#FRED:UNRATE-symbol-description"]={en:["Civilian Unemployment Rate"],nl_NL:["Civilian Unemployment Rate"]},e.exports["#FRED:POP-symbol-description"]={en:["Total Population: All Ages Including Armed Forces Overseas"],nl_NL:["Total Population: All Ages Including Armed Forces Overseas"]},e.exports["#ETHUSD-symbol-description"]={en:["Ethereum / U.S. Dollar"],nl_NL:["Ethereum / U.S. Dollar"]},e.exports["#BMFBOVESPA:IBOV-symbol-description"]={en:["Bovespa Index"],nl_NL:["IBovespa Index"]},e.exports["#BMFBOVESPA:IBRA-symbol-description"]={en:["IBrasil Index"],nl_NL:["IBrasil Index"]},e.exports["#BMFBOVESPA:IBXL-symbol-description"]={en:["Brazil 50 Index"],nl_NL:["IBRX 50 Index"]},e.exports["#COMEX:HG1!-symbol-description"]={en:["Copper Futures"],nl_NL:["Copper Futures"]},
e.exports["#INDEX:HSCE-symbol-description"]={en:["Hang Seng China Enterprises Index"],nl_NL:["Hang Seng China Enterprises Index"]},e.exports["#NYMEX:CL1!-symbol-description"]={en:["Light Crude Oil Futures"],nl_NL:["Light Crude Oil Futures"]},e.exports["#OTC:IHRMF-symbol-description"]={en:["Ishares MSCI Japan SHS"],nl_NL:["Ishares MSCI Japan SHS"]},e.exports["#TVC:DAX-symbol-description"]={en:["DAX Index"],nl_NL:["DAX Index"]},e.exports["#TVC:DE10-symbol-description"]={en:["German Government Bonds 10 YR"],nl_NL:["German Government Bonds 10 YR"]},e.exports["#TVC:DJI-symbol-description"]={en:["Dow Jones Industrial Average Index"],nl_NL:["Dow Jones Industrial Average Index"]},e.exports["#TVC:DXY-symbol-description"]={en:["U.S. Dollar Index"],nl_NL:["U.S. Dollar Currency Index"]},e.exports["#TVC:FR10-symbol-description"]={en:["France Government Bonds 10 YR"],nl_NL:["France Government Bonds 10 YR"]},e.exports["#TVC:HSI-symbol-description"]={en:["Hang Seng Index"],nl_NL:["Hang Seng Index"]},e.exports["#TVC:IBEX35-symbol-description"]={en:["IBEX 35 Index"],nl_NL:["IBEX 35 Index"]},e.exports["#FX:AUS200-symbol-description"]={en:["S&P/ASX Index"],nl_NL:["S&P/ASX Index"]},e.exports["#AMEX:SHY-symbol-description"]={en:["Ishares 1-3 Year Treasury Bond ETF"],nl_NL:["Ishares 1-3 Year Treasury Bond ETF"]},e.exports["#ASX:XJO-symbol-description"]={en:["S&P/ASX 200 Index"],nl_NL:["S&P/ASX 200 Index"]},e.exports["#BSE:SENSEX-symbol-description"]={en:["S&P BSE Sensex Index"],nl_NL:["S&P BSE Sensex Index"]},e.exports["#INDEX:MIB-symbol-description"]={en:["MIB Index"],nl_NL:["MIB Index"]},e.exports["#INDEX:MOY0-symbol-description"]={en:["Euro Stoxx 50 Index"],nl_NL:["Euro Stoxx 50 Index"]},e.exports["#MOEX:RTSI-symbol-description"]={en:["RTS Index"],nl_NL:["RTS Index"]},e.exports["#NSE:NIFTY-symbol-description"]={en:["Nifty 50 Index"],nl_NL:["Nifty 50 Index"]},e.exports["#NYMEX:NG1!-symbol-description"]={en:["Natural Gas Futures"],nl_NL:["Natural Gas Futures"]},e.exports["#NYMEX:ZC1!-symbol-description"]={en:["Corn Futures"],nl_NL:["Corn Futures"]},e.exports["#TVC:IN10-symbol-description"]={en:["India Government Bonds 10 YR"],nl_NL:["India Government Bonds 10 YR"]},e.exports["#TVC:IT10-symbol-description"]={en:["Italy Government Bonds 10 YR"],nl_NL:["Italy Government Bonds 10 YR"]},e.exports["#TVC:JP10-symbol-description"]={en:["Japan Government Bonds 10 YR"],nl_NL:["Japan Government Bonds 10 YR"]},e.exports["#TVC:NDX-symbol-description"]={en:["US 100 Index"],nl_NL:["NASDAQ 100 Index"]},e.exports["#TVC:NI225-symbol-description"]={en:["Nikkei 225 Index"],nl_NL:["Nikkei 225 Index"]},e.exports["#TVC:SPX-symbol-description"]={en:["S&P 500 Index"],nl_NL:["S&P 500 Index"]},e.exports["#TVC:SX5E-symbol-description"]={en:["Euro Stoxx 50 Index"],nl_NL:["Euro Stoxx 50 Index"]},e.exports["#TVC:TR10-symbol-description"]={en:["Turkey Government Bonds 10 YR"],nl_NL:["Turkey Government Bonds 10 YR"]},e.exports["#TVC:UKOIL-symbol-description"]={en:["CFDs on Brent Crude Oil"],nl_NL:["CFDs on Brent Crude Oil"]},e.exports["#TVC:UKX-symbol-description"]={
en:["UK 100 Index"],nl_NL:["UK 100 Index"]},e.exports["#TVC:US02-symbol-description"]={en:["US Government Bonds 2 YR"],nl_NL:["US Government Bonds 2 YR"]},e.exports["#TVC:US05-symbol-description"]={en:["US Government Bonds 5 YR"],nl_NL:["US Government Bonds 5 YR"]},e.exports["#TVC:US10-symbol-description"]={en:["US Government Bonds 10 YR"],nl_NL:["US Government Bonds 10 YR"]},e.exports["#TVC:USOIL-symbol-description"]={en:["CFDs on WTI Crude Oil"],nl_NL:["CFDs on WTI Crude Oil"]},e.exports["#NYMEX:ITI1!-symbol-description"]={en:["Iron Ore Futures"],nl_NL:["Iron Ore Futures"]},e.exports["#NASDAQ:SHY-symbol-description"]={en:["Ishares 1-3 Year Treasury Bond ETF"],nl_NL:["Ishares 1-3 Year Treasury Bond ETF"]},e.exports["#AMEX:ALD-symbol-description"]={en:["WisdomTree Asia Local Debt ETF"],nl_NL:["WisdomTree Asia Local Debt ETF"]},e.exports["#NASDAQ:AMD-symbol-description"]={en:["Advanced Micro Devices Inc"],nl_NL:["Advanced Micro Devices Inc"]},e.exports["#NYSE:BABA-symbol-description"]={en:["Alibaba Group Holdings Ltd."],nl_NL:["ALIBABA GROUP HLDG LTD"]},e.exports["#ICEEUR:CB-symbol-description"]={en:["Crude Oil Brent"],nl_NL:["Crude Oil Brent"]},e.exports["#ICEEUR:CB1!-symbol-description"]={en:["Brent Crude Oil"],nl_NL:["Brent Crude Oil"]},e.exports["#ICEUSA:CC-symbol-description"]={en:["Cocoa"],nl_NL:["Cocoa"]},e.exports["#NYMEX:CL-symbol-description"]={en:["Crude Oil WTI"],nl_NL:["Crude Oil WTI"]},e.exports["#ICEUSA:CT-symbol-description"]={en:["Cotton #2"],nl_NL:["Cotton #2"]},e.exports["#NASDAQ:CTRV-symbol-description"]={en:["ContraVir Pharmaceuticals Inc"],nl_NL:["CONTRAVIR PHARMACEUTICALS INC"]},e.exports["#CME:DL-symbol-description"]={en:["Class III Milk"],nl_NL:["Class III Milk"]},e.exports["#NYSE:F-symbol-description"]={en:["FORD MTR CO DEL"],nl_NL:["FORD MTR CO DEL"]},e.exports["#MOEX:GAZP-symbol-description"]={en:["GAZPROM"],nl_NL:["GAZPROM"]},e.exports["#COMEX:GC-symbol-description"]={en:["Gold"],nl_NL:["Gold"]},e.exports["#CME:GF-symbol-description"]={en:["Feeder Cattle"],nl_NL:["Feeder Cattle"]},e.exports["#CME:HE-symbol-description"]={en:["Lean Hogs"],nl_NL:["Lean Hogs"]},e.exports["#NASDAQ:IEF-symbol-description"]={en:["Ishares 7-10 Year Treasury Bond ETF"],nl_NL:["Ishares 7-10 Year Treasury Bond ETF"]},e.exports["#NASDAQ:IEI-symbol-description"]={en:["Ishares 3-7 Year Treasury Bond ETF"],nl_NL:["Ishares 3-7 Year Treasury Bond ETF"]},e.exports["#NYMEX:KA1-symbol-description"]={en:["Sugar #11 Futures"],nl_NL:["Sugar #11 Futures"]},e.exports["#ICEUSA:KC-symbol-description"]={en:["Coffee"],nl_NL:["Coffee"]},e.exports["#NYMEX:KG1-symbol-description"]={en:["Cotton Futures"],nl_NL:["Cotton Futures"]},e.exports["#FWB:KT1-symbol-description"]={en:["Key Tronic Corр."],nl_NL:["Key Tronic Corр."]},e.exports["#CME:LE-symbol-description"]={en:["Live Cattle"],nl_NL:["Live Cattle"]},e.exports["#ICEEUR:LO-symbol-description"]={en:["ICE Heating Oil"],nl_NL:["ICE Heating Oil"]},e.exports["#CME:LS-symbol-description"]={en:["Lumber"],nl_NL:["Lumber"]},e.exports["#MOEX:MGNT-symbol-description"]={en:["MAGNIT"],
nl_NL:["MAGNIT"]},e.exports["#LSIN:MNOD-symbol-description"]={en:["ADR GMK NORILSKIYNIKEL ORD SHS [REPO]"],nl_NL:["ADR GMK NORILSKIYNIKEL ORD SHS [REPO]"]},e.exports["#NYMEX:NG-symbol-description"]={en:["Natural Gas"],nl_NL:["Natural Gas"]},e.exports["#ICEUSA:OJ-symbol-description"]={en:["Orange Juice"],nl_NL:["Orange Juice"]},e.exports["#NYMEX:PA-symbol-description"]={en:["Palladium"],nl_NL:["Palladium"]},e.exports["#NYSE:PBR-symbol-description"]={en:["PETROLEO BRASILEIRO SA PETROBR"],nl_NL:["PETROLEO BRASILEIRO SA PETROBR"]},e.exports["#NYMEX:PL-symbol-description"]={en:["Platinum"],nl_NL:["Platinum"]},e.exports["#COMEX_MINI:QC-symbol-description"]={en:["E-Mini Copper"],nl_NL:["E-Mini Copper"]},e.exports["#NYMEX:RB-symbol-description"]={en:["Gasoline RBOB"],nl_NL:["Gasoline RBOB"]},e.exports["#NYMEX:RB1-symbol-description"]={en:["RBOB Gasoline Futures"],nl_NL:["RBOB Gasoline Futures"]},e.exports["#MOEX:SBER-symbol-description"]={en:["SBERBANK"],nl_NL:["SBERBANK"]},e.exports["#AMEX:SCHO-symbol-description"]={en:["Schwab Short-Term U.S. Treasury ETF"],nl_NL:["Schwab Short-Term U.S. Treasury ETF"]},e.exports["#COMEX:SI-symbol-description"]={en:["Silver"],nl_NL:["Silver"]},e.exports["#NASDAQ:TLT-symbol-description"]={en:["Ishares 20+ Year Treasury Bond ETF"],nl_NL:["Ishares 20+ Year Treasury Bond ETF"]},e.exports["#TVC:VIX-symbol-description"]={en:["Volatility S&P 500 Index"],nl_NL:["Volatility S&P 500 Index"]},e.exports["#MOEX:VTBR-symbol-description"]={en:["VTB"],nl_NL:["VTB"]},e.exports["#COMEX:ZA-symbol-description"]={en:["Zinc"],nl_NL:["Zinc"]},e.exports["#CBOT:ZC-symbol-description"]={en:["Corn"],nl_NL:["Corn"]},e.exports["#CBOT:ZK-symbol-description"]={en:["Ethanol Futures"],nl_NL:["Ethanol Futures"]},e.exports["#CBOT:ZL-symbol-description"]={en:["Soybean Oil"],nl_NL:["Soybean Oil"]},e.exports["#CBOT:ZO-symbol-description"]={en:["Oats"],nl_NL:["Oats"]},e.exports["#CBOT:ZR-symbol-description"]={en:["Rough Rice"],nl_NL:["Rough Rice"]},e.exports["#CBOT:ZS-symbol-description"]={en:["Soybeans"],nl_NL:["Soybeans"]},e.exports["#CBOT:ZS1-symbol-description"]={en:["Soybean Futures"],nl_NL:["Soybean Futures"]},e.exports["#CBOT:ZW-symbol-description"]={en:["Wheat"],nl_NL:["Wheat"]},e.exports["#CBOT:ZW1-symbol-description"]={en:["Wheat Futures - ECBT"],nl_NL:["Wheat Futures - ECBT"]},e.exports["#NASDAQ:ITI-symbol-description"]={en:["Iteris Inc"],nl_NL:["Iteris Inc."]},e.exports["#NYMEX:ITI2!-symbol-description"]={en:["Iron Ore Futures"],nl_NL:["Iron Ore Futures"]},e.exports["#CADUSD-symbol-description"]={en:["Canadian Dollar / U.S. Dollar"],nl_NL:["Canadian Dollar / U.S. Dollar"]},e.exports["#CHFUSD-symbol-description"]={en:["Swiss Franc / U.S. Dollar"],nl_NL:["Swiss Franc / U.S. Dollar"]},e.exports["#GPW:ACG-symbol-description"]={en:["Acautogaz"],nl_NL:["Acautogaz"]},e.exports["#JPYUSD-symbol-description"]={en:["Japanese Yen / U.S. Dollar"],nl_NL:["Japanese Yen / U.S. Dollar"]},e.exports["#USDAUD-symbol-description"]={en:["U.S. Dollar / Australian Dollar"],nl_NL:["U.S. Dollar / Australian Dollar"]},
e.exports["#USDEUR-symbol-description"]={en:["U.S. Dollar / Euro"],nl_NL:["U.S. Dollar / Euro"]},e.exports["#USDGBP-symbol-description"]={en:["U.S. Dollar / Pound Sterling"],nl_NL:["U.S. Dollar / Pound Sterling"]},e.exports["#USDNZD-symbol-description"]={en:["U.S. Dollar / New Zealand Dollar"],nl_NL:["U.S. Dollar / New Zealand Dollar"]},e.exports["#UKOIL-symbol-description"]={en:["CFDs on Crude Oil (Brent)"],nl_NL:["CFDs on Crude Oil (Brent)"]},e.exports["#USOIL-symbol-description"]={en:["CFDs on Crude Oil (WTI)"],nl_NL:["CFDs on Crude Oil (WTI)"]},e.exports["#US30-symbol-description"]={en:["Dow Jones Industrial Average Index"],nl_NL:["Dow Jones Industrial Average Index"]},e.exports["#BCHUSD-symbol-description"]={en:["Bitcoin Cash / U.S. Dollar"],nl_NL:["Bitcoin Cash / U.S. Dollar"]},e.exports["#ETCUSD-symbol-description"]={en:["Ethereum Classic / U.S. Dollar"],nl_NL:["Ethereum Classic / U.S. Dollar"]},e.exports["#GOOG-symbol-description"]={en:["Alphabet Inc (Google) Class C"],nl_NL:["Alphabet Inc (Google) Class C"]},e.exports["#LTCUSD-symbol-description"]={en:["Litecoin / U.S. Dollar"],nl_NL:["Litecoin / U.S. Dollar"]},e.exports["#XRPUSD-symbol-description"]={en:["XRP / U.S. Dollar"],nl_NL:["Ripple / U.S. Dollar"]},e.exports["#SP:SPX-symbol-description"]={en:["S&P 500 Index"],nl_NL:["S&P 500 Index"]},e.exports["#ETCBTC-symbol-description"]={en:["Ethereum Classic / Bitcoin"],nl_NL:["Ethereum Classic / Bitcoin"]},e.exports["#ETHBTC-symbol-description"]={en:["Ethereum / Bitcoin"],nl_NL:["Ethereum / Bitcoin"]},e.exports["#XRPBTC-symbol-description"]={en:["XRP / Bitcoin"],nl_NL:["Ripple / Bitcoin"]},e.exports["#TVC:US30-symbol-description"]={en:["US Government Bonds 30 YR"],nl_NL:["US Government Bonds 30 YR"]},e.exports["#COMEX:SI1!-symbol-description"]={en:["Silver Futures"],nl_NL:["Silver Futures"]},e.exports["#BTGUSD-symbol-description"]={en:["Bitcoin Gold / U.S. Dollar"],nl_NL:["Bitcoin Gold / U.S. Dollar"]},e.exports["#IOTUSD-symbol-description"]={en:["IOTA / U.S. Dollar"],nl_NL:["IOTA / U.S. Dollar"]},e.exports["#CME:BTC1!-symbol-description"]={en:["Bitcoin CME Futures"],nl_NL:["Bitcoin CME Futures"]},e.exports["#COMEX:GC1!-symbol-description"]={en:["Gold Futures"],nl_NL:["Gold Futures"]},e.exports["#CORNUSD-symbol-description"]={en:["CFDs on Corn"],nl_NL:["CFDs on Corn"]},e.exports["#COTUSD-symbol-description"]={en:["CFDs on Cotton"],nl_NL:["CFDs on Cotton"]},e.exports["#DJ:DJA-symbol-description"]={en:["Dow Jones Composite Average Index"],nl_NL:["Dow Jones Composite Average Index"]},e.exports["#DJ:DJI-symbol-description"]={en:["Dow Jones Industrial Average Index"],nl_NL:["Dow Jones Industrial Average Index"]},e.exports["#ETHEUR-symbol-description"]={en:["Ethereum / Euro"],nl_NL:["Ethereum / Euro"]},e.exports["#ETHGBP-symbol-description"]={en:["Ethereum / British Pound"],nl_NL:["Ethereum / British Pound"]},e.exports["#ETHJPY-symbol-description"]={en:["Ethereum / Japanese Yen"],nl_NL:["Ethereum / Japanese Yen"]},e.exports["#EURNOK-symbol-description"]={en:["Euro / Norwegian Krone"],nl_NL:["Euro / Norwegian Krone"]
},e.exports["#GBPPLN-symbol-description"]={en:["British Pound / Polish Zloty"],nl_NL:["British Pound / Polish Zloty"]},e.exports["#MOEX:BR1!-symbol-description"]={en:["Brent Oil Futures"],nl_NL:["Brent Oil Futures"]},e.exports["#NYMEX:KG1!-symbol-description"]={en:["Cotton Futures"],nl_NL:["Cotton Futures"]},e.exports["#NYMEX:PL1!-symbol-description"]={en:["Platinum Futures"],nl_NL:["Platinum Futures"]},e.exports["#SOYBNUSD-symbol-description"]={en:["CFDs on Soybeans"],nl_NL:["CFDs on Soybeans"]},e.exports["#SUGARUSD-symbol-description"]={en:["CFDs on Sugar"],nl_NL:["CFDs on Sugar"]},e.exports["#TVC:IXIC-symbol-description"]={en:["US Composite Index"],nl_NL:["NASDAQ Composite Index"]},e.exports["#TVC:RU-symbol-description"]={en:["Russell 1000 Index"],nl_NL:["Russell 1000 Index"]},e.exports["#USDZAR-symbol-description"]={en:["U.S. Dollar / South African Rand"],nl_NL:["U.S. Dollar / South African Rand"]},e.exports["#WHEATUSD-symbol-description"]={en:["CFDs on Wheat"],nl_NL:["CFDs on Wheat"]},e.exports["#XRPEUR-symbol-description"]={en:["XRP / Euro"],nl_NL:["Ripple / Euro"]},e.exports["#CBOT:S1!-symbol-description"]={en:["Soybean Futures"],nl_NL:["Soybean Futures"]},e.exports["#SP:MID-symbol-description"]={en:["S&P 400 Index"],nl_NL:["S&P 400 Index"]},e.exports["#TSX:XCUUSD-symbol-description"]={en:["CFDs on Copper"],nl_NL:["CFDs on Copper"]},e.exports["#TVC:NYA-symbol-description"]={en:["NYSE Composite Index"],nl_NL:["NYSE Composite Index"]},e.exports["#TVC:PLATINUM-symbol-description"]={en:["CFDs on Platinum (US$ / OZ)"],nl_NL:["CFDs on Platinum (US$ / OZ)"]},e.exports["#TVC:SSMI-symbol-description"]={en:["Swiss Market Index"],nl_NL:["Swiss Market Index"]},e.exports["#TVC:SXY-symbol-description"]={en:["Swiss Franc Currency Index"],nl_NL:["Swiss Franc Currency Index"]},e.exports["#MOEX:RI1!-symbol-description"]={en:["RTS Index Futures"],nl_NL:["RTS Index Futures"]},e.exports["#MOEX:MX1!-symbol-description"]={en:["MICEX Index Futures"],nl_NL:["MICEX Index Futures"]},e.exports["#CBOE:BG1!-symbol-description"]={en:["Bitcoin CBOE Futures"],nl_NL:["Bitcoin CBOE Futures"]},e.exports["#TVC:MY10-symbol-description"]={en:["Malaysia Government Bonds 10 YR"],nl_NL:["Malaysia Government Bonds 10 YR"]},e.exports["#CME:S61!-symbol-description"]={en:["Swiss Franc Futures"],nl_NL:["Swiss Franc Futures"]},e.exports["#TVC:DEU30-symbol-description"]={en:["DAX Index"],nl_NL:["DAX Index"]},e.exports["#BCHEUR-symbol-description"]={en:["Bitcoin Cash / Euro"],nl_NL:["Bitcoin Cash / Euro"]},e.exports["#TVC:ZXY-symbol-description"]={en:["New Zealand Dollar Currency Index"],nl_NL:["New Zealand Dollar Currency Index"]},e.exports["#MIL:FTSEMIB-symbol-description"]={en:["FTSE MIB Index"],nl_NL:["FTSE MIB Index"]},e.exports["#XETR:DAX-symbol-description"]={en:["DAX Index"],nl_NL:["DAX Index"]},e.exports["#MOEX:IMOEX-symbol-description"]={en:["MOEX Russia Index"],nl_NL:["MOEX Russia Index"]},e.exports["#FX:US30-symbol-description"]={en:["Dow Jones Industrial Average Index"],nl_NL:["Dow Jones Industrial Average Index"]},
e.exports["#MOEX:RUAL-symbol-description"]={en:["United Company RUSAL PLC"],nl_NL:["United Company RUSAL PLC"]},e.exports["#MOEX:MX2!-symbol-description"]={en:["MICEX Index Futures"],nl_NL:["MICEX Index Futures"]},e.exports["#NEOUSD-symbol-description"]={en:["NEO / U.S. Dollar"],nl_NL:["NEO / U.S. Dollar"]},e.exports["#XMRUSD-symbol-description"]={en:["Monero / U.S. Dollar"],nl_NL:["Monero / U.S. Dollar"]},e.exports["#ZECUSD-symbol-description"]={en:["Zcash / U.S. Dollar"],nl_NL:["Zcash / U.S. Dollar"]},e.exports["#TVC:CAC-symbol-description"]={en:["CAC 40 Index"],nl_NL:["CAC 40 Index"]},e.exports["#NASDAQ:ZS-symbol-description"]={en:["Zscaler Inc"],nl_NL:["Zscaler Inc"]},e.exports["#TVC:GB10Y-symbol-description"]={en:["UK Government Bonds 10 YR Yield"],nl_NL:["UK Government Bonds 10 YR Yield"]},e.exports["#TVC:AU10Y-symbol-description"]={en:["Australia Government Bonds 10 YR Yield"],nl_NL:["Australia Government Bonds 10 YR Yield"]},e.exports["#TVC:CN10Y-symbol-description"]={en:["China Government Bonds 10 YR Yield"],nl_NL:["China Government Bonds 10 YR Yield"]},e.exports["#TVC:DE10Y-symbol-description"]={en:["German Government Bonds 10 YR Yield"],nl_NL:["German Government Bonds 10 YR Yield"]},e.exports["#TVC:ES10Y-symbol-description"]={en:["Spain Government Bonds 10 YR Yield"],nl_NL:["Spain Government Bonds 10 YR Yield"]},e.exports["#TVC:FR10Y-symbol-description"]={en:["France Government Bonds 10 YR Yield"],nl_NL:["France Government Bonds 10 YR Yield"]},e.exports["#TVC:IN10Y-symbol-description"]={en:["India Government Bonds 10 YR Yield"],nl_NL:["India Government Bonds 10 yr"]},e.exports["#TVC:IT10Y-symbol-description"]={en:["Italy Government Bonds 10 YR Yield"],nl_NL:["Italy Government Bonds 10 yr"]},e.exports["#TVC:JP10Y-symbol-description"]={en:["Japan Government Bonds 10 YR Yield"],nl_NL:["Japan Government Bonds 10 yr"]},e.exports["#TVC:KR10Y-symbol-description"]={en:["Korea Government Bonds 10 YR Yield"],nl_NL:["Korea Government Bonds 10 YR Yield"]},e.exports["#TVC:MY10Y-symbol-description"]={en:["Malaysia Government Bonds 10 YR Yield"],nl_NL:["Malaysia Government Bonds 10 YR Yield"]},e.exports["#TVC:PT10Y-symbol-description"]={en:["Portugal Government Bonds 10 YR Yield"],nl_NL:["Portugal Government Bonds 10 YR Yield"]},e.exports["#TVC:TR10Y-symbol-description"]={en:["Turkey Government Bonds 10 YR Yield"],nl_NL:["Turkey Government Bonds 10 YR"]},e.exports["#TVC:US02Y-symbol-description"]={en:["US Government Bonds 2 YR Yield"],nl_NL:["US Government Bonds 2 yr"]},e.exports["#TVC:US05Y-symbol-description"]={en:["US Government Bonds 5 YR Yield"],nl_NL:["US Government Bonds 5 yr"]},e.exports["#TVC:US10Y-symbol-description"]={en:["US Government Bonds 10 YR Yield"],nl_NL:["US Government Bonds 10 yr"]},e.exports["#INDEX:TWII-symbol-description"]={en:["Taiwan Weighted Index"],nl_NL:["Taiwan Weighted Index"]},e.exports["#CME:J61!-symbol-description"]={en:["Japanese Yen Futures"],nl_NL:["Japanese Yen Futures"]},e.exports["#CME_MINI:J71!-symbol-description"]={en:["Japanese Yen E-mini Futures"],
nl_NL:["Japanese Yen E-mini Futures"]},e.exports["#CME_MINI:WM1!-symbol-description"]={en:["E-micro Japanese Yen / U.S. Dollar Futures"],nl_NL:["E-micro Japanese Yen / U.S. Dollar Futures"]},e.exports["#CME:M61!-symbol-description"]={en:["Mexican Peso Futures"],nl_NL:["Mexican Peso Futures"]},e.exports["#CME:T61!-symbol-description"]={en:["South African Rand Futures"],nl_NL:["South African Rand Futures"]},e.exports["#CME:SK1!-symbol-description"]={en:["Swedish Krona Futures"],nl_NL:["Swedish Krona Futures"]},e.exports["#CME:QT1!-symbol-description"]={en:["Chinese Renminbi / U.S. Dollar Futures"],nl_NL:["Chinese Renminbi / U.S. Dollar Futures"]},e.exports["#COMEX:AUP1!-symbol-description"]={en:["Aluminum MW U.S. Transaction Premium Platts (25MT) Futures"],nl_NL:["Aluminum MW U.S. Transaction Premium Platts (25MT) Futures"]},e.exports["#CME:L61!-symbol-description"]={en:["Brazilian Real Futures"],nl_NL:["Brazilian Real Futures"]},e.exports["#CME:WP1!-symbol-description"]={en:["Polish Zloty Futures"],nl_NL:["Polish Zloty Futures"]},e.exports["#CME:N61!-symbol-description"]={en:["New Zealand Dollar Futures"],nl_NL:["New Zealand Dollar Futures"]},e.exports["#CME_MINI:MG1!-symbol-description"]={en:["E-micro Australian Dollar / U.S. Dollar Futures"],nl_NL:["E-micro Australian Dollar / U.S. Dollar Futures"]},e.exports["#CME_MINI:WN1!-symbol-description"]={en:["E-micro Swiss Franc / U.S. Dollar Futures"],nl_NL:["E-micro Swiss Franc / U.S. Dollar Futures"]},e.exports["#CME_MINI:MF1!-symbol-description"]={en:["E-micro Euro / U.S. Dollar Futures"],nl_NL:["E-micro Euro / U.S. Dollar Futures"]},e.exports["#CME_MINI:E71!-symbol-description"]={en:["Euro E-mini Futures"],nl_NL:["Euro E-mini Futures"]},e.exports["#CBOT:ZK1!-symbol-description"]={en:["Denatured Fuel Ethanol Futures"],nl_NL:["Denatured Fuel Ethanol Futures"]},e.exports["#CME_MINI:MB1!-symbol-description"]={en:["E-micro British Pound / U.S. Dollar Futures"],nl_NL:["E-micro British Pound / U.S. Dollar Futures"]},e.exports["#NYMEX_MINI:QU1!-symbol-description"]={en:["E-mini Gasoline Futures"],nl_NL:["E-mini Gasoline Futures"]},e.exports["#NYMEX_MINI:QX1!-symbol-description"]={en:["E-mini Heating Oil Futures"],nl_NL:["E-mini Heating Oil Futures"]},e.exports["#COMEX_MINI:QC1!-symbol-description"]={en:["E-mini Copper Futures"],nl_NL:["E-mini Copper Futures"]},e.exports["#NYMEX_MINI:QG1!-symbol-description"]={en:["E-mini Natural Gas Futures"],nl_NL:["E-mini Natural Gas Futures"]},e.exports["#CME:E41!-symbol-description"]={en:["U.S. Dollar / Turkish Lira Futures"],nl_NL:["U.S. Dollar / Turkish Lira Futures"]},e.exports["#COMEX_MINI:QI1!-symbol-description"]={en:["Silver (Mini) Futures"],nl_NL:["Silver (Mini) Futures"]},e.exports["#CME:DL1!-symbol-description"]={en:["Milk, Class III Futures"],nl_NL:["Milk, Class III Futures"]},e.exports["#NYMEX:UX1!-symbol-description"]={en:["Uranium Futures"],nl_NL:["Uranium Futures"]},e.exports["#CBOT:BO1!-symbol-description"]={en:["Soybean Oil Futures"],nl_NL:["Soybean Oil Futures"]},e.exports["#CME:HE1!-symbol-description"]={
en:["Lean Hogs Futures"],nl_NL:["Lean Hogs Futures"]},e.exports["#NYMEX:IAC1!-symbol-description"]={en:["Newcastle Coal Futures"],nl_NL:["Newcastle Coal Futures"]},e.exports["#NYMEX_MINI:QM1!-symbol-description"]={en:["E-mini Light Crude Oil Futures"],nl_NL:["E-mini Light Crude Oil Futures"]},e.exports["#NYMEX:JMJ1!-symbol-description"]={en:["Mini Brent Financial Futures"],nl_NL:["Mini Brent Financial Futures"]},e.exports["#COMEX:AEP1!-symbol-description"]={en:["Aluminium European Premium Futures"],nl_NL:["Aluminium European Premium Futures"]},e.exports["#CBOT:ZQ1!-symbol-description"]={en:["30 Day Federal Funds Interest Rate Futures"],nl_NL:["30 Day Federal Funds Interest Rate Futures"]},e.exports["#CME:LE1!-symbol-description"]={en:["Live Cattle Futures"],nl_NL:["Live Cattle Futures"]},e.exports["#CME:UP1!-symbol-description"]={en:["Swiss Franc / Japanese Yen Futures"],nl_NL:["Swiss Franc / Japanese Yen Futures"]},e.exports["#CBOT:ZN1!-symbol-description"]={en:["10 Year T-Note Futures"],nl_NL:["10 Year T-Note Futures"]},e.exports["#CBOT:ZB1!-symbol-description"]={en:["T-Bond Futures"],nl_NL:["T-Bond Futures"]},e.exports["#CME:GF1!-symbol-description"]={en:["Feeder Cattle Futures"],nl_NL:["Feeder Cattle Futures"]},e.exports["#CBOT:UD1!-symbol-description"]={en:["Ultra T-Bond Futures"],nl_NL:["Ultra T-Bond Futures"]},e.exports["#CME:I91!-symbol-description"]={en:["CME Housing Futures — Washington DC"],nl_NL:["CME Housing Futures — Washington DC"]},e.exports["#CBOT:ZO1!-symbol-description"]={en:["Oat Futures"],nl_NL:["Oat Futures"]},e.exports["#CBOT:ZM1!-symbol-description"]={en:["Soybean Meal Futures"],nl_NL:["Soybean Meal Futures"]},e.exports["#CBOT_MINI:XN1!-symbol-description"]={en:["Corn Mini Futures"],nl_NL:["Corn Mini Futures"]},e.exports["#CBOT:ZC1!-symbol-description"]={en:["Corn Futures"],nl_NL:["Corn Futures"]},e.exports["#CME:LS1!-symbol-description"]={en:["Lumber Futures"],nl_NL:["Lumber Futures"]},e.exports["#CBOT_MINI:XW1!-symbol-description"]={en:["Wheat Mini Futures"],nl_NL:["Wheat Mini Futures"]},e.exports["#CBOT_MINI:XK1!-symbol-description"]={en:["Soybean Mini Futures"],nl_NL:["Soybean Mini Futures"]},e.exports["#CBOT:ZS1!-symbol-description"]={en:["Soybean Futures"],nl_NL:["Soybean Futures"]},e.exports["#NYMEX:PA1!-symbol-description"]={en:["Palladium Futures"],nl_NL:["Palladium Futures"]},e.exports["#CME:FTU1!-symbol-description"]={en:["E-mini FTSE 100 Index USD Futures"],nl_NL:["E-mini FTSE 100 Index USD Futures"]},e.exports["#CBOT:ZR1!-symbol-description"]={en:["Rice Futures"],nl_NL:["Rice Futures"]},e.exports["#COMEX_MINI:GR1!-symbol-description"]={en:["Gold (E-micro) Futures"],nl_NL:["Gold (E-micro) Futures"]},e.exports["#COMEX_MINI:QO1!-symbol-description"]={en:["Gold (Mini) Futures"],nl_NL:["Gold (Mini) Futures"]},e.exports["#CME_MINI:RL1!-symbol-description"]={en:["E-mini Russell 1000 Futures"],nl_NL:["E-mini Russell 1000 Futures"]},e.exports["#CME_MINI:EW1!-symbol-description"]={en:["S&P 400 Midcap E-mini Futures"],nl_NL:["S&P 400 Midcap E-mini Futures"]},
e.exports["#COMEX:LD1!-symbol-description"]={en:["Lead Futures"],nl_NL:["Lead Futures"]},e.exports["#CME_MINI:ES1!-symbol-description"]={en:["S&P 500 E-mini Futures"],nl_NL:["S&P 500 E-mini Futures"]},e.exports["#TVC:SA40-symbol-description"]={en:["South Africa Top 40 Index"],nl_NL:["South Africa Top 40 Index"]},e.exports["#BMV:ME-symbol-description"]={en:["S&P/BMV IPC Index"],nl_NL:["IPC Mexico Index"]},e.exports["#BCBA:IMV-symbol-description"]={en:["S&P MERVAL Index"],nl_NL:["MERVAL Index"]},e.exports["#HSI:HSI-symbol-description"]={en:["Hang Seng Index"],nl_NL:["Hang Seng Index"]},e.exports["#BVL:SPBLPGPT-symbol-description"]={en:["S&P / BVL Peru General Index (PEN)"],nl_NL:["S&P / BVL Peru General Index (PEN)"]},e.exports["#EGX:EGX30-symbol-description"]={en:["EGX 30 Index"],nl_NL:["EGX 30 Price Return Index"]},e.exports["#BVC:IGBC-symbol-description"]={en:["Indice General de la Bolsa de Valores de Colombia"],nl_NL:["Indice General de la Bolsa de Valores de Colombia"]},e.exports["#TWSE:TAIEX-symbol-description"]={en:["Taiwan Capitalization Weighted Stock Index"],nl_NL:["Taiwan Capitalization Weighted Stock Index"]},e.exports["#QSE:GNRI-symbol-description"]={en:["QE Index"],nl_NL:["QE Index"]},e.exports["#BME:IBC-symbol-description"]={en:["IBEX 35 Index"],nl_NL:["IBEX 35 Index"]},e.exports["#NZX:NZ50G-symbol-description"]={en:["S&P / NZX 50 Index Gross"],nl_NL:["S&P / NZX 50 Index Gross"]},e.exports["#SIX:SMI-symbol-description"]={en:["Swiss Market Index"],nl_NL:["Swiss Market Index"]},e.exports["#SZSE:399001-symbol-description"]={en:["Shenzhen Component Index"],nl_NL:["SZSE Component Index"]},e.exports["#TADAWUL:TASI-symbol-description"]={en:["Tadawul All Shares Index"],nl_NL:["Tadawul All Shares Index"]},e.exports["#IDX:COMPOSITE-symbol-description"]={en:["IDX Composite Index"],nl_NL:["IDX Composite Index"]},e.exports["#EURONEXT:PX1-symbol-description"]={en:["CAC 40 Index"],nl_NL:["CAC 40 Index"]},e.exports["#OMXHEX:OMXH25-symbol-description"]={en:["OMX Helsinki 25 Index"],nl_NL:["OMX Helsinki 25 Index"]},e.exports["#EURONEXT:BEL20-symbol-description"]={en:["BEL 20 Index"],nl_NL:["BEL 20 Index"]},e.exports["#TVC:STI-symbol-description"]={en:["Straits Times Index"],nl_NL:["Straits Times Index"]},e.exports["#DFM:DFMGI-symbol-description"]={en:["DFM Index"],nl_NL:["DFM Index"]},e.exports["#TVC:KOSPI-symbol-description"]={en:["Korea Composite Stock Price Index"],nl_NL:["Korea Composite Stock Price Index"]},e.exports["#FTSEMYX:FBMKLCI-symbol-description"]={en:["FTSE Bursa Malaysia KLCI Index"],nl_NL:["FTSE Bursa Malaysia KLCI Index"]},e.exports["#TASE:TA35-symbol-description"]={en:["TA-35 Index"],nl_NL:["TA-35 Index"]},e.exports["#OMXSTO:OMXS30-symbol-description"]={en:["OMX Stockholm 30 Index"],nl_NL:["OMX Stockholm 30 Index"]},e.exports["#OMXICE:OMXI8-symbol-description"]={en:["OMX Iceland 8 Index"],nl_NL:["OMX Iceland 8 Index"]},e.exports["#NSENG:NSE30-symbol-description"]={en:["NSE 30 Index"],nl_NL:["NSE 30 Index"]},e.exports["#BAHRAIN:BSEX-symbol-description"]={en:["Bahrain All Share Index"],
nl_NL:["Bahrain All Share Index"]},e.exports["#OMXTSE:OMXTGI-symbol-description"]={en:["OMX Tallinn Gross Index"],nl_NL:["OMX Tallinn GI"]},e.exports["#OMXCOP:OMXC25-symbol-description"]={en:["OMX Copenhagen 25 Index"],nl_NL:["OMX Copenhagen 25 Index"]},e.exports["#OMXRSE:OMXRGI-symbol-description"]={en:["OMX Riga Gross Index"],nl_NL:["OMX Riga GI"]},e.exports["#BELEX:BELEX15-symbol-description"]={en:["BELEX 15 Index"],nl_NL:["BELEX 15 Index"]},e.exports["#OMXVSE:OMXVGI-symbol-description"]={en:["OMX Vilnius Gross Index"],nl_NL:["OMX Vilnius GI"]},e.exports["#EURONEXT:AEX-symbol-description"]={en:["AEX Index"],nl_NL:["AEX Index"]},e.exports["#CBOE:VIX-symbol-description"]={en:["Volatility S&P 500 Index"],nl_NL:["Volatility S&P 500 Index"]},e.exports["#NASDAQ:XAU-symbol-description"]={en:["PHLX Gold and Silver Sector Index"],nl_NL:["PHLX Gold and Silver Sector Index"]},e.exports["#DJ:DJUSCL-symbol-description"]={en:["Dow Jones U.S. Coal Index"],nl_NL:["Dow Jones U.S. Coal Index"]},e.exports["#DJ:DJCIKC-symbol-description"]={en:["Dow Jones Commodity Index Coffee"],nl_NL:["Dow Jones Commodity Index Coffee"]},e.exports["#DJ:DJCIEN-symbol-description"]={en:["Dow Jones Commodity Index Energy"],nl_NL:["Dow Jones Commodity Index Energy"]},e.exports["#NASDAQ:OSX-symbol-description"]={en:["PHLX Oil Service Sector Index"],nl_NL:["PHLX Oil Service Sector Index"]},e.exports["#DJ:DJCISB-symbol-description"]={en:["Dow Jones Commodity Index Sugar"],nl_NL:["Dow Jones Commodity Index Sugar"]},e.exports["#DJ:DJCICC-symbol-description"]={en:["Dow Jones Commodity Index Cocoa"],nl_NL:["Dow Jones Commodity Index Cocoa"]},e.exports["#DJ:DJCIGR-symbol-description"]={en:["Dow Jones Commodity Index Grains"],nl_NL:["Dow Jones Commodity Index Grains"]},e.exports["#DJ:DJCIAGC-symbol-description"]={en:["Dow Jones Commodity Index Agriculture Capped Component"],nl_NL:["Dow Jones Commodity Index Agriculture Capped Component"]},e.exports["#DJ:DJCISI-symbol-description"]={en:["Dow Jones Commodity Index Silver"],nl_NL:["Dow Jones Commodity Index Silver"]},e.exports["#DJ:DJCIIK-symbol-description"]={en:["Dow Jones Commodity Index Nickel"],nl_NL:["Dow Jones Commodity Index Nickel"]},e.exports["#NASDAQ:HGX-symbol-description"]={en:["PHLX Housing Sector Index"],nl_NL:["PHLX Housing Sector Index"]},e.exports["#DJ:DJCIGC-symbol-description"]={en:["Dow Jones Commodity Index Gold"],nl_NL:["Dow Jones Commodity Index Gold"]},e.exports["#SP:SPGSCI-symbol-description"]={en:["S&P Goldman Sachs Commodity Index"],nl_NL:["S&P Goldman Sachs Commodity Index"]},e.exports["#NASDAQ:UTY-symbol-description"]={en:["PHLX Utility Sector Index"],nl_NL:["PHLX Utility Sector Index"]},e.exports["#DJ:DJU-symbol-description"]={en:["Dow Jones Utility Average Index"],nl_NL:["Dow Jones Utility Average Index"]},e.exports["#SP:SVX-symbol-description"]={en:["S&P 500 Value Index"],nl_NL:["S&P 500 Value Index"]},e.exports["#SP:OEX-symbol-description"]={en:["S&P 100 Index"],nl_NL:["S&P 100 Index"]},e.exports["#CBOE:OEX-symbol-description"]={en:["S&P 100 Index"],nl_NL:["S&P 100 Index"]},
e.exports["#NASDAQ:SOX-symbol-description"]={en:["Philadelphia Semiconductor Index"],nl_NL:["Philadelphia Semiconductor Index"]},e.exports["#RUSSELL:RUI-symbol-description"]={en:["Russell 1000 Index"],nl_NL:["Russell 1000 Index"]},e.exports["#RUSSELL:RUA-symbol-description"]={en:["Russell 3000 Index"],nl_NL:["Russell 3000 Index"]},e.exports["#RUSSELL:RUT-symbol-description"]={en:["Russell 2000 Index"],nl_NL:["Russell 2000 Index"]},e.exports["#NYSE:XMI-symbol-description"]={en:["NYSE ARCA Major Market Index"],nl_NL:["NYSE ARCA Major Market Index"]},e.exports["#NYSE:XAX-symbol-description"]={en:["AMEX Composite Index"],nl_NL:["AMEX Composite Index"]},e.exports["#NASDAQ:NDX-symbol-description"]={en:["Nasdaq 100 Index"],nl_NL:["Nasdaq 100 Index"]},e.exports["#NASDAQ:IXIC-symbol-description"]={en:["Nasdaq Composite Index"],nl_NL:["Nasdaq Composite Index"]},e.exports["#DJ:DJT-symbol-description"]={en:["Dow Jones Transportation Average Index"],nl_NL:["Dow Jones Transportation Average Index"]},e.exports["#NYSE:NYA-symbol-description"]={en:["NYSE Composite Index"],nl_NL:["NYSE Composite Index"]},e.exports["#NYMEX:CJ1!-symbol-description"]={en:["Cocoa Futures"],nl_NL:["Cocoa Futures"]},e.exports["#USDILS-symbol-description"]={en:["U.S. Dollar / Israeli Shekel"],nl_NL:["U.S. Dollar / Israeli Shekel"]},e.exports["#TSXV:F-symbol-description"]={en:["Fiore Gold Inc"],nl_NL:["Fiore Gold Inc"]},e.exports["#SIX:F-symbol-description"]={en:["Ford Motor Company"],nl_NL:["Ford Motor Company"]},e.exports["#BMV:F-symbol-description"]={en:["Ford Motor Company"],nl_NL:["Ford Motor Company"]},e.exports["#TWII-symbol-description"]={en:["Taiwan Weighted Index"],nl_NL:["Taiwan Weighted Index"]},e.exports["#TVC:PL10Y-symbol-description"]={en:["Poland Government Bonds 10 YR Yield"],nl_NL:["Poland Government Bonds 10 YR Yield"]},e.exports["#TVC:PL05Y-symbol-description"]={en:["Poland Government Bonds 5 YR Yield"],nl_NL:["Poland Government Bonds 5 YR Yield"]},e.exports["#SET:GC-symbol-description"]={en:["Global Connections Public Company"],nl_NL:["Global Connections Public Company"]},e.exports["#TSX:GC-symbol-description"]={en:["Great Canadian Gaming Corporation"],nl_NL:["Great Canadian Gaming Corporation"]},e.exports["#TVC:FTMIB-symbol-description"]={en:["Milano Italia Borsa Index"],nl_NL:["Milano Italia Borsa Index"]},e.exports["#OANDA:SPX500USD-symbol-description"]={en:["S&P 500 Index"],nl_NL:["S&P 500 Index"]},e.exports["#BMV:CT-symbol-description"]={en:["China SX20 RT"],nl_NL:["China SX20 RT"]},e.exports["#TSXV:CT-symbol-description"]={en:["Centenera Mining Corporation"],nl_NL:["Centenera Mining Corporation"]},e.exports["#BYBIT:ETHUSD-symbol-description"]={en:["ETHUSD Perpetual Contract"],nl_NL:["ETHUSD Perpetual Contract"]},e.exports["#BYBIT:XRPUSD-symbol-description"]={en:["XRPUSD Perpetual Contract"],nl_NL:["XRPUSD Perpetual Contract"]},e.exports["#BYBIT:BTCUSD-symbol-description"]={en:["BTCUSD Perpetual Contract"],nl_NL:["BTCUSD Perpetual Contract"]},e.exports["#BITMEX:ETHUSD-symbol-description"]={en:["ETHUSD Perpetual Futures Contract"],
nl_NL:["ETHUSD Perpetual Futures Contract"]},e.exports["#DERIBIT:BTCUSD-symbol-description"]={en:["BTCUSD Perpetual Futures Contract"],nl_NL:["BTCUSD Perpetual Futures Contract"]},e.exports["#DERIBIT:ETHUSD-symbol-description"]={en:["ETHUSD Perpetual Futures Contract"],nl_NL:["ETHUSD Perpetual Futures Contract"]},e.exports["#USDHUF-symbol-description"]={en:["U.S. Dollar / Hungarian Forint"],nl_NL:["U.S. Dollar / Hungarian Forint"]},e.exports["#USDTHB-symbol-description"]={en:["U.S. Dollar / Thai Baht"],nl_NL:["U.S. Dollar / Thai Baht"]},e.exports["#FOREXCOM:US2000-symbol-description"]={en:["US Small Cap 2000"],nl_NL:["US Small Cap 2000"]},e.exports["#TSXV:PBR-symbol-description"]={en:["Para Resources Inc"],nl_NL:["Para Resources Inc"]},e.exports["#NYSE:SI-symbol-description"]={en:["Silvergate Capital Corporation"],nl_NL:["Silvergate Capital Corporation"]},e.exports["#NASDAQ:LE-symbol-description"]={en:["Lands' End Inc"],nl_NL:["Lands' End Inc"]},e.exports["#CME:CB1!-symbol-description"]={en:["Butter Futures-Cash (Continuous: Current contract in front)"],nl_NL:["Butter Futures-Cash (Continuous: Current contract in front)"]},e.exports["#LSE:SCHO-symbol-description"]={en:["Scholium Group Plc Ord 1P"],nl_NL:["Scholium Group Plc Ord 1P"]},e.exports["#NEO:HE-symbol-description"]={en:["Hanwei Energy Services Corp."],nl_NL:["Hanwei Energy Services Corp."]},e.exports["#NYSE:HE-symbol-description"]={en:["Hawaiian Electric Industries"],nl_NL:["Hawaiian Electric Industries"]},e.exports["#OMXCOP:SCHO-symbol-description"]={en:["Schouw & Co A/S"],nl_NL:["Schouw & Co A/S"]},e.exports["#TSX:HE-symbol-description"]={en:["Hanwei Energy Services Corp."],nl_NL:["Hanwei Energy Services Corp."]},e.exports["#BSE:ITI-symbol-description"]={en:["ITI Ltd"],nl_NL:["ITI Ltd"]},e.exports["#NSE:ITI-symbol-description"]={en:["Indian Telephone Industries Limited"],nl_NL:["Indian Telephone Industries Limited"]},e.exports["#TSX:LS-symbol-description"]={en:["Middlefield Healthcare & Life Sciences Dividend Fund"],nl_NL:["Middlefield Healthcare & Life Sciences Dividend Fund"]},e.exports["#BITMEX:XBT-symbol-description"]={en:["Bitcoin / U.S. Dollar Index"],nl_NL:["Bitcoin / U.S. Dollar Index"]},e.exports["#CME_MINI:RTY1!-symbol-description"]={en:["E-Mini Russell 2000 Index Futures"],nl_NL:["E-Mini Russell 2000 Index Futures"]},e.exports["#CRYPTOCAP:TOTAL-symbol-description"]={en:["Crypto Total Market Cap, $"],nl_NL:["Crypto Total Market Cap, $"]},e.exports["#ICEUS:DX1!-symbol-description"]={en:["U.S. Dollar Index Futures"],nl_NL:["U.S. Dollar Index Futures"]},e.exports["#NYMEX:TT1!-symbol-description"]={en:["Cotton Futures"],nl_NL:["Cotton Futures"]},e.exports["#PHEMEX:BTCUSD-symbol-description"]={en:["BTC Perpetual Futures Contract"],nl_NL:["BTC Perpetual Futures Contract"]},e.exports["#PHEMEX:ETHUSD-symbol-description"]={en:["ETH Perpetual Futures Contract"],nl_NL:["ETH Perpetual Futures Contract"]},e.exports["#PHEMEX:XRPUSD-symbol-description"]={en:["XRP Perpetual Futures Contract"],nl_NL:["XRP Perpetual Futures Contract"]},
e.exports["#PHEMEX:LTCUSD-symbol-description"]={en:["LTC Perpetual Futures Contract"],nl_NL:["LTC Perpetual Futures Contract"]},e.exports["#BITCOKE:BCHUSD-symbol-description"]={en:["BCH Quanto Swap"],nl_NL:["BCH Quanto Swap"]},e.exports["#BITCOKE:BTCUSD-symbol-description"]={en:["BTC Quanto Swap"],nl_NL:["BTC Quanto Swap"]},e.exports["#BITCOKE:ETHUSD-symbol-description"]={en:["ETH Quanto Swap"],nl_NL:["ETH Quanto Swap"]},e.exports["#BITCOKE:LTCUSD-symbol-description"]={en:["LTC Quanto Swap"],nl_NL:["LTC Quanto Swap"]},e.exports["#TVC:CA10-symbol-description"]={en:["Canadian Government Bonds, 10 YR"],nl_NL:["Canadian Government Bonds, 10 YR"]},e.exports["#TVC:CA10Y-symbol-description"]={en:["Canadian Government Bonds 10 YR Yield"],nl_NL:["Canadian Government Bonds 10 YR Yield"]},e.exports["#TVC:ID10Y-symbol-description"]={en:["Indonesia Government Bonds 10 YR Yield"],nl_NL:["Indonesia Government Bonds 10 YR Yield"]},e.exports["#TVC:NL10-symbol-description"]={en:["Netherlands Government Bonds, 10 YR"],nl_NL:["Netherlands Government Bonds, 10 YR"]},e.exports["#TVC:NL10Y-symbol-description"]={en:["Netherlands Government Bonds 10 YR Yield"],nl_NL:["Netherlands Government Bonds 10 YR Yield"]},e.exports["#TVC:NZ10-symbol-description"]={en:["New Zealand Government Bonds, 10 YR"],nl_NL:["New Zealand Government Bonds, 10 YR"]},e.exports["#TVC:NZ10Y-symbol-description"]={en:["New Zealand Government Bonds 10 YR Yield"],nl_NL:["New Zealand Government Bonds 10 YR Yield"]},e.exports["#SOLUSD-symbol-description"]={en:["Solana / U.S. Dollar"],nl_NL:["Solana / U.S. Dollar"]},e.exports["#LUNAUSD-symbol-description"]={en:["Luna / U.S. Dollar"],nl_NL:["Luna / U.S. Dollar"]},e.exports["#UNIUSD-symbol-description"]={en:["Uniswap / U.S. Dollar"],nl_NL:["Uniswap / U.S. Dollar"]},e.exports["#LTCBRL-symbol-description"]={en:["Litecoin / Brazilian Real"],nl_NL:["Litecoin / Brazilian Real"]},e.exports["#ETCEUR-symbol-description"]={en:["Ethereum Classic / Euro"],nl_NL:["Ethereum Classic / Euro"]},e.exports["#ETHKRW-symbol-description"]={en:["Ethereum / South Korean Won"],nl_NL:["Ethereum / South Korean Won"]},e.exports["#BTCRUB-symbol-description"]={en:["Bitcoin / Russian Ruble"],nl_NL:["Bitcoin / Russian Ruble"]},e.exports["#BTCTHB-symbol-description"]={en:["Bitcoin / Thai Baht"],nl_NL:["Bitcoin / Thai Baht"]},e.exports["#ETHTHB-symbol-description"]={en:["Ethereum / Thai Baht"],nl_NL:["Ethereum / Thai Baht"]},e.exports["#TVC:EU10YY-symbol-description"]={en:["Euro Government Bonds 10 YR Yield"],nl_NL:["Euro Government Bonds 10 YR Yield"]},e.exports["#NASDAQ:LCID-symbol-description"]={en:["Lucid Group, Inc."],nl_NL:["Lucid Group, Inc."]},e.exports["#TADAWUL:2370-symbol-description"]={en:["Middle East Specialized Cables Co."],nl_NL:["Middle East Specialized Cables Co."]},e.exports["#NASDAQ:GOOG-symbol-description"]={en:["#NASDAQ:GOOG-symbol-description"],nl_NL:["#NASDAQ:GOOG-symbol-description"]},e.exports["#NASDAQ:GOOGL-symbol-description"]={en:["#NASDAQ:GOOGL-symbol-description"],nl_NL:["#NASDAQ:GOOGL-symbol-description"]}}}]);