(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[2364],{48473:e=>{e.exports={en:["Real-time data for {symbolName}"],nl_NL:["Real-time data for {symbolName}"]}},84455:e=>{e.exports={en:["is provided by {exchange} exchange."],nl_NL:["is provided by {exchange} exchange."]}},97349:e=>{e.exports={en:["Fr"],nl_NL:["Fr"]}},30961:e=>{e.exports={en:["Mo"],nl_NL:["Mo"]}},94748:e=>{e.exports={en:["Sa"],nl_NL:["Sa"]}},75005:e=>{e.exports={en:["Su"],nl_NL:["Su"]}},92578:e=>{e.exports={en:["We"],nl_NL:["We"]}},8765:e=>{e.exports={en:["Th"],nl_NL:["Th"]}},9135:e=>{e.exports={en:["Tu"],nl_NL:["Tu"]}},43206:e=>{e.exports={en:["Could not get Pine source code."],nl_NL:["Could not get Pine source code."]}},65495:e=>{e.exports={en:["Collapse pane"],nl_NL:["Collapse pane"]}},81605:e=>{e.exports={en:["Confirm Remove Study Tree"],nl_NL:["Confirm Remove Study Tree"]}},40225:e=>{e.exports={en:["Continuous futures contracts"],nl_NL:["Continuous futures contracts"]}},78162:e=>{e.exports={en:["Continuous futures contracts are synthetic instruments combining individual contracts. The 1! contract represents the front-month (nearest expiration) while the 2! represents the second nearest expiration."],nl_NL:["Continuous futures contracts are synthetic instruments combining individual contracts. The 1! contract represents the front-month (nearest expiration) while the 2! represents the second nearest expiration."]}},58796:e=>{e.exports={en:["Cboe One"],nl_NL:["Cboe One"]}},29151:e=>{e.exports={en:["Change description"],nl_NL:["Change description"]}},63245:e=>{e.exports={en:["Change symbol"],nl_NL:["Change symbol"]}},45639:e=>{e.exports={en:["Chart values"],nl_NL:["Chart values"]}},28214:e=>{e.exports={en:["Create a free account"],nl_NL:["Create a free account"]}},53357:e=>{e.exports={en:["All's well — Market is open."],nl_NL:["All's well — Market is open."]}},28896:e=>{e.exports={en:["April"],nl_NL:["April"]}},11081:e=>{e.exports={en:["August"],nl_NL:["August"]}},10842:e=>{e.exports={en:["Bar change values"],nl_NL:["Bar Change Values"]}},70032:e=>{e.exports={en:["Buy real-time data"],nl_NL:["Buy real-time data"]}},54480:e=>{e.exports={en:["Go to Editor"],nl_NL:["Go to Editor"]}},77174:e=>{e.exports={en:["Do you really want to delete study and all of it's children?"],nl_NL:["Do you really want to delete study and all of it's children?"]}},13930:e=>{e.exports={en:["Double click"],nl_NL:["Double click"]}},78992:e=>{e.exports={en:["Data error"],nl_NL:["Data error"]}},32925:e=>{e.exports={en:["Data is updated once a day."],nl_NL:["Data is updated once a day."]}},33039:e=>{e.exports={en:["Data is updated once per second, even if there are more updates on the market."],nl_NL:["Data is updated once per second, even if there are more updates on the market."]}},43348:e=>{e.exports={en:["Data is delayed"],nl_NL:["Data is delayed"]}},38368:e=>{e.exports={en:["Data on our Basic plan is updated once per second, even if there are more updates on the market."],
nl_NL:["Data on our Basic plan is updated once per second, even if there are more updates on the market."]}},90082:e=>{e.exports={en:["December"],nl_NL:["December"]}},66260:e=>{e.exports={en:["Delete pane"],nl_NL:["Delete pane"]}},54602:e=>{e.exports={en:["Delisted"],nl_NL:["Delisted"]}},31683:e=>{e.exports={en:["Delisted alert"],nl_NL:["Delisted alert"]}},50035:e=>{e.exports={en:["Derived Data"],nl_NL:["Derived Data"]}},45321:e=>{e.exports={en:["Derived Data refers to financial indicators which are created by combining and/or processing raw data supplied from various sources."],nl_NL:["Derived Data refers to financial indicators which are created by combining and/or processing raw data supplied from various sources."]}},5805:e=>{e.exports={en:["End of day data"],nl_NL:["End of day data"]}},57335:e=>{e.exports={en:["Error"],nl_NL:["Fout"]}},23302:e=>{e.exports={en:["Evening. Market is open for post-market trading."],nl_NL:["Evening. Market is open for post-market trading."]}},63538:e=>{e.exports={en:["Exchange timezone"],nl_NL:["Exchange timezone"]}},81069:e=>{e.exports={en:["February"],nl_NL:["February"]}},5447:e=>{e.exports={en:["Fill out Exchange Agreements"],nl_NL:["Fill out Exchange Agreements"]}},44454:e=>{e.exports={en:["Flag Symbol"],nl_NL:["Flag Symbol"]}},22928:e=>{e.exports={en:["Fri"],nl_NL:["Fri"]}},3570:e=>{e.exports={en:["Friday"],nl_NL:["Friday"]}},87845:e=>{e.exports={en:["Holiday"],nl_NL:["Holiday"]}},75119:e=>{e.exports={en:["Halal symbol"],nl_NL:["Halal symbol"]}},44036:e=>{e.exports={en:["Indicator arguments"],nl_NL:["Indicator Arguments"]}},7511:e=>{e.exports={en:["Indicator titles"],nl_NL:["Indicator Titles"]}},51353:e=>{e.exports={en:["Indicator values"],nl_NL:["Indicator Values"]}},95400:e=>{e.exports={en:["If you'd like {listedExchange} real-time data you'll need to complete an Exchange Agreement. Don't worry, it only takes a few clicks"],nl_NL:["If you'd like {listedExchange} real-time data you'll need to complete an Exchange Agreement. Don't worry, it only takes a few clicks"]}},81509:e=>{e.exports={en:["It'll go to post-market trading in {remainingTime}."],nl_NL:["It'll go to post-market trading in {remainingTime}."]}},58470:e=>{e.exports={en:["It'll open for pre-market trading in {remainingTime}."],nl_NL:["It'll open for pre-market trading in {remainingTime}."]}},200:e=>{e.exports={en:["January"],nl_NL:["January"]}},6608:e=>{e.exports={en:["July"],nl_NL:["July"]}},61487:e=>{e.exports={en:["June"],nl_NL:["June"]}},91006:e=>{e.exports={en:["One update per second"],nl_NL:["One update per second"]}},37997:e=>{e.exports={en:["October"],nl_NL:["October"]}},25765:e=>{e.exports={en:["Open market status"],nl_NL:["Open market status"]}},54316:e=>{e.exports={en:["Opened in Editor"],nl_NL:["Opened in Editor"]}},28632:e=>{e.exports={en:["Opened in detached Editor"],nl_NL:["Opened in detached Editor"]}},72423:e=>{e.exports={en:["Last day change values"],nl_NL:["Last day change values"]}},27741:e=>{e.exports={en:["Learn more"],nl_NL:["Learn more"]}},74079:e=>{e.exports={en:["Move pane down"],
nl_NL:["Move pane down"]}},7310:e=>{e.exports={en:["Move pane up"],nl_NL:["Move pane up"]}},37150:e=>{e.exports={en:["Mon"],nl_NL:["Mon"]}},19573:e=>{e.exports={en:["Monday"],nl_NL:["Monday"]}},37117:e=>{e.exports={en:["More"],nl_NL:["Meer"]}},65420:e=>{e.exports={en:["Morning. Market is open for pre-market trading."],nl_NL:["Morning. Market is open for pre-market trading."]}},61206:e=>{e.exports={en:["Maximize chart"],nl_NL:["Maximize chart"]}},90165:e=>{e.exports={en:["Maximize pane"],nl_NL:["Maximize pane"]}},25734:e=>{e.exports={en:["May"],nl_NL:["Mei"]}},75018:e=>{e.exports={en:["Manage panes"],nl_NL:["Manage panes"]}},93878:e=>{e.exports={en:["March"],nl_NL:["March"]}},80086:e=>{e.exports={en:["Market open"],nl_NL:["Market open"]}},5371:e=>{e.exports={en:["Market opens in {remainingTime}."],nl_NL:["Market opens in {remainingTime}."]}},62464:e=>{e.exports={en:["Market closed"],nl_NL:["Market closed"]}},18643:e=>{e.exports={en:["Market closes in {remainingTime}."],nl_NL:["Market closes in {remainingTime}."]}},41392:e=>{e.exports={en:["Market is currently on holiday. Lucky them."],nl_NL:["Market is currently on holiday. Lucky them."]}},4607:e=>{e.exports={en:["November"],nl_NL:["November"]}},87142:e=>{e.exports={en:["Source code"],nl_NL:["Source code"]}},32273:e=>{e.exports={en:["Sat"],nl_NL:["Sat"]}},30348:e=>{e.exports={en:["Saturday"],nl_NL:["Saturday"]}},90761:e=>{e.exports={en:["Scroll to the left"],nl_NL:["Scroll to the left"]}},83040:e=>{e.exports={en:["Scroll to the most recent bar"],nl_NL:["Scroll to the most recent bar"]}},25131:e=>{e.exports={en:["Scroll to the right"],nl_NL:["Scroll to the right"]}},32179:e=>{e.exports={en:["September"],nl_NL:["September"]}},85786:e=>{e.exports={en:["Show Object Tree"],nl_NL:["Show Object Tree"]}},74759:e=>{e.exports={en:["Show interval settings"],nl_NL:["Show interval settings"]}},86158:e=>{e.exports={en:["Study Error"],nl_NL:["Study Error"]}},77493:e=>{e.exports={en:["Sun"],nl_NL:["Sun"]}},61480:e=>{e.exports={en:["Sunday"],nl_NL:["Sunday"]}},23079:e=>{e.exports={en:["Symbol price source"],nl_NL:["Symbol price source"]}},14771:e=>{e.exports={en:["Symbol title"],nl_NL:["Symbol title"]}},44138:e=>{e.exports={en:["Synthetic symbol"],nl_NL:["Synthetic symbol"]}},73897:e=>{e.exports={en:["Post-market"],nl_NL:["Post-market"]}},85996:e=>{e.exports={en:["Paid plans feature faster data updates."],nl_NL:["Paid plans feature faster data updates."]}},36018:e=>{e.exports={en:["Pre-market"],nl_NL:["Pre-market"]}},94972:e=>{e.exports={en:["Primary listing"],nl_NL:["Primary listing"]}},20987:e=>{e.exports={en:["Real-time data for this symbol is not supported right now. We may support it in the future."],nl_NL:["Real-time data for this symbol is not supported right now. We may support it in the future."]}},31539:e=>{e.exports={en:["Real-time data for {symbolName} is provided by {exchange} exchange."],nl_NL:["Real-time data for {symbolName} is provided by {exchange} exchange."]}},31142:e=>{e.exports={en:["Restore chart"],nl_NL:["Restore chart"]}},12486:e=>{e.exports={
en:["Restore pane"],nl_NL:["Restore pane"]}},11532:e=>{e.exports={en:["Wed"],nl_NL:["Wed"]}},94226:e=>{e.exports={en:["Wednesday"],nl_NL:["Wednesday"]}},7281:e=>{e.exports={en:["To get real-time data for {description}, please buy the real-time data package."],nl_NL:["To get real-time data for {description}, please buy the real-time data package."]}},71388:e=>{e.exports={en:["Thu"],nl_NL:["Thu"]}},79137:e=>{e.exports={en:["Thursday"],nl_NL:["Thursday"]}},95246:e=>{e.exports={en:["The main, or first, stock exchange where a company's stock is listed and traded."],nl_NL:["The main, or first, stock exchange where a company's stock is listed and traded."]}},25608:e=>{e.exports={en:["The source code of this script version is open in the Pine Editor."],nl_NL:["The source code of this script version is open in the Pine Editor."]}},33161:e=>{e.exports={en:["The source code of this script version is open in the detached Pine Editor."],nl_NL:["The source code of this script version is open in the detached Pine Editor."]}},24669:e=>{e.exports={en:["This data is real-time, but it may be slightly different to its official counterpart coming from primary exchanges."],nl_NL:["This data is real-time, but it may be slightly different to its official counterpart coming from primary exchanges."]}},52668:e=>{e.exports={en:["This data is real-time, but it may be slightly different to its official counterpart coming from {exchange}."],nl_NL:["This data is real-time, but it may be slightly different to its official counterpart coming from {exchange}."]}},67607:e=>{e.exports={en:["This is a shariah-compliant stock, meaning that it follows Islamic law. This company does not charge or receive interest, and does not engage with certain sectors (gambling, alcohol, tobacco, pork products)."],nl_NL:["This is a shariah-compliant stock, meaning that it follows Islamic law. This company does not charge or receive interest, and does not engage with certain sectors (gambling, alcohol, tobacco, pork products)."]}},83556:e=>{e.exports={en:["This is no longer publicly trading so no new data will be added. But you can explore the historicals here."],nl_NL:["This is no longer publicly trading so no new data will be added. But you can explore the historicals here."]}},44492:e=>{e.exports={en:["This real-time data is provided by {originalExchange} exchange. It may be slightly different from the official data directly from {exchange}. If this difference is crucial for you, you'll need to purchase real-time data from the primary exchange, which we can help with."],nl_NL:["This real-time data is provided by {originalExchange} exchange. It may be slightly different from the official data directly from {exchange}. If this difference is crucial for you, you'll need to purchase real-time data from the primary exchange, which we can help with."]}},53272:e=>{e.exports={en:["This symbol doesn't exist, please pick another one."],nl_NL:["This symbol doesn't exist, please pick another one."]}},90589:e=>{e.exports={
en:["This symbol is calculated by TradingView using the rate from other exchanges."],nl_NL:["This symbol is calculated by TradingView using the rate from other exchanges."]}},52176:e=>{e.exports={en:["Time for a walk — this market is closed."],nl_NL:["Time for a walk — this market is closed."]}},11916:e=>{e.exports={en:["Tue"],nl_NL:["Tue"]}},82160:e=>{e.exports={en:["Tuesday"],nl_NL:["Tuesday"]}},13865:e=>{e.exports={en:["Unflag Symbol"],nl_NL:["Unflag Symbol"]}},37644:e=>{e.exports={en:["Volume"],nl_NL:["Volume"]}},97038:e=>{e.exports={en:["Zoom in"],nl_NL:["Inzoomen"]}},88710:e=>{e.exports={en:["Zoom out"],nl_NL:["Uitzoomen"]}},96227:e=>{e.exports={en:["change open market status visibility"],nl_NL:["change open market status visibility"]}},27426:e=>{e.exports={en:["change bar change visibility"],nl_NL:["change bar change visibility"]}},79637:e=>{e.exports={en:["change chart values visibility"],nl_NL:["change chart values visibility"]}},63050:e=>{e.exports={en:["change indicator titles visibility"],nl_NL:["change indicator titles visibility"]}},49583:e=>{e.exports={en:["change indicator values visibility"],nl_NL:["change indicator values visibility"]}},78310:e=>{e.exports={en:["change indicator arguments visibility"],nl_NL:["change indicator arguments visibility"]}},66307:e=>{e.exports={en:["change last day change visibility"],nl_NL:["change last day change visibility"]}},88167:e=>{e.exports={en:["change symbol description visibility"],nl_NL:["change symbol description visibility"]}},12050:e=>{e.exports={en:["change symbol field visibility"],nl_NL:["change symbol field visibility"]}},96201:e=>{e.exports={en:["change volume values visibility"],nl_NL:["change volume values visibility"]}},59938:e=>{e.exports={en:["less than 1 minute"],nl_NL:["less than 1 minute"]}},51382:e=>{e.exports={en:["show {title}"],nl_NL:["show {title}"]}},51320:e=>{e.exports={en:["{days} and {hours}"],nl_NL:["{days} and {hours}"]}},55154:e=>{e.exports={en:["{exchange} by {originalExchange}"],nl_NL:["{exchange} by {originalExchange}"]}},83187:e=>{e.exports={en:["{hours} and {minutes}"],nl_NL:["{hours} and {minutes}"]}},51211:e=>{e.exports={en:["{listedExchange} real-time data is available for free to registered users."],nl_NL:["{listedExchange} real-time data is available for free to registered users."]}},89142:e=>{e.exports={en:["{symbolName} data is delayed by {time} minutes because of exchange requirements."],nl_NL:["{symbolName} data is delayed by {time} minutes because of exchange requirements."]}},51931:e=>{e.exports={en:["Data is updated once every {amount} second, even if there are more updates on the market.","Data is updated once every {amount} seconds, even if there are more updates on the market."],nl_NL:["Data is updated once every {amount} second, even if there are more updates on the market.","Data is updated once every {amount} seconds, even if there are more updates on the market."]}},83978:e=>{e.exports={
en:["Data on our Basic plan is updated once every {amount} second, even if there are more updates on the market.","Data on our Basic plan is updated once every {amount} seconds, even if there are more updates on the market."],nl_NL:["Data on our Basic plan is updated once every {amount} second, even if there are more updates on the market.","Data on our Basic plan is updated once every {amount} seconds, even if there are more updates on the market."]}},46960:e=>{e.exports={en:["Hide indicator legend","Hide indicators legend"],nl_NL:["Hide indicator legend","Hide indicators legend"]}},36050:e=>{e.exports={en:["One update every {amount} second","One update every {amount} seconds"],nl_NL:["One update every {amount} second","One update every {amount} seconds"]}},36553:e=>{e.exports={en:["Show indicator legend","Show indicators legend"],nl_NL:["Show indicator legend","Show indicators legend"]}},39501:e=>{e.exports={en:["{number} day","{number} days"],nl_NL:["{number} day","{number} days"]}},44646:e=>{e.exports={en:["{number} hour","{number} hours"],nl_NL:["{number} hour","{number} hours"]}},32547:e=>{e.exports={en:["{number} minute","{number} minutes"],nl_NL:["{number} minute","{number} minutes"]}}}]);