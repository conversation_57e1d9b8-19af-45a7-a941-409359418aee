(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[6822],{94920:r=>{r.exports={en:["ADJ"],ro:["adj"]}},16755:r=>{r.exports={en:["B-ADJ"],ro:["B-ADJ"]}},82631:r=>{r.exports={en:["SET"],ro:["SET"]}},22233:r=>{r.exports={en:["auto"],ro:["auto"]}},4161:r=>{r.exports={en:["log"],ro:["log"]}},58221:r=>{r.exports={en:["All data"],ro:["All data"]}},19273:r=>{r.exports={en:["Year to day"],ro:["Year to day"]}},58426:r=>{r.exports={en:["{timePeriod} in {timeInterval}"],ro:["{timePeriod} in {timeInterval}"]}},93020:r=>{r.exports={en:["Adjust data for dividends"],ro:["Adjust data for dividends"]}},68921:r=>{r.exports={en:["Adjust for contract changes"],ro:["Adjust for contract changes"]}},42432:r=>{r.exports={en:["Go to"],ro:["Go to"]}},92966:r=>{r.exports={en:["Extended Hours is available only for intraday charts"],ro:["Extended Hours is available only for intraday charts"]}},61206:r=>{r.exports={en:["Maximize chart"],ro:["Maximize chart"]}},2031:r=>{r.exports={en:["Main symbol data is adjusted for dividends only"],ro:["Main symbol data is adjusted for dividends only"]}},95739:r=>{r.exports={en:["Main symbol data is adjusted for splits only"],ro:["Main symbol data is adjusted for splits only"]}},27665:r=>{r.exports={en:["Sessions"],ro:["Sessions"]}},31142:r=>{r.exports={en:["Restore chart"],ro:["Restore chart"]}},41888:r=>{r.exports={en:["Toggle Auto Scale"],ro:["Toggle Auto Scale"]}},1e4:r=>{r.exports={en:["Toggle Log Scale"],ro:["Toggle Log Scale"]}},81649:r=>{r.exports={en:["Toggle Percentage"],ro:["Toggle Percentage"]}},77073:r=>{r.exports={en:["Timezone"],ro:["Timezone"]}},49545:r=>{r.exports={en:["Use settlement as close on daily interval"],ro:["Use settlement as close on daily interval"]}},8586:r=>{r.exports={en:["ext"],ro:["ext"]}},63808:r=>{r.exports={en:["{str} day","{str} days"],ro:["{str} day","{str} days"]}},62368:r=>{r.exports={en:["{str} day","{str} days"],ro:["{str} day","{str} days"]}},561:r=>{r.exports={en:["{str} day intervals","{str} days intervals"],ro:["{str} day intervals","{str} days intervals"]}},72495:r=>{r.exports={en:["{str} hour","{str} hours"],ro:["{str} hour","{str} hours"]}},64963:r=>{r.exports={en:["{str} hour","{str} hours"],ro:["{str} hour","{str} hours"]}},14887:r=>{r.exports={en:["{str} hour intervals","{str} hours intervals"],ro:["{str} hour intervals","{str} hours intervals"]}},12752:r=>{r.exports={en:["{str} month","{str} months"],ro:["{str} month","{str} months"]}},20062:r=>{r.exports={en:["{str} month","{str} months"],ro:["{str} month","{str} months"]}},48514:r=>{r.exports={en:["{str} month intervals","{str} months intervals"],ro:["{str} month intervals","{str} months intervals"]}},95484:r=>{r.exports={en:["{str} minute","{str} minutes"],ro:["{str} minute","{str} minutes"]}},5926:r=>{r.exports={en:["{str} minute","{str} minutes"],ro:["{str} minute","{str} minutes"]}},15489:r=>{r.exports={en:["{str} minute intervals","{str} minutes intervals"],ro:["{str} minute intervals","{str} minutes intervals"]}},6088:r=>{r.exports={en:["{str} week","{str} weeks"],
ro:["{str} week","{str} weeks"]}},49306:r=>{r.exports={en:["{str} week","{str} weeks"],ro:["{str} week","{str} weeks"]}},60316:r=>{r.exports={en:["{str} week intervals","{str} weeks intervals"],ro:["{str} week intervals","{str} weeks intervals"]}},96325:r=>{r.exports={en:["{str} year","{str} years"],ro:["{str} year","{str} years"]}},91549:r=>{r.exports={en:["{str} year","{str} years"],ro:["{str} year","{str} years"]}},78971:r=>{r.exports={en:["{str} year intervals","{str} years intervals"],ro:["{str} year intervals","{str} years intervals"]}}}]);