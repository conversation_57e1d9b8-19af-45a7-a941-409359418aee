(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[2364],{48473:e=>{e.exports={en:["Real-time data for {symbolName}"],ro:["Real-time data for {symbolName}"]}},84455:e=>{e.exports={en:["is provided by {exchange} exchange."],ro:["is provided by {exchange} exchange."]}},97349:e=>{e.exports={en:["Fr"],ro:["Fr"]}},30961:e=>{e.exports={en:["Mo"],ro:["Mo"]}},94748:e=>{e.exports={en:["Sa"],ro:["Sa"]}},75005:e=>{e.exports={en:["Su"],ro:["Su"]}},92578:e=>{e.exports={en:["We"],ro:["We"]}},8765:e=>{e.exports={en:["Th"],ro:["Th"]}},9135:e=>{e.exports={en:["Tu"],ro:["Tu"]}},43206:e=>{e.exports={en:["Could not get Pine source code."],ro:["Could not get Pine source code."]}},65495:e=>{e.exports={en:["Collapse pane"],ro:["Collapse pane"]}},81605:e=>{e.exports={en:["Confirm Remove Study Tree"],ro:["Confirm Remove Study Tree"]}},40225:e=>{e.exports={en:["Continuous futures contracts"],ro:["Continuous futures contracts"]}},78162:e=>{e.exports={en:["Continuous futures contracts are synthetic instruments combining individual contracts. The 1! contract represents the front-month (nearest expiration) while the 2! represents the second nearest expiration."],ro:["Continuous futures contracts are synthetic instruments combining individual contracts. The 1! contract represents the front-month (nearest expiration) while the 2! represents the second nearest expiration."]}},58796:e=>{e.exports={en:["Cboe One"],ro:["Cboe BZX"]}},29151:e=>{e.exports={en:["Change description"],ro:["Change description"]}},63245:e=>{e.exports={en:["Change symbol"],ro:["Change symbol"]}},45639:e=>{e.exports={en:["Chart values"],ro:["Chart values"]}},28214:e=>{e.exports={en:["Create a free account"],ro:["Create a free account"]}},53357:e=>{e.exports={en:["All's well — Market is open."],ro:["All's well — Market is open."]}},28896:e=>{e.exports={en:["April"],ro:["April"]}},11081:e=>{e.exports={en:["August"],ro:["August"]}},10842:e=>{e.exports={en:["Bar change values"],ro:["Bar Change Values"]}},70032:e=>{e.exports={en:["Buy real-time data"],ro:["Buy real-time data"]}},54480:e=>{e.exports={en:["Go to Editor"],ro:["Go to Editor"]}},77174:e=>{e.exports={en:["Do you really want to delete study and all of it's children?"],ro:["Do you really want to delete study and all of it's children?"]}},13930:e=>{e.exports={en:["Double click"],ro:["Double click"]}},78992:e=>{e.exports={en:["Data error"],ro:["Data error"]}},32925:e=>{e.exports={en:["Data is updated once a day."],ro:["Data is updated once a day."]}},33039:e=>{e.exports={en:["Data is updated once per second, even if there are more updates on the market."],ro:["Data is updated once per second, even if there are more updates on the market."]}},43348:e=>{e.exports={en:["Data is delayed"],ro:["Data is delayed"]}},38368:e=>{e.exports={en:["Data on our Basic plan is updated once per second, even if there are more updates on the market."],ro:["Data on our Basic plan is updated once per second, even if there are more updates on the market."]}},90082:e=>{e.exports={en:["December"],ro:["December"]}},66260:e=>{
e.exports={en:["Delete pane"],ro:["Delete pane"]}},54602:e=>{e.exports={en:["Delisted"],ro:["Delisted"]}},31683:e=>{e.exports={en:["Delisted alert"],ro:["Delisted alert"]}},50035:e=>{e.exports={en:["Derived Data"],ro:["Derived Data"]}},45321:e=>{e.exports={en:["Derived Data refers to financial indicators which are created by combining and/or processing raw data supplied from various sources."],ro:["Derived Data refers to financial indicators which are created by combining and/or processing raw data supplied from various sources."]}},5805:e=>{e.exports={en:["End of day data"],ro:["End of day data"]}},57335:e=>{e.exports={en:["Error"],ro:["Error"]}},23302:e=>{e.exports={en:["Evening. Market is open for post-market trading."],ro:["Evening. Market is open for post-market trading."]}},63538:e=>{e.exports={en:["Exchange timezone"],ro:["Exchange timezone"]}},81069:e=>{e.exports={en:["February"],ro:["February"]}},5447:e=>{e.exports={en:["Fill out Exchange Agreements"],ro:["Fill out Exchange Agreements"]}},44454:e=>{e.exports={en:["Flag Symbol"],ro:["Flag Symbol"]}},22928:e=>{e.exports={en:["Fri"],ro:["Fri"]}},3570:e=>{e.exports={en:["Friday"],ro:["Friday"]}},87845:e=>{e.exports={en:["Holiday"],ro:["Holiday"]}},75119:e=>{e.exports={en:["Halal symbol"],ro:["Halal symbol"]}},44036:e=>{e.exports={en:["Indicator arguments"],ro:["Indicator Arguments"]}},7511:e=>{e.exports={en:["Indicator titles"],ro:["Indicator Titles"]}},51353:e=>{e.exports={en:["Indicator values"],ro:["Indicator Values"]}},95400:e=>{e.exports={en:["If you'd like {listedExchange} real-time data you'll need to complete an Exchange Agreement. Don't worry, it only takes a few clicks"],ro:["If you'd like {listedExchange} real-time data you'll need to complete an Exchange Agreement. Don't worry, it only takes a few clicks"]}},81509:e=>{e.exports={en:["It'll go to post-market trading in {remainingTime}."],ro:["It'll go to post-market trading in {remainingTime}."]}},58470:e=>{e.exports={en:["It'll open for pre-market trading in {remainingTime}."],ro:["It'll open for pre-market trading in {remainingTime}."]}},200:e=>{e.exports={en:["January"],ro:["January"]}},6608:e=>{e.exports={en:["July"],ro:["July"]}},61487:e=>{e.exports={en:["June"],ro:["June"]}},91006:e=>{e.exports={en:["One update per second"],ro:["One update per second"]}},37997:e=>{e.exports={en:["October"],ro:["October"]}},25765:e=>{e.exports={en:["Open market status"],ro:["Open market status"]}},54316:e=>{e.exports={en:["Opened in Editor"],ro:["Opened in Editor"]}},28632:e=>{e.exports={en:["Opened in detached Editor"],ro:["Opened in detached Editor"]}},72423:e=>{e.exports={en:["Last day change values"],ro:["Last day change values"]}},27741:e=>{e.exports={en:["Learn more"],ro:["Learn more"]}},74079:e=>{e.exports={en:["Move pane down"],ro:["Move pane down"]}},7310:e=>{e.exports={en:["Move pane up"],ro:["Move pane up"]}},37150:e=>{e.exports={en:["Mon"],ro:["Mon"]}},19573:e=>{e.exports={en:["Monday"],ro:["Monday"]}},37117:e=>{e.exports={en:["More"],ro:["More"]}},65420:e=>{e.exports={
en:["Morning. Market is open for pre-market trading."],ro:["Morning. Market is open for pre-market trading."]}},61206:e=>{e.exports={en:["Maximize chart"],ro:["Maximize chart"]}},90165:e=>{e.exports={en:["Maximize pane"],ro:["Maximize pane"]}},25734:e=>{e.exports={en:["May"],ro:["May"]}},75018:e=>{e.exports={en:["Manage panes"],ro:["Manage panes"]}},93878:e=>{e.exports={en:["March"],ro:["March"]}},80086:e=>{e.exports={en:["Market open"],ro:["Market open"]}},5371:e=>{e.exports={en:["Market opens in {remainingTime}."],ro:["Market opens in {remainingTime}."]}},62464:e=>{e.exports={en:["Market closed"],ro:["Market closed"]}},18643:e=>{e.exports={en:["Market closes in {remainingTime}."],ro:["Market closes in {remainingTime}."]}},41392:e=>{e.exports={en:["Market is currently on holiday. Lucky them."],ro:["Market is currently on holiday. Lucky them."]}},4607:e=>{e.exports={en:["November"],ro:["November"]}},87142:e=>{e.exports={en:["Source code"],ro:["Source code"]}},32273:e=>{e.exports={en:["Sat"],ro:["Sat"]}},30348:e=>{e.exports={en:["Saturday"],ro:["Saturday"]}},90761:e=>{e.exports={en:["Scroll to the left"],ro:["Scroll to the left"]}},83040:e=>{e.exports={en:["Scroll to the most recent bar"],ro:["Scroll to the most recent bar"]}},25131:e=>{e.exports={en:["Scroll to the right"],ro:["Scroll to the right"]}},32179:e=>{e.exports={en:["September"],ro:["September"]}},85786:e=>{e.exports={en:["Show Object Tree"],ro:["Show Object Tree"]}},74759:e=>{e.exports={en:["Show interval settings"],ro:["Show interval settings"]}},86158:e=>{e.exports={en:["Study Error"],ro:["Study Error"]}},77493:e=>{e.exports={en:["Sun"],ro:["Sun"]}},61480:e=>{e.exports={en:["Sunday"],ro:["Sunday"]}},23079:e=>{e.exports={en:["Symbol price source"],ro:["Symbol price source"]}},14771:e=>{e.exports={en:["Symbol title"],ro:["Symbol title"]}},44138:e=>{e.exports={en:["Synthetic symbol"],ro:["Synthetic symbol"]}},73897:e=>{e.exports={en:["Post-market"],ro:["Post-market"]}},85996:e=>{e.exports={en:["Paid plans feature faster data updates."],ro:["Paid plans feature faster data updates."]}},36018:e=>{e.exports={en:["Pre-market"],ro:["Pre-market"]}},94972:e=>{e.exports={en:["Primary listing"],ro:["Primary listing"]}},20987:e=>{e.exports={en:["Real-time data for this symbol is not supported right now. We may support it in the future."],ro:["Real-time data for this symbol is not supported right now. We may support it in the future."]}},31539:e=>{e.exports={en:["Real-time data for {symbolName} is provided by {exchange} exchange."],ro:["Real-time data for {symbolName} is provided by {exchange} exchange."]}},31142:e=>{e.exports={en:["Restore chart"],ro:["Restore chart"]}},12486:e=>{e.exports={en:["Restore pane"],ro:["Restore pane"]}},11532:e=>{e.exports={en:["Wed"],ro:["Wed"]}},94226:e=>{e.exports={en:["Wednesday"],ro:["Wednesday"]}},7281:e=>{e.exports={en:["To get real-time data for {description}, please buy the real-time data package."],ro:["To get real-time data for {description}, please buy the real-time data package."]}},71388:e=>{e.exports={en:["Thu"],ro:["Thu"]}
},79137:e=>{e.exports={en:["Thursday"],ro:["Thursday"]}},95246:e=>{e.exports={en:["The main, or first, stock exchange where a company's stock is listed and traded."],ro:["The main, or first, stock exchange where a company's stock is listed and traded."]}},25608:e=>{e.exports={en:["The source code of this script version is open in the Pine Editor."],ro:["The source code of this script version is open in the Pine Editor."]}},33161:e=>{e.exports={en:["The source code of this script version is open in the detached Pine Editor."],ro:["The source code of this script version is open in the detached Pine Editor."]}},24669:e=>{e.exports={en:["This data is real-time, but it may be slightly different to its official counterpart coming from primary exchanges."],ro:["This data is real-time, but it may be slightly different to its official counterpart coming from primary exchanges."]}},52668:e=>{e.exports={en:["This data is real-time, but it may be slightly different to its official counterpart coming from {exchange}."],ro:["This data is real-time, but it may be slightly different to its official counterpart coming from {exchange}."]}},67607:e=>{e.exports={en:["This is a shariah-compliant stock, meaning that it follows Islamic law. This company does not charge or receive interest, and does not engage with certain sectors (gambling, alcohol, tobacco, pork products)."],ro:["This is a shariah-compliant stock, meaning that it follows Islamic law. This company does not charge or receive interest, and does not engage with certain sectors (gambling, alcohol, tobacco, pork products)."]}},83556:e=>{e.exports={en:["This is no longer publicly trading so no new data will be added. But you can explore the historicals here."],ro:["This is no longer publicly trading so no new data will be added. But you can explore the historicals here."]}},44492:e=>{e.exports={en:["This real-time data is provided by {originalExchange} exchange. It may be slightly different from the official data directly from {exchange}. If this difference is crucial for you, you'll need to purchase real-time data from the primary exchange, which we can help with."],ro:["This real-time data is provided by {originalExchange} exchange. It may be slightly different from the official data directly from {exchange}. If this difference is crucial for you, you'll need to purchase real-time data from the primary exchange, which we can help with."]}},53272:e=>{e.exports={en:["This symbol doesn't exist, please pick another one."],ro:["This symbol doesn't exist, please pick another one."]}},90589:e=>{e.exports={en:["This symbol is calculated by TradingView using the rate from other exchanges."],ro:["This symbol is calculated by TradingView using the rate from other exchanges."]}},52176:e=>{e.exports={en:["Time for a walk — this market is closed."],ro:["Time for a walk — this market is closed."]}},11916:e=>{e.exports={en:["Tue"],ro:["Tue"]}},82160:e=>{e.exports={en:["Tuesday"],ro:["Tuesday"]}},13865:e=>{e.exports={en:["Unflag Symbol"],ro:["Unflag Symbol"]}},37644:e=>{e.exports={
en:["Volume"],ro:["Volume"]}},97038:e=>{e.exports={en:["Zoom in"],ro:["Zoom In"]}},88710:e=>{e.exports={en:["Zoom out"],ro:["Zoom Out"]}},96227:e=>{e.exports={en:["change open market status visibility"],ro:["change open market status visibility"]}},27426:e=>{e.exports={en:["change bar change visibility"],ro:["change bar change visibility"]}},79637:e=>{e.exports={en:["change chart values visibility"],ro:["change chart values visibility"]}},63050:e=>{e.exports={en:["change indicator titles visibility"],ro:["change indicator titles visibility"]}},49583:e=>{e.exports={en:["change indicator values visibility"],ro:["change indicator values visibility"]}},78310:e=>{e.exports={en:["change indicator arguments visibility"],ro:["change indicator arguments visibility"]}},66307:e=>{e.exports={en:["change last day change visibility"],ro:["change last day change visibility"]}},88167:e=>{e.exports={en:["change symbol description visibility"],ro:["change symbol description visibility"]}},12050:e=>{e.exports={en:["change symbol field visibility"],ro:["change symbol field visibility"]}},96201:e=>{e.exports={en:["change volume values visibility"],ro:["change volume values visibility"]}},59938:e=>{e.exports={en:["less than 1 minute"],ro:["less than 1 minute"]}},51382:e=>{e.exports={en:["show {title}"],ro:["show {title}"]}},51320:e=>{e.exports={en:["{days} and {hours}"],ro:["{days} and {hours}"]}},55154:e=>{e.exports={en:["{exchange} by {originalExchange}"],ro:["{exchange} by {originalExchange}"]}},83187:e=>{e.exports={en:["{hours} and {minutes}"],ro:["{hours} and {minutes}"]}},51211:e=>{e.exports={en:["{listedExchange} real-time data is available for free to registered users."],ro:["{listedExchange} real-time data is available for free to registered users."]}},89142:e=>{e.exports={en:["{symbolName} data is delayed by {time} minutes because of exchange requirements."],ro:["{symbolName} data is delayed by {time} minutes because of exchange requirements."]}},51931:e=>{e.exports={en:["Data is updated once every {amount} second, even if there are more updates on the market.","Data is updated once every {amount} seconds, even if there are more updates on the market."],ro:["Data is updated once every {amount} second, even if there are more updates on the market.","Data is updated once every {amount} seconds, even if there are more updates on the market."]}},83978:e=>{e.exports={en:["Data on our Basic plan is updated once every {amount} second, even if there are more updates on the market.","Data on our Basic plan is updated once every {amount} seconds, even if there are more updates on the market."],ro:["Data on our Basic plan is updated once every {amount} second, even if there are more updates on the market.","Data on our Basic plan is updated once every {amount} seconds, even if there are more updates on the market."]}},46960:e=>{e.exports={en:["Hide indicator legend","Hide indicators legend"],ro:["Hide indicator legend","Hide indicators legend"]}},36050:e=>{e.exports={
en:["One update every {amount} second","One update every {amount} seconds"],ro:["One update every {amount} second","One update every {amount} seconds"]}},36553:e=>{e.exports={en:["Show indicator legend","Show indicators legend"],ro:["Show indicator legend","Show indicators legend"]}},39501:e=>{e.exports={en:["{number} day","{number} days"],ro:["{number} day","{number} days"]}},44646:e=>{e.exports={en:["{number} hour","{number} hours"],ro:["{number} hour","{number} hours"]}},32547:e=>{e.exports={en:["{number} minute","{number} minutes"],ro:["{number} minute","{number} minutes"]}}}]);