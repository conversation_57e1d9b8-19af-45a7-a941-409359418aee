(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[4026],{53310:e=>{e.exports={en:["Re"],sv:["Re"]}},94073:e=>{e.exports={en:["A"],sv:["A"]}},66384:e=>{e.exports={en:["L"],sv:["L"]}},85119:e=>{e.exports={en:["Dark"],sv:["Mörk"]}},96870:e=>{e.exports={en:["Light"],sv:["Ljus"]}},85886:e=>{e.exports={en:["d"],sv:["d"]}},44634:e=>{e.exports={en:["h"],sv:["h"]}},5977:e=>{e.exports={en:["m"],sv:["m"]}},21492:e=>{e.exports={en:["s"],sv:["s"]}},97559:e=>{e.exports={en:["{title} copy"],sv:["{title} kopia"]}},38691:e=>{e.exports={en:["D"],sv:["D"]}},77995:e=>{e.exports={en:["M"],sv:["M"]}},93934:e=>{e.exports={en:["R"],sv:["R"]}},82901:e=>{e.exports={en:["T"],sv:["T"]}},7408:e=>{e.exports={en:["W"],sv:["V"]}},38048:e=>{e.exports={en:["h"],sv:["t"]}},68430:e=>{e.exports={en:["m"],sv:["m"]}},68823:e=>{e.exports={en:["s"],sv:["K"]}},2696:e=>{e.exports={en:["C"],sv:["C"]}},43253:e=>{e.exports={en:["H"],sv:["H"]}},61372:e=>{e.exports={en:["HL2"],sv:["HL2"]}},55096:e=>{e.exports={en:["HLC3"],sv:["HLC3"]}},94174:e=>{e.exports={en:["OHLC4"],sv:["OHLC4"]}},89923:e=>{e.exports={en:["L"],sv:["L"]}},46728:e=>{e.exports={en:["O"],sv:["O"]}},32856:e=>{e.exports=Object.create(null),e.exports.Close_input={en:["Close"],sv:["Stängning"]},e.exports.Back_input={en:["Back"],sv:["Tillbaka"]},e.exports.Minimize_input={en:["Minimize"],sv:["Minimera"]},e.exports["Hull MA_input"]={en:["Hull MA"],sv:["Hull MA"]},e.exports.from_input={en:["from"],sv:["from"]},e.exports.to_input={en:["to"],sv:["to"]},e.exports["{number} item_combobox_input"]={en:["{number} item","{number} items"],sv:["{number} objekt","{number} objekt"]},e.exports.Length_input={en:["Length"],sv:["Längd"]},e.exports.Plot_input={en:["Plot"],sv:["Rita ut"]},e.exports.Zero_input={en:["Zero"],sv:["Noll"]},e.exports.Signal_input={en:["Signal"],sv:["Signal"]},e.exports.Long_input={en:["Long"],sv:["Lång"]},e.exports.Short_input={en:["Short"],sv:["Kort"]},e.exports.UpperLimit_input={en:["UpperLimit"],sv:["ÖvreGräns"]},e.exports.LowerLimit_input={en:["LowerLimit"],sv:["LowerLimit"]},e.exports.Offset_input={en:["Offset"],sv:["Kompensation"]},e.exports.length_input={en:["length"],sv:["längd"]},e.exports.mult_input={en:["mult"],sv:["mult"]},e.exports.short_input={en:["short"],sv:["kort"]},e.exports.long_input={en:["long"],sv:["lång"]},e.exports.Limit_input={en:["Limit"],sv:["Begränsning"]},e.exports.Move_input={en:["Move"],sv:["Flytta"]},e.exports.Value_input={en:["Value"],sv:["Värde"]},e.exports.Method_input={en:["Method"],sv:["Metod"]},e.exports["Values in status line_input"]={en:["Values in status line"],sv:["Värden i statuslinje"]},e.exports["Labels on price scale_input"]={en:["Labels on price scale"],sv:["Etiketter på prisskalan"]},e.exports["Accumulation/Distribution_input"]={en:["Accumulation/Distribution"],sv:["Ackumulering/Distribution"]},e.exports.ADR_B_input={en:["ADR_B"],sv:["ADR_B"]},e.exports["Equality Line_input"]={en:["Equality Line"],sv:["Kapitallinje"]},e.exports["Window Size_input"]={en:["Window Size"],sv:["Fönsterstorlek"]},e.exports.Sigma_input={en:["Sigma"],
sv:["Sigma"]},e.exports["Aroon Up_input"]={en:["Aroon Up"],sv:["Aroon upp"]},e.exports["Aroon Down_input"]={en:["Aroon Down"],sv:["Aroon ner"]},e.exports.Upper_input={en:["Upper"],sv:["Övre"]},e.exports.Lower_input={en:["Lower"],sv:["Lägre"]},e.exports.Deviation_input={en:["Deviation"],sv:["Avvikelse"]},e.exports["Levels Format_input"]={en:["Levels Format"],sv:["Nivåformat"]},e.exports["Labels Position_input"]={en:["Labels Position"],sv:["Etikettposition"]},e.exports["0 Level Color_input"]={en:["0 Level Color"],sv:["0 Nivåfärg"]},e.exports["0.236 Level Color_input"]={en:["0.236 Level Color"],sv:["0,236 Nivåfärg"]},e.exports["0.382 Level Color_input"]={en:["0.382 Level Color"],sv:["0,382 Nivåfärg"]},e.exports["0.5 Level Color_input"]={en:["0.5 Level Color"],sv:["0,5 Nivåfärg"]},e.exports["0.618 Level Color_input"]={en:["0.618 Level Color"],sv:["0,618 Nivåfärg"]},e.exports["0.65 Level Color_input"]={en:["0.65 Level Color"],sv:["0,65 Nivåfärg"]},e.exports["0.786 Level Color_input"]={en:["0.786 Level Color"],sv:["0,786 Nivåfärg"]},e.exports["1 Level Color_input"]={en:["1 Level Color"],sv:["1 Nivåfärg"]},e.exports["1.272 Level Color_input"]={en:["1.272 Level Color"],sv:["1,272 Nivåfärg"]},e.exports["1.414 Level Color_input"]={en:["1.414 Level Color"],sv:["1,414 Nivåfärg"]},e.exports["1.618 Level Color_input"]={en:["1.618 Level Color"],sv:["1,618 Nivåfärg"]},e.exports["1.65 Level Color_input"]={en:["1.65 Level Color"],sv:["1,65 Nivåfärg"]},e.exports["2.618 Level Color_input"]={en:["2.618 Level Color"],sv:["2,618 Nivåfärg"]},e.exports["2.65 Level Color_input"]={en:["2.65 Level Color"],sv:["2,65 Nivåfärg"]},e.exports["3.618 Level Color_input"]={en:["3.618 Level Color"],sv:["3,618 Nivåfärg"]},e.exports["3.65 Level Color_input"]={en:["3.65 Level Color"],sv:["3,65 Nivåfärg"]},e.exports["4.236 Level Color_input"]={en:["4.236 Level Color"],sv:["4,236 Nivåfärg"]},e.exports["-0.236 Level Color_input"]={en:["-0.236 Level Color"],sv:["-0,236 Nivåfärg"]},e.exports["-0.382 Level Color_input"]={en:["-0.382 Level Color"],sv:["-0,382 Nivåfärg"]},e.exports["-0.618 Level Color_input"]={en:["-0.618 Level Color"],sv:["-0,618 Nivåfärg"]},e.exports["-0.65 Level Color_input"]={en:["-0.65 Level Color"],sv:["-0,65 Nivåfärg"]},e.exports.ADX_input={en:["ADX"],sv:["ADX"]},e.exports["ADX Smoothing_input"]={en:["ADX Smoothing"],sv:["ADX Smoothing"]},e.exports["DI Length_input"]={en:["DI Length"],sv:["DI längd"]},e.exports.Smoothing_input={en:["Smoothing"],sv:["Smoothing"]},e.exports.ATR_input={en:["ATR"],sv:["ATR"]},e.exports.Growing_input={en:["Growing"],sv:["Växande"]},e.exports.Falling_input={en:["Falling"],sv:["Fallande"]},e.exports["Color 0_input"]={en:["Color 0"],sv:["Färg 0"]},e.exports["Color 1_input"]={en:["Color 1"],sv:["Färg 1"]},e.exports.Source_input={en:["Source"],sv:["Källa"]},e.exports.StdDev_input={en:["StdDev"],sv:["StdDev"]},e.exports.Basis_input={en:["Basis"],sv:["Basis"]},e.exports.Median_input={en:["Median"],sv:["Median"]},e.exports["Bollinger Bands %B_input"]={en:["Bollinger Bands %B"],sv:["Bollinger band %B"]},
e.exports.Overbought_input={en:["Overbought"],sv:["Överköpt"]},e.exports.Oversold_input={en:["Oversold"],sv:["Översåld"]},e.exports["Bollinger Bands Width_input"]={en:["Bollinger Bands Width"],sv:["Bollinger Bands Width"]},e.exports["RSI Length_input"]={en:["RSI Length"],sv:["RSI-längd"]},e.exports["UpDown Length_input"]={en:["UpDown Length"],sv:["UpDown längd"]},e.exports["ROC Length_input"]={en:["ROC Length"],sv:["ROC längd"]},e.exports.MF_input={en:["MF"],sv:["MF"]},e.exports.resolution_input={en:["resolution"],sv:["upplösning"]},e.exports["Fast Length_input"]={en:["Fast Length"],sv:["Snabb längd"]},e.exports["Slow Length_input"]={en:["Slow Length"],sv:["Långsam längd"]},e.exports["Chaikin Oscillator_input"]={en:["Chaikin Oscillator"],sv:["Chaikin Oscillator"]},e.exports.P_input={en:["P"],sv:["P"]},e.exports.X_input={en:["X"],sv:["X"]},e.exports.Q_input={en:["Q"],sv:["Q"]},e.exports.p_input={en:["p"],sv:["p"]},e.exports.x_input={en:["x"],sv:["x"]},e.exports.q_input={en:["q"],sv:["q"]},e.exports.Price_input={en:["Price"],sv:["Pris"]},e.exports["Chande MO_input"]={en:["Chande MO"],sv:["Chande MO"]},e.exports["Zero Line_input"]={en:["Zero Line"],sv:["Nollinje"]},e.exports["Color 2_input"]={en:["Color 2"],sv:["Färg 2"]},e.exports["Color 3_input"]={en:["Color 3"],sv:["Färg 3"]},e.exports["Color 4_input"]={en:["Color 4"],sv:["Färg 4"]},e.exports["Color 5_input"]={en:["Color 5"],sv:["Färg 5"]},e.exports["Color 6_input"]={en:["Color 6"],sv:["Färg 6"]},e.exports["Color 7_input"]={en:["Color 7"],sv:["Färg 7"]},e.exports["Color 8_input"]={en:["Color 8"],sv:["Färg 8"]},e.exports.CHOP_input={en:["CHOP"],sv:["CHOP"]},e.exports["Upper Band_input"]={en:["Upper Band"],sv:["Övre band"]},e.exports["Lower Band_input"]={en:["Lower Band"],sv:["Lägre band"]},e.exports.CCI_input={en:["CCI"],sv:["CCI"]},e.exports["Smoothing Line_input"]={en:["Smoothing Line"],sv:["Utjämnande linje"]},e.exports["Smoothing Length_input"]={en:["Smoothing Length"],sv:["Utjämnande längd"]},e.exports["WMA Length_input"]={en:["WMA Length"],sv:["WMA-längd"]},e.exports["Long RoC Length_input"]={en:["Long RoC Length"],sv:["Lång RoC längd"]},e.exports["Short RoC Length_input"]={en:["Short RoC Length"],sv:["Kort RoC längd"]},e.exports.sym_input={en:["sym"],sv:["sym"]},e.exports.Symbol_input={en:["Symbol"],sv:["Symbol"]},e.exports.Correlation_input={en:["Correlation"],sv:["Korrelation"]},e.exports.Period_input={en:["Period"],sv:["Period"]},e.exports.Centered_input={en:["Centered"],sv:["Centrerad"]},e.exports["Detrended Price Oscillator_input"]={en:["Detrended Price Oscillator"],sv:["Detrended Price Oscillator"]},e.exports.isCentered_input={en:["isCentered"],sv:["isCentered"]},e.exports.DPO_input={en:["DPO"],sv:["DPO"]},e.exports["ADX smoothing_input"]={en:["ADX smoothing"],sv:["ADX Smoothing"]},e.exports["+DI_input"]={en:["+DI"],sv:["+DI"]},e.exports["-DI_input"]={en:["-DI"],sv:["-DI"]},e.exports.DEMA_input={en:["DEMA"],sv:["DEMA"]},e.exports["Multi timeframe_input"]={en:["Multi timeframe"],sv:["Multi timeframe"]},e.exports.Timeframe_input={en:["Timeframe"],
sv:["Timeframe"]},e.exports["Wait for timeframe closes_input"]={en:["Wait for timeframe closes"],sv:["Wait for timeframe closes"]},e.exports.Divisor_input={en:["Divisor"],sv:["Avgränsare"]},e.exports.EOM_input={en:["EOM"],sv:["EOM"]},e.exports["Elder's Force Index_input"]={en:["Elder's Force Index"],sv:["Elder's Force Index"]},e.exports.Percent_input={en:["Percent"],sv:["Procent"]},e.exports.Exponential_input={en:["Exponential"],sv:["Exponentiell"]},e.exports.Average_input={en:["Average"],sv:["Genomsnitt"]},e.exports["Upper Percentage_input"]={en:["Upper Percentage"],sv:["Övre procentsats"]},e.exports["Lower Percentage_input"]={en:["Lower Percentage"],sv:["Lägre procentsats"]},e.exports.Fisher_input={en:["Fisher"],sv:["Fisher"]},e.exports.Trigger_input={en:["Trigger"],sv:["Utlösare"]},e.exports.Level_input={en:["Level"],sv:["Nivå"]},e.exports["Trader EMA 1 length_input"]={en:["Trader EMA 1 length"],sv:["Trader EMA 1 längd"]},e.exports["Trader EMA 2 length_input"]={en:["Trader EMA 2 length"],sv:["Trader EMA 2 längd"]},e.exports["Trader EMA 3 length_input"]={en:["Trader EMA 3 length"],sv:["Trader EMA 3 längd"]},e.exports["Trader EMA 4 length_input"]={en:["Trader EMA 4 length"],sv:["Trader EMA 4 längd"]},e.exports["Trader EMA 5 length_input"]={en:["Trader EMA 5 length"],sv:["Trader EMA 5 längd"]},e.exports["Trader EMA 6 length_input"]={en:["Trader EMA 6 length"],sv:["Trader EMA 6 längd"]},e.exports["Investor EMA 1 length_input"]={en:["Investor EMA 1 length"],sv:["Investerare EMA 1 längd"]},e.exports["Investor EMA 2 length_input"]={en:["Investor EMA 2 length"],sv:["Investerare EMA 2 längd"]},e.exports["Investor EMA 3 length_input"]={en:["Investor EMA 3 length"],sv:["Investerare EMA 3 längd"]},e.exports["Investor EMA 4 length_input"]={en:["Investor EMA 4 length"],sv:["Investerare EMA 4 längd"]},e.exports["Investor EMA 5 length_input"]={en:["Investor EMA 5 length"],sv:["Investerare EMA 5 längd"]},e.exports["Investor EMA 6 length_input"]={en:["Investor EMA 6 length"],sv:["Investerare EMA 6 längd"]},e.exports.HV_input={en:["HV"],sv:["HV"]},e.exports["Conversion Line Periods_input"]={en:["Conversion Line Periods"],sv:["Konverteringspunktlinje"]},e.exports["Base Line Periods_input"]={en:["Base Line Periods"],sv:["Baslinjepunkter"]},e.exports["Lagging Span_input"]={en:["Lagging Span"],sv:["Fördröjningsspann"]},e.exports["Conversion Line_input"]={en:["Conversion Line"],sv:["Konverteringslinje"]},e.exports["Base Line_input"]={en:["Base Line"],sv:["Baslinje"]},e.exports["Leading Span A_input"]={en:["Leading Span A"],sv:["Bly 1"]},e.exports["Leading Span B_input"]={sv:["Fördröjningsspann 2 perioder"]},e.exports["Plots Background_input"]={en:["Plots Background"],sv:["Ritbakgrund"]},e.exports["yay Color 0_input"]={en:["yay Color 0"],sv:["yay Färg 0"]},e.exports["yay Color 1_input"]={en:["yay Color 1"],sv:["yay Färg 1"]},e.exports.Multiplier_input={en:["Multiplier"],sv:["Multiplikator"]},e.exports["Bands style_input"]={en:["Bands style"],sv:["Bandstil"]},e.exports.Middle_input={en:["Middle"],sv:["Mitten"]},
e.exports.useTrueRange_input={en:["useTrueRange"],sv:["användTrueRange"]},e.exports.ROCLen1_input={en:["ROCLen1"],sv:["ROCLen1"]},e.exports.ROCLen2_input={en:["ROCLen2"],sv:["ROCLen2"]},e.exports.ROCLen3_input={en:["ROCLen3"],sv:["ROCLen3"]},e.exports.ROCLen4_input={en:["ROCLen4"],sv:["ROCLen4"]},e.exports.SMALen1_input={en:["SMALen1"],sv:["SMALen1"]},e.exports.SMALen2_input={en:["SMALen2"],sv:["SMALen2"]},e.exports.SMALen3_input={en:["SMALen3"],sv:["SMALen3"]},e.exports.SMALen4_input={en:["SMALen4"],sv:["SMALen4"]},e.exports.SigLen_input={en:["SigLen"],sv:["SigLen"]},e.exports.KST_input={en:["KST"],sv:["KST"]},e.exports.Sig_input={en:["Sig"],sv:["Sig"]},e.exports.roclen1_input={en:["roclen1"],sv:["roclen1"]},e.exports.roclen2_input={en:["roclen2"],sv:["roclen2"]},e.exports.roclen3_input={en:["roclen3"],sv:["roclen3"]},e.exports.roclen4_input={en:["roclen4"],sv:["roclen4"]},e.exports.smalen1_input={en:["smalen1"],sv:["smalen1"]},e.exports.smalen2_input={en:["smalen2"],sv:["smalen2"]},e.exports.smalen3_input={en:["smalen3"],sv:["smalen3"]},e.exports.smalen4_input={en:["smalen4"],sv:["smalen4"]},e.exports.siglen_input={en:["siglen"],sv:["siglin"]},e.exports["Upper Deviation_input"]={en:["Upper Deviation"],sv:["Övre avvikelse"]},e.exports["Lower Deviation_input"]={en:["Lower Deviation"],sv:["Lägre avvikelse"]},e.exports["Use Upper Deviation_input"]={en:["Use Upper Deviation"],sv:["Använd övre Deviation"]},e.exports["Use Lower Deviation_input"]={en:["Use Lower Deviation"],sv:["Använd lägre deviation"]},e.exports.Count_input={en:["Count"],sv:["Räkna"]},e.exports.Crosses_input={en:["Crosses"],sv:["Korsar"]},e.exports.MOM_input={en:["MOM"],sv:["MOM"]},e.exports.MA_input={en:["MA"],sv:["MA"]},e.exports["Length EMA_input"]={en:["Length EMA"],sv:["EMA längd"]},e.exports["Length MA_input"]={en:["Length MA"],sv:["MA längd"]},e.exports["Fast length_input"]={en:["Fast length"],sv:["Snabb längd"]},e.exports["Slow length_input"]={en:["Slow length"],sv:["Långsam längd"]},e.exports["Signal smoothing_input"]={en:["Signal smoothing"],sv:["Signal smoothing"]},e.exports["Simple ma(oscillator)_input"]={en:["Simple ma(oscillator)"],sv:["Enkel ma(oscillator)"]},e.exports["Simple ma(signal line)_input"]={en:["Simple ma(signal line)"],sv:["Enkel ma(signallinje)"]},e.exports.Histogram_input={en:["Histogram"],sv:["Histogram"]},e.exports.MACD_input={en:["MACD"],sv:["MACD"]},e.exports.fastLength_input={en:["fastLength"],sv:["fastLength"]},e.exports.slowLength_input={en:["slowLength"],sv:["visaLängd"]},e.exports.signalLength_input={en:["signalLength"],sv:["signalLängd"]},e.exports.NV_input={en:["NV"],sv:["NV"]},e.exports.OnBalanceVolume_input={en:["OnBalanceVolume"],sv:["OnBalanceVolume"]},e.exports.Start_input={en:["Start"],sv:["Start"]},e.exports.Increment_input={en:["Increment"],sv:["Ökning"]},e.exports["Max value_input"]={en:["Max value"],sv:["Maxvärde"]},e.exports.ParabolicSAR_input={en:["ParabolicSAR"],sv:["ParabolicSAR"]},e.exports.start_input={en:["start"],sv:["start"]},e.exports.increment_input={en:["increment"],sv:["ökning"]},
e.exports.maximum_input={en:["maximum"],sv:["Maximal"]},e.exports["Short length_input"]={en:["Short length"],sv:["Kort längd"]},e.exports["Long length_input"]={en:["Long length"],sv:["Lång längd"]},e.exports.OSC_input={en:["OSC"],sv:["OSC"]},e.exports.shortlen_input={en:["shortlen"],sv:["shortlen"]},e.exports.longlen_input={en:["longlen"],sv:["longlen"]},e.exports.PVT_input={en:["PVT"],sv:["PVT"]},e.exports.ROC_input={en:["ROC"],sv:["ROC"]},e.exports.RSI_input={en:["RSI"],sv:["RSI"]},e.exports.RVGI_input={en:["RVGI"],sv:["RVGI"]},e.exports.RVI_input={en:["RVI"],sv:["RVI"]},e.exports["Long period_input"]={en:["Long period"],sv:["Lång period"]},e.exports["Short period_input"]={en:["Short period"],sv:["Kort period"]},e.exports["Signal line period_input"]={en:["Signal line period"],sv:["Signallinje period"]},e.exports.SMI_input={en:["SMI"],sv:["SMI"]},e.exports["SMI Ergodic Oscillator_input"]={en:["SMI Ergodic Oscillator"],sv:["SMI Ergodisk Oscillator"]},e.exports.Indicator_input={en:["Indicator"],sv:["Indikator"]},e.exports.Oscillator_input={en:["Oscillator"],sv:["Oscillator"]},e.exports.K_input={en:["K"],sv:["K"]},e.exports.D_input={en:["D"],sv:["D"]},e.exports.smoothK_input={en:["smoothK"],sv:["smoothK"]},e.exports.smoothD_input={en:["smoothD"],sv:["smoothD"]},e.exports["%K_input"]={en:["%K"],sv:["%K"]},e.exports["%D_input"]={en:["%D"],sv:["％D"]},e.exports["Stochastic Length_input"]={en:["Stochastic Length"],sv:["Stochastic längd"]},e.exports["RSI Source_input"]={en:["RSI Source"],sv:["RSI-källa"]},e.exports.lengthRSI_input={en:["lengthRSI"],sv:["längdRSI"]},e.exports.lengthStoch_input={en:["lengthStoch"],sv:["längdStoch"]},e.exports.TRIX_input={en:["TRIX"],sv:["TRIX"]},e.exports.TEMA_input={en:["TEMA"],sv:["TEMA"]},e.exports["Long Length_input"]={en:["Long Length"],sv:["Lång längd"]},e.exports["Short Length_input"]={en:["Short Length"],sv:["Kort längd"]},e.exports["Signal Length_input"]={en:["Signal Length"],sv:["Signallängd"]},e.exports.Length1_input={en:["Length1"],sv:["Längd1"]},e.exports.Length2_input={en:["Length2"],sv:["Längd2"]},e.exports.Length3_input={en:["Length3"],sv:["Längd3"]},e.exports.length7_input={en:["length7"],sv:["längd7"]},e.exports.length14_input={en:["length14"],sv:["längd14"]},e.exports.length28_input={en:["length28"],sv:["längd28"]},e.exports.UO_input={en:["UO"],sv:["UO"]},e.exports.VWMA_input={en:["VWMA"],sv:["VWMA"]},e.exports.len_input={en:["len"],sv:["len"]},e.exports["VI +_input"]={en:["VI +"],sv:["VI +"]},e.exports["VI -_input"]={en:["VI -"],sv:["VI -"]},e.exports["%R_input"]={en:["%R"],sv:["%R"]},e.exports["Jaw Length_input"]={en:["Jaw Length"],sv:["Jaw längd"]},e.exports["Teeth Length_input"]={en:["Teeth Length"],sv:["Teeth längd"]},e.exports["Lips Length_input"]={en:["Lips Length"],sv:["Lips längd"]},e.exports.Jaw_input={en:["Jaw"],sv:["Jaw"]},e.exports.Teeth_input={en:["Teeth"],sv:["Tänder"]},e.exports.Lips_input={en:["Lips"],sv:["Lips"]},e.exports["Jaw Offset_input"]={en:["Jaw Offset"],sv:["Jaw vid start"]},e.exports["Teeth Offset_input"]={en:["Teeth Offset"],sv:["Teeth vid start"]
},e.exports["Lips Offset_input"]={en:["Lips Offset"],sv:["Lips vid start"]},e.exports["Down fractals_input"]={en:["Down fractals"],sv:["Nedåtgående fraktal"]},e.exports["Up fractals_input"]={en:["Up fractals"],sv:["Uppåtgående fraktal"]},e.exports.Periods_input={en:["Periods"],sv:["Perioder"]},e.exports.Shapes_input={en:["Shapes"],sv:["Former"]},e.exports["show MA_input"]={en:["show MA"],sv:["Visa MA"]},e.exports["MA Length_input"]={en:["MA Length"],sv:["MA längd"]},e.exports["Color based on previous close_input"]={en:["Color based on previous close"],sv:["Färg baserad på föregående stängning"]},e.exports["Rows Layout_input"]={en:["Rows Layout"],sv:["Radlayout"]},e.exports["Row Size_input"]={en:["Row Size"],sv:["Radstorlek"]},e.exports.Volume_input={en:["Volume"],sv:["Volym"]},e.exports["Value Area volume_input"]={en:["Value Area volume"],sv:["Volym på områdesvärde"]},e.exports["Extend Right_input"]={en:["Extend Right"],sv:["Utöka åt höger"]},e.exports["Extend POC Right_input"]={en:["Extend POC Right"],sv:["Utöka POC åt höger"]},e.exports["Extend VAH Right_input"]={en:["Extend VAH Right"],sv:["Utöka VAH höger"]},e.exports["Extend VAL Right_input"]={en:["Extend VAL Right"],sv:["Utöka VAL höger"]},e.exports["Value Area Volume_input"]={en:["Value Area Volume"],sv:["Volym på områdesvärde"]},e.exports.Placement_input={en:["Placement"],sv:["Placering"]},e.exports.POC_input={en:["POC"],sv:["POC"]},e.exports["Developing Poc_input"]={en:["Developing Poc"],sv:["Utvecklande Poc"]},e.exports["Up Volume_input"]={en:["Up Volume"],sv:["Volym upp"]},e.exports["Down Volume_input"]={en:["Down Volume"],sv:["Volym ner"]},e.exports["Value Area_input"]={en:["Value Area"],sv:["Områdessvärde"]},e.exports["Histogram Box_input"]={en:["Histogram Box"],sv:["Histogrambox"]},e.exports["Value Area Up_input"]={en:["Value Area Up"],sv:["Värdearea upp"]},e.exports["Value Area Down_input"]={en:["Value Area Down"],sv:["värdearea ner"]},e.exports["Number Of Rows_input"]={en:["Number Of Rows"],sv:["Antal rader"]},e.exports["Ticks Per Row_input"]={en:["Ticks Per Row"],sv:["Tick per rad"]},e.exports["Up/Down_input"]={en:["Up/Down"],sv:["Upp/ned"]},e.exports.Total_input={en:["Total"],sv:["Total"]},e.exports.Delta_input={en:["Delta"],sv:["Delta"]},e.exports.Bar_input={en:["Bar"],sv:["Stapel"]},e.exports.Day_input={en:["Day"],sv:["Dag"]},e.exports["Deviation (%)_input"]={en:["Deviation (%)"],sv:["Avvikelse (%)"]},e.exports.Depth_input={en:["Depth"],sv:["Djup"]},e.exports["Extend to last bar_input"]={en:["Extend to last bar"],sv:["Utöka den sista stapeln"]},e.exports.Simple_input={en:["Simple"],sv:["Enkel"]},e.exports.Weighted_input={en:["Weighted"],sv:["Viktad"]},e.exports["Wilder's Smoothing_input"]={en:["Wilder's Smoothing"],sv:["Wilders smoothing"]},e.exports["1st Period_input"]={en:["1st Period"],sv:["1:a perioden"]},e.exports["2nd Period_input"]={en:["2nd Period"],sv:["2:a perioden"]},e.exports["3rd Period_input"]={en:["3rd Period"],sv:["3:e perioden"]},e.exports["4th Period_input"]={en:["4th Period"],sv:["4:e perioden"]},e.exports["5th Period_input"]={
en:["5th Period"],sv:["5:e perioden"]},e.exports["6th Period_input"]={en:["6th Period"],sv:["6:e perioden"]},e.exports["Rate of Change Lookback_input"]={en:["Rate of Change Lookback"],sv:["Återblick på förändringsgrad"]},e.exports["Instrument 1_input"]={en:["Instrument 1"],sv:["Instrument 1"]},e.exports["Instrument 2_input"]={en:["Instrument 2"],sv:["Instrument 2"]},e.exports["Rolling Period_input"]={en:["Rolling Period"],sv:["Rullande period"]},e.exports["Standard Errors_input"]={en:["Standard Errors"],sv:["Standardfel"]},e.exports["Averaging Periods_input"]={en:["Averaging Periods"],sv:["Medelvärdesperioder"]},e.exports["Days Per Year_input"]={en:["Days Per Year"],sv:["Dagar per år"]},e.exports["Market Closed Percentage_input"]={en:["Market Closed Percentage"],sv:["Procent vid marknadsstängning"]},e.exports["ATR Mult_input"]={en:["ATR Mult"],sv:["ATR-multiplikator"]},e.exports.VWAP_input={en:["VWAP"],sv:["VWAP"]},e.exports["Anchor Period_input"]={en:["Anchor Period"],sv:["Ankarperiod"]},e.exports.Session_input={en:["Session"],sv:["Session"]},e.exports.Week_input={en:["Week"],sv:["Vecka"]},e.exports.Month_input={en:["Month"],sv:["Månad"]},e.exports.Year_input={en:["Year"],sv:["År"]},e.exports.Decade_input={en:["Decade"],sv:["Decennium"]},e.exports.Century_input={en:["Century"],sv:["Sekel"]},e.exports.Sessions_input={en:["Sessions"],sv:["Sessioner"]},e.exports["Each (pre-market, market, post-market)_input"]={en:["Each (pre-market, market, post-market)"],sv:["Vardera (förmarknad, marknad, eftermarknad)"]},e.exports["Pre-market only_input"]={en:["Pre-market only"],sv:["Enbart förmarknad"]},e.exports["Market only_input"]={en:["Market only"],sv:["Enbart marknad"]},e.exports["Post-market only_input"]={en:["Post-market only"],sv:["nbart eftermarknad"]},e.exports["Main chart symbol_input"]={en:["Main chart symbol"],sv:["Huvuddiagramsymbol"]},e.exports["Another symbol_input"]={en:["Another symbol"],sv:["Annan symbol"]},e.exports.Line_input={en:["Line"],sv:["Linje"]},e.exports["Nothing selected_combobox_input"]={en:["Nothing selected"],sv:["Inget valt"]},e.exports["All items_combobox_input"]={en:["All items"],sv:["Alla föremål"]},e.exports.Cancel_input={en:["Cancel"],sv:["Avbryt"]},e.exports.Open_input={en:["Open"],sv:["Open"]},e.exports.MM_month_input={en:["MM"]},e.exports.YY_year_input={en:["YY"]},e.exports.Style_input={en:["Style"]},e.exports["Box size assignment method_input"]={en:["Box size assignment method"]},e.exports["Color bars based on previous close_input"]={en:["Color bars based on previous close"]},e.exports.Candles_input={en:["Candles"]},e.exports.Borders_input={en:["Borders"]},e.exports.Wick_input={en:["Wick"]},e.exports["HLC bars_input"]={en:["HLC bars"]},e.exports["Price source_input"]={en:["Price source"]},e.exports.Type_input={en:["Type"]},e.exports["Show real prices on price scale (instead of Heikin-Ashi price)_input"]={en:["Show real prices on price scale (instead of Heikin-Ashi price)"]},e.exports["Up bars_input"]={en:["Up bars"]},e.exports["Down bars_input"]={en:["Down bars"]},
e.exports["Projection up bars_input"]={en:["Projection up bars"]},e.exports["Projection down bars_input"]={en:["Projection down bars"]},e.exports["Projection up color_input"]={en:["Projection up color"]},e.exports["Projection down color_input"]={en:["Projection down color"]},e.exports.Fill_input={en:["Fill"]},e.exports["Up color_input"]={en:["Up color"]},e.exports["Down color_input"]={en:["Down color"]},e.exports.Traditional_input={en:["Traditional"]},e.exports.PercentageLTP_input={en:["PercentageLTP"]},e.exports["Box size_input"]={en:["Box size"]},e.exports["Number of line_input"]={en:["Number of line"]},e.exports["ATR length_input"]={en:["ATR length"]},e.exports.Percentage_input={en:["Percentage"]},e.exports["Reversal amount_input"]={en:["Reversal amount"]},e.exports["Phantom bars_input"]={en:["Phantom bars"]},e.exports["One step back building_input"]={en:["One step back building"]},e.exports.Wicks_input={en:["Wicks"]},e.exports.Range_input={en:["Range"]},e.exports.All_input={en:["All"]},e.exports.Custom_input={en:["Custom"]},e.exports["Leading Span Periods_input"]={en:["Leading Span Periods"]},e.exports["Leading Shift Periods_input"]={en:["Leading Shift Periods"]},e.exports["ATR({atrValue})_input"]={en:["ATR({atrValue})"]},e.exports["PercentageLTP({percentageLTPValue}%)_input"]={en:["PercentageLTP({percentageLTPValue}%)"]}},50873:e=>{e.exports={en:["ATR({atrValue})"]}},28037:e=>{e.exports={en:["PercentageLTP({percentageLTPValue}%)"]}},40566:e=>{e.exports={en:["Traditional"]}},75163:e=>{e.exports={en:["Invert scale"],sv:["Invertera skala"]}},35210:e=>{e.exports={en:["Indexed to 100"],sv:["Indexeras till 100"]}},31340:e=>{e.exports={en:["Logarithmic"],sv:["Logaritmisk"]}},19405:e=>{e.exports={en:["No overlapping labels"],sv:["Inga överlappande etiketter"]}},34954:e=>{e.exports={en:["Percent"],sv:["Procent"]}},55300:e=>{e.exports={en:["Regular"],sv:["Vanlig"]}},8029:e=>{e.exports={en:["ETH"],sv:["ETH"]}},34647:e=>{e.exports={en:["Electronic trading hours"],sv:["Elektroniska handelstimmar"]}},36862:e=>{e.exports={en:["Extended trading hours"],sv:["Förlängda handelstider"]}},7807:e=>{e.exports={en:["POST"],sv:["efter"]}},46273:e=>{e.exports={en:["PRE"],sv:["för"]}},50434:e=>{e.exports={en:["Postmarket"],sv:["Eftermarknads"]}},59330:e=>{e.exports={en:["Premarket"],sv:["Förmarknad"]}},35342:e=>{e.exports={en:["RTH"],sv:["RTH"]}},84246:e=>{e.exports={en:["Regular trading hours"],sv:["Vanliga handelstimmar"]}},13132:e=>{e.exports={en:["May"],sv:["Maj"]}},83477:e=>{e.exports=Object.create(null),e.exports.Technicals_study={en:["Technicals"],sv:["Tekniska"]},e.exports["Average Day Range_study"]={en:["Average Day Range"],sv:["Genomsnittligt dagsintervall"]},e.exports["Bull Bear Power_study"]={en:["Bull Bear Power"],sv:["Bull Bear Styrka"]},e.exports["Capital expenditures_study"]={en:["Capital expenditures"],sv:["Kapitalutgifter"]},e.exports["Cash to debt ratio_study"]={en:["Cash to debt ratio"],sv:["Kvot mellan likvida medel och skulder"]},e.exports["Debt to EBITDA ratio_study"]={en:["Debt to EBITDA ratio"],
sv:["Skulder i förhållande till EBITDA"]},e.exports["Directional Movement Index_study"]={en:["Directional Movement Index"],sv:["Riktningsindex för rörelser"]},e.exports.DMI_study={en:["DMI"],sv:["DMI"]},e.exports["Dividend payout ratio %_study"]={en:["Dividend payout ratio %"],sv:["Utdelningsgrad %"]},e.exports["Equity to assets ratio_study"]={en:["Equity to assets ratio"],sv:["Soliditet"]},e.exports["Enterprise value to EBIT ratio_study"]={en:["Enterprise value to EBIT ratio"],sv:["Företagsvärde i förhållande till EBIT"]},e.exports["Enterprise value to EBITDA ratio_study"]={en:["Enterprise value to EBITDA ratio"],sv:["Företagsvärde i förhållande till EBITDA"]},e.exports["Enterprise value to revenue ratio_study"]={en:["Enterprise value to revenue ratio"],sv:["Företagsvärde i förhållande till omsättning"]},e.exports["Goodwill, net_study"]={en:["Goodwill, net"],sv:["Goodwill, netto"]},e.exports["Ichimoku Cloud_study"]={en:["Ichimoku Cloud"],sv:["Ichimoku Cloud"]},e.exports.Ichimoku_study={en:["Ichimoku"],sv:["Ichimoku"]},e.exports["Moving Average Convergence Divergence_study"]={en:["Moving Average Convergence Divergence"],sv:["Glidande medelvärde konvergens divergens"]},e.exports["Operating income_study"]={en:["Operating income"],sv:["Rörelseintäkt"]},e.exports["Price to book ratio_study"]={en:["Price to book ratio"],sv:["Kvotvärde (P/B-förhållande)"]},e.exports["Price to cash flow ratio_study"]={en:["Price to cash flow ratio"],sv:["Pris/kassaflödesförhållande"]},e.exports["Price to earnings ratio_study"]={en:["Price to earnings ratio"],sv:["P/E-kvot"]},e.exports["Price to free cash flow ratio_study"]={en:["Price to free cash flow ratio"],sv:["Pris/fritt kassaflödesförhållande (P/FCF-kvot)"]},e.exports["Price to sales ratio_study"]={en:["Price to sales ratio"],sv:["Pris/försäljningskvot (P/S)"]},e.exports["Float shares outstanding_study"]={en:["Float shares outstanding"],sv:["Utestående aktier i omsättning"]},e.exports["Total common shares outstanding_study"]={en:["Total common shares outstanding"],sv:["Summa utestående stamaktier"]},e.exports["Volume Weighted Average Price_study"]={en:["Volume Weighted Average Price"],sv:["Volymviktat medelpris"]},e.exports["Volume Weighted Moving Average_study"]={en:["Volume Weighted Moving Average"],sv:["Volymviktat glidande medelvärde"]},e.exports["Williams Percent Range_study"]={en:["Williams Percent Range"],sv:["Williams procentintervall"]},e.exports.Doji_study={en:["Doji"],sv:["Doji"]},e.exports["Spinning Top Black_study"]={en:["Spinning Top Black"],sv:["Spinning Top Black"]},e.exports["Spinning Top White_study"]={en:["Spinning Top White"],sv:["Spinning Top White"]},e.exports["Accounts payable_study"]={en:["Accounts payable"],sv:["Leverantörsskulder"]},e.exports["Accounts receivables, gross_study"]={en:["Accounts receivables, gross"],sv:["Kundfordringar, brutto"]},e.exports["Accounts receivable - trade, net_study"]={en:["Accounts receivable - trade, net"],sv:["Kundfordringar - handel, netto"]},e.exports.Accruals_study={en:["Accruals"],sv:["Periodiseringar"]},
e.exports["Accrued payroll_study"]={en:["Accrued payroll"],sv:["Upplupna löner"]},e.exports["Accumulated depreciation, total_study"]={en:["Accumulated depreciation, total"],sv:["Ackumulerade avskrivningar, totalt"]},e.exports["Additional paid-in capital/Capital surplus_study"]={en:["Additional paid-in capital/Capital surplus"],sv:["Övrigt inbetalt kapital/kapitalöverskott"]},e.exports["After tax other income/expense_study"]={en:["After tax other income/expense"],sv:["Övriga intäkter/kostnader efter skatt"]},e.exports["Altman Z-score_study"]={en:["Altman Z-score"],sv:["Altman Z-poäng"]},e.exports.Amortization_study={en:["Amortization"],sv:["Avskrivning"]},e.exports["Amortization of intangibles_study"]={en:["Amortization of intangibles"],sv:["Avskrivningar av immateriella tillgångar"]},e.exports["Amortization of deferred charges_study"]={en:["Amortization of deferred charges"],sv:["Avskrivning av förutbetalda kostnader"]},e.exports["Asset turnover_study"]={en:["Asset turnover"],sv:["Omsättning av tillgångar"]},e.exports["Average basic shares outstanding_study"]={en:["Average basic shares outstanding"],sv:["Genomsnittligt antal utestående aktier"]},e.exports["Bad debt / Doubtful accounts_study"]={en:["Bad debt / Doubtful accounts"],sv:["Osäkra fordringar/osäkra poster"]},e.exports["Basic EPS_study"]={en:["Basic EPS"],sv:["Grundläggande vinst per aktie"]},e.exports["Basic earnings per share (Basic EPS)_study"]={en:["Basic earnings per share (Basic EPS)"],sv:["Vinst per aktie (Basic EPS)"]},e.exports["Beneish M-score_study"]={en:["Beneish M-score"],sv:["Beneish M-poäng"]},e.exports["Book value per share_study"]={en:["Book value per share"],sv:["Bokfört värde per aktie"]},e.exports["Buyback yield %_study"]={en:["Buyback yield %"],sv:["Återköpsavkastning %"]},e.exports["Capital and operating lease obligations_study"]={en:["Capital and operating lease obligations"],sv:["Förpliktelser för kapital- och operationella leasingavtal"]},e.exports["Capital expenditures - fixed assets_study"]={en:["Capital expenditures - fixed assets"],sv:["Investeringsutgifter - anläggningstillgångar"]},e.exports["Capital expenditures - other assets_study"]={en:["Capital expenditures - other assets"],sv:["Investeringsutgifter - övriga tillgångar"]},e.exports["Capitalized lease obligations_study"]={en:["Capitalized lease obligations"],sv:["Aktiverade leasingförpliktelser"]},e.exports["Cash and short term investments_study"]={en:["Cash and short term investments"],sv:["Likvida medel och kortfristiga investeringar"]},e.exports["Cash conversion cycle_study"]={en:["Cash conversion cycle"],sv:["Kontantkonverteringscykel"]},e.exports["Cash & equivalents_study"]={en:["Cash & equivalents"],sv:["Likvida medel och motsvarande"]},e.exports["Cash from financing activities_study"]={en:["Cash from financing activities"],sv:["Likvida medel från finansieringsaktiviteter"]},e.exports["Cash from investing activities_study"]={en:["Cash from investing activities"],sv:["Likvida medel från investeringsaktiviteter"]},e.exports["Cash from operating activities_study"]={
en:["Cash from operating activities"],sv:["Kontanta medel från löpande drift"]},e.exports["Change in accounts payable_study"]={en:["Change in accounts payable"],sv:["Förändring av leverantörsskulder"]},e.exports["Change in accounts receivable_study"]={en:["Change in accounts receivable"],sv:["Förändringar av kundfordringar"]},e.exports["Change in accrued expenses_study"]={en:["Change in accrued expenses"],sv:["Förändring av upplupna kostnader"]},e.exports["Change in inventories_study"]={en:["Change in inventories"],sv:["Lagerförändring"]},e.exports["Change in other assets/liabilities_study"]={en:["Change in other assets/liabilities"],sv:["Förändring av andra tillgångar/skulder"]},e.exports["Change in taxes payable_study"]={en:["Change in taxes payable"],sv:["Förändring av skatteskulder"]},e.exports["Changes in working capital_study"]={en:["Changes in working capital"],sv:["Förändringar i rörelsekapital"]},e.exports["COGS to revenue ratio_study"]={en:["COGS to revenue ratio"],sv:["Kostnader för försäljning i förhållande till omsättningen"]},e.exports["Common dividends paid_study"]={en:["Common dividends paid"],sv:["Utbetalda utdelningar"]},e.exports["Common equity, total_study"]={en:["Common equity, total"],sv:["Primärt kapital, totalt"]},e.exports["Common stock par/Carrying value_study"]={en:["Common stock par/Carrying value"],sv:["Stamaktier nominellt värde/bärande värde"]},e.exports["Cost of goods_study"]={en:["Cost of goods"],sv:["Varukostnad"]},e.exports["Cost of goods sold_study"]={en:["Cost of goods sold"],sv:["Kostnad för sålda varor"]},e.exports["Current portion of LT debt and capital leases_study"]={en:["Current portion of LT debt and capital leases"],sv:["Nuvarande andel av långfristiga skulder och leasingavtal"]},e.exports["Current ratio_study"]={en:["Current ratio"],sv:["Aktuell kvot"]},e.exports["Days inventory_study"]={en:["Days inventory"],sv:["Dagar inventering"]},e.exports["Days payable_study"]={en:["Days payable"],sv:["Antal dagar som ska betalas"]},e.exports["Days sales outstanding_study"]={en:["Days sales outstanding"],sv:["Antal dagar med utestående försäljning"]},e.exports["Debt to assets ratio_study"]={en:["Debt to assets ratio"],sv:["Skulder i förhållande till tillgångar"]},e.exports["Debt to equity ratio_study"]={en:["Debt to equity ratio"],sv:["Skuldsättningsgrad"]},e.exports["Debt to revenue ratio_study"]={en:["Debt to revenue ratio"],sv:["Skuldkvot"]},e.exports["Deferred income, current_study"]={en:["Deferred income, current"],sv:["Uppskjutna intäkter, löpande"]},e.exports["Deferred income, non-current_study"]={en:["Deferred income, non-current"],sv:["Uppskjutna intäkter, ej löpande"]},e.exports["Deferred tax assets_study"]={en:["Deferred tax assets"],sv:["Uppskjutna skattefordringar"]},e.exports["Deferred taxes (cash flow)_study"]={en:["Deferred taxes (cash flow)"],sv:["Uppskjutna skatter (kassaflöde)"]},e.exports["Deferred tax liabilities_study"]={en:["Deferred tax liabilities"],sv:["Uppskjutna skatteskulder"]},e.exports.Depreciation_study={en:["Depreciation"],sv:["Nedsättning"]},
e.exports["Deprecation and amortization_study"]={en:["Deprecation and amortization"],sv:["Avskrivningar och amorteringar"]},e.exports["Depreciation & amortization (cash flow)_study"]={en:["Depreciation & amortization (cash flow)"],sv:["Avskrivningar och amorteringar (kassaflöde)"]},e.exports["Depreciation/depletion_study"]={en:["Depreciation/depletion"],sv:["Avskrivningar/nedskrivningar"]},e.exports["Diluted EPS_study"]={en:["Diluted EPS"],sv:["Resultat per aktie efter utspädning"]},e.exports["Diluted earnings per share (Diluted EPS)_study"]={en:["Diluted earnings per share (Diluted EPS)"],sv:["Utspädd vinst per aktie (Diluted EPS)"]},e.exports["Diluted net income available to common stockholders_study"]={en:["Diluted net income available to common stockholders"],sv:["Nettoresultat efter utspädning tillgängligt för stamaktieägare"]},e.exports["Diluted shares outstanding_study"]={en:["Diluted shares outstanding"],sv:["Utestående aktier efter utspädning"]},e.exports["Dilution adjustment_study"]={en:["Dilution adjustment"],sv:["Utspädningsjustering"]},e.exports["Discontinued operations_study"]={en:["Discontinued operations"],sv:["Avvecklad verksamhet"]},e.exports["Dividends payable_study"]={en:["Dividends payable"],sv:["Utdelningar som ska betalas"]},e.exports["Dividends per share - common stock primary issue_study"]={en:["Dividends per share - common stock primary issue"],sv:["Utdelningar per aktie – stamaktier i primäremission"]},e.exports["Dividend yield %_study"]={en:["Dividend yield %"],sv:["Direktavkastning (FY)"]},e.exports["Earnings yield_study"]={en:["Earnings yield"],sv:["Vinstavkastning"]},e.exports.EBIT_study={en:["EBIT"],sv:["EBIT"]},e.exports.EBITDA_study={en:["EBITDA"],sv:["EBITDA"]},e.exports["EBITDA margin %_study"]={en:["EBITDA margin %"],sv:["EBITDA-marginal %"]},e.exports["Effective interest rate on debt %_study"]={en:["Effective interest rate on debt %"],sv:["Effektiv ränta på skulder %"]},e.exports["Enterprise value_study"]={en:["Enterprise value"],sv:["Företagsvärde"]},e.exports["EPS basic one year growth_study"]={en:["EPS basic one year growth"],sv:["VPA grundläggande tillväxt på ett år"]},e.exports["EPS diluted one year growth_study"]={en:["EPS diluted one year growth"],sv:["VPA efter utspädning ett års tillväxt"]},e.exports["EPS estimates_study"]={en:["EPS estimates"],sv:["Uppskattningar av VPA"]},e.exports["Equity in earnings_study"]={en:["Equity in earnings"],sv:["Eget kapital i vinstmedel"]},e.exports["Financing activities – other sources_study"]={en:["Financing activities – other sources"],sv:["Finansieringsverksamhet - andra källor"]},e.exports["Financing activities – other uses_study"]={en:["Financing activities – other uses"],sv:["Finansieringsverksamhet - annan användning"]},e.exports["Free cash flow_study"]={en:["Free cash flow"],sv:["Fritt kassaflöde"]},e.exports["Free cash flow margin %_study"]={en:["Free cash flow margin %"],sv:["Marginal för fritt kassaflöde %"]},e.exports["Fulmer H factor_study"]={en:["Fulmer H factor"],sv:["Fulmer H-faktor"]},
e.exports["Funds from operations_study"]={en:["Funds from operations"],sv:["Medel från verksamheten"]},e.exports["Goodwill to assets ratio_study"]={en:["Goodwill to assets ratio"],sv:["Förhållandet mellan goodwill och tillgångar"]},e.exports["Graham's number_study"]={en:["Graham's number"],sv:["Grahams nummer"]},e.exports["Gross margin %_study"]={en:["Gross margin %"],sv:["Bruttomarginal %"]},e.exports["Gross profit_study"]={en:["Gross profit"],sv:["Bruttovinst"]},e.exports["Gross profit to assets ratio_study"]={en:["Gross profit to assets ratio"],sv:["Bruttovinst i förhållande till tillgångar"]},e.exports["Gross property/plant/equipment_study"]={en:["Gross property/plant/equipment"],sv:["Bruttoinvesteringar i materiella anläggningstillgångar"]},e.exports.Impairments_study={en:["Impairments"],sv:["Nedskrivningar"]},e.exports["Income Tax Credits_study"]={en:["Income Tax Credits"],sv:["Inkomstskatteavdrag"]},e.exports["Income tax, current_study"]={en:["Income tax, current"],sv:["Inkomstskatt, nuvarande"]},e.exports["Income tax, current - domestic_study"]={en:["Income tax, current - domestic"],sv:["Inkomstskatt, nuvarande – inhemsk"]},e.exports["Income Tax, current - foreign_study"]={en:["Income Tax, current - foreign"],sv:["Inkomstskatt, nuvarande – utländsk"]},e.exports["Income tax, deferred_study"]={en:["Income tax, deferred"],sv:["Uppskjuten inkomstskatt"]},e.exports["Income tax, deferred - domestic_study"]={en:["Income tax, deferred - domestic"],sv:["Förutbetald inkomstskatt – inom landet"]},e.exports["Income tax, deferred - foreign_study"]={en:["Income tax, deferred - foreign"],sv:["Förutbetald inkomstskatt – utländsk"]},e.exports["Income tax payable_study"]={en:["Income tax payable"],sv:["Inkomstskatt att betala"]},e.exports["Interest capitalized_study"]={en:["Interest capitalized"],sv:["Kapitaliserad ränta"]},e.exports["Interest coverage_study"]={en:["Interest coverage"],sv:["Räntetäckning"]},e.exports["Interest expense, net of interest capitalized_study"]={en:["Interest expense, net of interest capitalized"],sv:["Räntekostnader, netto av kapitaliserad ränta"]},e.exports["Interest expense on debt_study"]={en:["Interest expense on debt"],sv:["Räntekostnader för skulder"]},e.exports["Inventories - finished goods_study"]={en:["Inventories - finished goods"],sv:["Inventarier - färdiga varor"]},e.exports["Inventories - progress payments & other_study"]={en:["Inventories - progress payments & other"],sv:["Inventarier - delbetalningar och andra"]},e.exports["Inventories - raw materials_study"]={en:["Inventories - raw materials"],sv:["Inventarier - råvaror"]},e.exports["Inventories - work in progress_study"]={en:["Inventories - work in progress"],sv:["Inventarier - pågående arbete"]},e.exports["Inventory to revenue ratio_study"]={en:["Inventory to revenue ratio"],sv:["Förhållande mellan lager och omsättning"]},e.exports["Inventory turnover_study"]={en:["Inventory turnover"],sv:["Omsättningshastighet"]},e.exports["Investing activities – other sources_study"]={en:["Investing activities – other sources"],
sv:["Finansieringsverksamhet - andra källor"]},e.exports["Investing activities – other uses_study"]={en:["Investing activities – other uses"],sv:["Finansieringsverksamhet - andra källor"]},e.exports["Investments in unconsolidated subsidiaries_study"]={en:["Investments in unconsolidated subsidiaries"],sv:["Investeringar i icke-konsoliderade dotterbolag"]},e.exports["Issuance of long term debt_study"]={en:["Issuance of long term debt"],sv:["Emission av långfristiga skulder"]},e.exports["Issuance/retirement of debt, net_study"]={en:["Issuance/retirement of debt, net"],sv:["Emission/avveckling av skulder, netto"]},e.exports["Issuance/retirement of long term debt_study"]={en:["Issuance/retirement of long term debt"],sv:["Emission/avveckling av långfristiga skulder"]},e.exports["Issuance/retirement of other debt_study"]={en:["Issuance/retirement of other debt"],sv:["Emission/avveckling av andra skulder"]},e.exports["Issuance/retirement of short term debt_study"]={en:["Issuance/retirement of short term debt"],sv:["Emission/avveckling av kortfristiga skulder"]},e.exports["Issuance/retirement of stock, net_study"]={en:["Issuance/retirement of stock, net"],sv:["Emission/avveckling av aktier, netto"]},e.exports["KZ index_study"]={en:["KZ index"],sv:["KZ-index"]},e.exports["Legal claim expense_study"]={en:["Legal claim expense"],sv:["Kostnader för rättsliga anspråk"]},e.exports["Long term debt_study"]={en:["Long term debt"],sv:["Långfristiga skulder"]},e.exports["Long term debt excl. lease liabilities_study"]={en:["Long term debt excl. lease liabilities"],sv:["Långfristiga skulder exkl. leasingskulder"]},e.exports["Long term debt to total assets ratio_study"]={en:["Long term debt to total assets ratio"],sv:["Långfristig skuld i förhållande till totala tillgångar"]},e.exports["Long term debt to total equity ratio_study"]={en:["Long term debt to total equity ratio"],sv:["Långfristig skuld i förhållande till totalt eget kapital"]},e.exports["Long term investments_study"]={en:["Long term investments"],sv:["Långsiktiga investeringar"]},e.exports["Market capitalization_study"]={en:["Market capitalization"],sv:["Marknadskapitalisering"]},e.exports["Minority interest_study"]={en:["Minority interest"],sv:["Minoritetsintresse"]},e.exports["Miscellaneous non-operating expense_study"]={en:["Miscellaneous non-operating expense"],sv:["Diverse icke-operativa kostnader"]},e.exports["Net current asset value per share_study"]={en:["Net current asset value per share"],sv:["Substansvärde per aktie"]},e.exports["Net debt_study"]={en:["Net debt"],sv:["Nettoskuld"]},e.exports["Net income_study"]={en:["Net income"],sv:["Nettoinkomst"]},e.exports["Net income before discontinued operations_study"]={en:["Net income before discontinued operations"],sv:["Nettoinkomst innan verksamhetsavveckling"]},e.exports["Net income (cash flow)_study"]={en:["Net income (cash flow)"],sv:["Nettoresultat (kassaflöde)"]},e.exports["Net income per employee_study"]={en:["Net income per employee"],sv:["Nettoinkomst per anställd"]},e.exports["Net intangible assets_study"]={
en:["Net intangible assets"],sv:["Immateriella tillgångar netto"]},e.exports["Net margin %_study"]={en:["Net margin %"],sv:["Nettomarginal %"]},e.exports["Net property/plant/equipment_study"]={en:["Net property/plant/equipment"],sv:["Nettoinvesteringar i materiella anläggningstillgångar"]},e.exports["Non-cash items_study"]={en:["Non-cash items"],sv:["Icke-kontanta poster"]},e.exports["Non-controlling/minority interest_study"]={en:["Non-controlling/minority interest"],sv:["Minoritetsintresse utan bestämmande inflytande"]},e.exports["Non-operating income, excl. interest expenses_study"]={en:["Non-operating income, excl. interest expenses"],sv:["Icke-operativa intäkter, exkl. räntekostnader"]},e.exports["Non-operating income, total_study"]={en:["Non-operating income, total"],sv:["Icke-operativa intäkter, totalt"]},e.exports["Non-operating interest income_study"]={en:["Non-operating interest income"],sv:["Ränteintäkter från icke-operativ verksamhet"]},e.exports["Note receivable - long term_study"]={en:["Note receivable - long term"],sv:["Skuldbrev - långfristiga"]},e.exports["Notes payable_study"]={en:["Notes payable"],sv:["Skulder till kreditinstitut"]},e.exports["Number of employees_study"]={en:["Number of employees"],sv:["Antal anställda"]},e.exports["Number of shareholders_study"]={en:["Number of shareholders"],sv:["Antal aktieägare"]},e.exports["Operating earnings yield %_study"]={en:["Operating earnings yield %"],sv:["Rörelseresultatets avkastning %"]},e.exports["Operating expenses (excl. COGS)_study"]={en:["Operating expenses (excl. COGS)"],sv:["Driftskostnader (exkl. kostnad för sålda varor)"]},e.exports["Operating lease liabilities_study"]={en:["Operating lease liabilities"],sv:["Skulder för operationella leasingavtal"]},e.exports["Operating margin %_study"]={en:["Operating margin %"],sv:["Rörelsemarginal %"]},e.exports["Other COGS_study"]={en:["Other COGS"],sv:["Övriga kostnader för sålda varor"]},e.exports["Other common equity_study"]={en:["Other common equity"],sv:["Primärt kapital, totalt"]},e.exports["Other current assets, total_study"]={en:["Other current assets, total"],sv:["Övriga omsättningstillgångar, totalt"]},e.exports["Other current liabilities_study"]={en:["Other current liabilities"],sv:["Övriga kortfristiga skulder"]},e.exports["Other cost of goods sold_study"]={en:["Other cost of goods sold"],sv:["Ytterligare kostnader för sålda varor"]},e.exports["Other exceptional charges_study"]={en:["Other exceptional charges"],sv:["Övriga extraordinära kostnader"]},e.exports["Other financing cash flow items, total_study"]={en:["Other financing cash flow items, total"],sv:["Andra poster gällande finansiering av kassaflöde, summa"]},e.exports["Other intangibles, net_study"]={en:["Other intangibles, net"],sv:["Övriga immateriella tillgångar, netto"]},e.exports["Other investing cash flow items, total_study"]={en:["Other investing cash flow items, total"],sv:["Andra poster gällande investeringskassaflöde, summa"]},e.exports["Other investments_study"]={en:["Other investments"],sv:["Övriga investeringar"]},
e.exports["Other liabilities, total_study"]={en:["Other liabilities, total"],sv:["Övriga skulder, totalt"]},e.exports["Other long term assets, total_study"]={en:["Other long term assets, total"],sv:["Övriga långfristiga tillgångar, totalt"]},e.exports["Other non-current liabilities, total_study"]={en:["Other non-current liabilities, total"],sv:["Totalt övriga långfristiga skulder"]},e.exports["Other operating expenses, total_study"]={en:["Other operating expenses, total"],sv:["Övriga driftskostnader, totalt"]},e.exports["Other receivables_study"]={en:["Other receivables"],sv:["Övriga fordringar"]},e.exports["Other short term debt_study"]={en:["Other short term debt"],sv:["Andra kortfristiga skulder"]},e.exports["Paid in capital_study"]={en:["Paid in capital"],sv:["Inbetalt kapital"]},e.exports["PEG ratio_study"]={en:["PEG ratio"],sv:["PEG-förhållande"]},e.exports["Piotroski F-score_study"]={en:["Piotroski F-score"],sv:["Piotroski F-poäng"]},e.exports["Preferred dividends_study"]={en:["Preferred dividends"],sv:["Preferensutdelningar"]},e.exports["Preferred dividends paid_study"]={en:["Preferred dividends paid"],sv:["Utbetalda preferensutdelningar"]},e.exports["Preferred stock, carrying value_study"]={en:["Preferred stock, carrying value"],sv:["Preferensaktier, bokfört värde"]},e.exports["Prepaid expenses_study"]={en:["Prepaid expenses"],sv:["Förutbetalda kostnader"]},e.exports["Pretax equity in earnings_study"]={en:["Pretax equity in earnings"],sv:["Eget kapital i vinstmedel före skatt"]},e.exports["Pretax income_study"]={en:["Pretax income"],sv:["Inkomst före skatt"]},e.exports["Price earnings ratio forward_study"]={en:["Price earnings ratio forward"],sv:["Vinstmultipel (P/E-tal) framåt"]},e.exports["Price sales ratio forward_study"]={en:["Price sales ratio forward"],sv:["Pris-försäljningsförhållande framåt"]},e.exports["Price to tangible book ratio_study"]={en:["Price to tangible book ratio"],sv:["Pris i förhållande till materiell bokföring (PTBV)"]},e.exports["Provision for risks & charge_study"]={en:["Provision for risks & charge"],sv:["Avsättning för risker och kostnader"]},e.exports["Purchase/acquisition of business_study"]={en:["Purchase/acquisition of business"],sv:["Köp/förvärv av företag"]},e.exports["Purchase of investments_study"]={en:["Purchase of investments"],sv:["Inköp av investeringar"]},e.exports["Purchase/sale of business, net_study"]={en:["Purchase/sale of business, net"],sv:["Köp/försäljning av affärsverksamhet, netto"]},e.exports["Purchase/sale of investments, net_study"]={en:["Purchase/sale of investments, net"],sv:["Köp/försäljning av investeringar, netto"]},e.exports["Quality ratio_study"]={en:["Quality ratio"],sv:["Kvalitetskvot"]},e.exports["Quick ratio_study"]={en:["Quick ratio"],sv:["Kassalikviditet"]},e.exports["Reduction of long term debt_study"]={en:["Reduction of long term debt"],sv:["Minskning av långfristig skuld"]},e.exports["Repurchase of common & preferred stock_study"]={en:["Repurchase of common & preferred stock"],sv:["Återköp av stamaktier och preferensaktier"]},
e.exports["Research & development_study"]={en:["Research & development"],sv:["Forskning och utveckling"]},e.exports["Research & development to revenue ratio_study"]={en:["Research & development to revenue ratio"],sv:["Forskning och utveckling (FoU) i förhållande till omsättning"]},e.exports["Restructuring charge_study"]={en:["Restructuring charge"],sv:["Omstruktureringskostnader"]},e.exports["Retained earnings_study"]={en:["Retained earnings"],sv:["Balanserade vinstmedel"]},e.exports["Return on assets %_study"]={en:["Return on assets %"],sv:["Avkastning på tillgångar %"]},e.exports["Return on equity %_study"]={en:["Return on equity %"],sv:["Avkastning på eget kapital %"]},e.exports["Return on equity adjusted to book value %_study"]={en:["Return on equity adjusted to book value %"],sv:["Avkastning på eget kapital justerat till bokfört värde %"]},e.exports["Return on invested capital %_study"]={en:["Return on invested capital %"],sv:["Avkastning på investerat kapital %"]},e.exports["Return on tangible assets %_study"]={en:["Return on tangible assets %"],sv:["Avkastning på materiella tillgångar %"]},e.exports["Return on tangible equity %_study"]={en:["Return on tangible equity %"],sv:["Avkastning på materiellt eget kapital %"]},e.exports["Revenue estimates_study"]={en:["Revenue estimates"],sv:["Uppskattning av omsättning"]},e.exports["Revenue one year growth_study"]={en:["Revenue one year growth"],sv:["Omsättningstillväxt under ett år"]},e.exports["Revenue per employee_study"]={en:["Revenue per employee"],sv:["Omsättning per anställd"]},e.exports["Sale/maturity of investments_study"]={en:["Sale/maturity of investments"],sv:["Försäljning/förfall av investeringar"]},e.exports["Sale of common & preferred stock_study"]={en:["Sale of common & preferred stock"],sv:["Försäljning av stamaktier och preferensaktier"]},e.exports["Sale of fixed assets & businesses_study"]={en:["Sale of fixed assets & businesses"],sv:["Försäljning av anläggningstillgångar och företag"]},e.exports["Selling/general/admin expenses, other_study"]={en:["Selling/general/admin expenses, other"],sv:["Utgifter för försäljning/allmänt/administration, övrigt"]},e.exports["Selling/general/admin expenses, total_study"]={en:["Selling/general/admin expenses, total"],sv:["Försäljning/allmänna/administrativa utgifter, totalt"]},e.exports["Shareholders' equity_study"]={en:["Shareholders' equity"],sv:["Eget aktiekapital"]},e.exports["Shares buyback ratio %_study"]={en:["Shares buyback ratio %"],sv:["Återköpskvot av aktier %"]},e.exports["Short term debt_study"]={en:["Short term debt"],sv:["Kortfristiga skulder"]},e.exports["Short term debt excl. current portion of LT debt_study"]={en:["Short term debt excl. current portion of LT debt"],sv:["Kortfristiga skulder exkl. kortfristig andel av långfristiga skulder"]},e.exports["Short term investments_study"]={en:["Short term investments"],sv:["Kortfristiga investeringar"]},e.exports["Sloan ratio %_study"]={en:["Sloan ratio %"],sv:["Sloan-kvot %"]},e.exports["Springate score_study"]={en:["Springate score"],
sv:["Springate-poäng"]},e.exports["Sustainable growth rate_study"]={en:["Sustainable growth rate"],sv:["Hållbar tillväxttakt"]},e.exports["Tangible book value per share_study"]={en:["Tangible book value per share"],sv:["Materiellt bokfört värde per aktie"]},e.exports["Tangible common equity ratio_study"]={en:["Tangible common equity ratio"],sv:["Kvot för materiellt primärkapital"]},e.exports.Taxes_study={en:["Taxes"],sv:["Skatter"]},e.exports["Tobin's Q (approximate)_study"]={en:["Tobin's Q (approximate)"],sv:["Tobins Q (ungefärlig)"]},e.exports["Total assets_study"]={en:["Total assets"],sv:["Totala tillgångar"]},e.exports["Total cash dividends paid_study"]={en:["Total cash dividends paid"],sv:["Summa utbetald likvid aktieutdelning"]},e.exports["Total current assets_study"]={en:["Total current assets"],sv:["Alla nuvarande tillgångar totalt"]},e.exports["Total current liabilities_study"]={en:["Total current liabilities"],sv:["Alla nuvarande åtaganden"]},e.exports["Total debt_study"]={en:["Total debt"],sv:["Total skuld"]},e.exports["Total equity_study"]={en:["Total equity"],sv:["Totalt kapital"]},e.exports["Total inventory_study"]={en:["Total inventory"],sv:["Total inventory"]},e.exports["Total liabilities_study"]={en:["Total liabilities"],sv:["Totala skulder"]},e.exports["Total liabilities & shareholders' equities_study"]={en:["Total liabilities & shareholders' equities"],sv:["Totala skulder och eget aktiekapital"]},e.exports["Total non-current assets_study"]={en:["Total non-current assets"],sv:["Summa anläggningstillgångar"]},e.exports["Total non-current liabilities_study"]={en:["Total non-current liabilities"],sv:["Summa långfristiga skulder"]},e.exports["Total operating expenses_study"]={en:["Total operating expenses"],sv:["Totala rörelsekostnader"]},e.exports["Total receivables, net_study"]={en:["Total receivables, net"],sv:["Totala fordringar, netto"]},e.exports["Total revenue_study"]={en:["Total revenue"],sv:["Total omsättning"]},e.exports["Treasury stock - common_study"]={en:["Treasury stock - common"],sv:["Egna aktier - primära"]},e.exports["Unrealized gain/loss_study"]={en:["Unrealized gain/loss"],sv:["Orealiserad vinst/förlust"]},e.exports["Unusual income/expense_study"]={en:["Unusual income/expense"],sv:["Ovanliga inkomster/kostnader"]},e.exports["Zmijewski score_study"]={en:["Zmijewski score"],sv:["Zmijewski-poäng"]},e.exports["Valuation ratios_study"]={en:["Valuation ratios"],sv:["Värderingskvoter"]},e.exports["Profitability ratios_study"]={en:["Profitability ratios"],sv:["Lönsamhetskvoter"]},e.exports["Liquidity ratios_study"]={en:["Liquidity ratios"],sv:["Likviditetskvoter"]},e.exports["Solvency ratios_study"]={en:["Solvency ratios"],sv:["Kapitaltäckningskvot"]},e.exports["Key stats_study"]={en:["Key stats"],sv:["Nyckeltatistik"]},e.exports["Accumulation/Distribution_study"]={en:["Accumulation/Distribution"],sv:["Ackumulering Distribution"]},e.exports["Accumulative Swing Index_study"]={en:["Accumulative Swing Index"],sv:["Accumulative Swing Index"]},e.exports["Advance/Decline_study"]={
en:["Advance/Decline"],sv:["Uppgång/nedgång"]},e.exports["All Chart Patterns_study"]={en:["All Chart Patterns"],sv:["All Chart Patterns"]},e.exports["Arnaud Legoux Moving Average_study"]={en:["Arnaud Legoux Moving Average"],sv:["Arnaud Legoux Moving Average"]},e.exports.Aroon_study={en:["Aroon"],sv:["Aroon"]},e.exports.ASI_study={en:["ASI"],sv:["ASI"]},e.exports["Average Directional Index_study"]={en:["Average Directional Index"],sv:["Average Directional Index"]},e.exports["Average True Range_study"]={en:["Average True Range"],sv:["Average True Range"]},e.exports["Awesome Oscillator_study"]={en:["Awesome Oscillator"],sv:["Awesome Oscillator"]},e.exports["Balance of Power_study"]={en:["Balance of Power"],sv:["Maktbalansen"]},e.exports["Bollinger Bands %B_study"]={en:["Bollinger Bands %B"],sv:["Bollinger Bands %B"]},e.exports["Bollinger Bands Width_study"]={en:["Bollinger Bands Width"],sv:["Bollinger Bands Width"]},e.exports["Bollinger Bands_study"]={en:["Bollinger Bands"],sv:["Bollinger Bands"]},e.exports["Chaikin Money Flow_study"]={en:["Chaikin Money Flow"],sv:["Chaikin Money Flow"]},e.exports["Chaikin Oscillator_study"]={en:["Chaikin Oscillator"],sv:["Chaikin Oscillator"]},e.exports["Chande Kroll Stop_study"]={en:["Chande Kroll Stop"],sv:["Chande Kroll Stop"]},e.exports["Chande Momentum Oscillator_study"]={en:["Chande Momentum Oscillator"],sv:["Chande Momentum Oscillator"]},e.exports["Chop Zone_study"]={en:["Chop Zone"],sv:["Chop Zone"]},e.exports["Choppiness Index_study"]={en:["Choppiness Index"],sv:["Choppiness Index"]},e.exports["Commodity Channel Index_study"]={en:["Commodity Channel Index"],sv:["Råvarukanalsindex"]},e.exports["Connors RSI_study"]={en:["Connors RSI"],sv:["Connors RSI"]},e.exports["Coppock Curve_study"]={en:["Coppock Curve"],sv:["Coppock Curve"]},e.exports["Correlation Coefficient_study"]={en:["Correlation Coefficient"],sv:["Korrelationskoefficient"]},e.exports.CRSI_study={en:["CRSI"],sv:["CRSI"]},e.exports["Detrended Price Oscillator_study"]={en:["Detrended Price Oscillator"],sv:["Detrended Price Oscillator"]},e.exports["Directional Movement_study"]={en:["Directional Movement"],sv:["Directional Movement"]},e.exports["Donchian Channels_study"]={en:["Donchian Channels"],sv:["Donchian kanaler"]},e.exports["Double EMA_study"]={en:["Double EMA"],sv:["Dubbel EMA"]},e.exports["Ease Of Movement_study"]={en:["Ease Of Movement"],sv:["Rörligheten"]},e.exports["Elder Force Index_study"]={en:["Elder Force Index"],sv:["Elder's Force Index"]},e.exports["EMA Cross_study"]={en:["EMA Cross"],sv:["EMA Cross"]},e.exports.Envelopes_study={en:["Envelopes"],sv:["Kuvert"]},e.exports["Fisher Transform_study"]={en:["Fisher Transform"],sv:["Fisher Transform"]},e.exports["Fixed Range_study"]={en:["Fixed Range"],sv:["Fast intervall"]},e.exports["Fixed Range Volume Profile_study"]={en:["Fixed Range Volume Profile"],sv:["Volymprofil med fast intervall"]},e.exports["Guppy Multiple Moving Average_study"]={en:["Guppy Multiple Moving Average"],sv:["Guppy Multiple Moving Average (GMMA)"]},
e.exports["Historical Volatility_study"]={en:["Historical Volatility"],sv:["Historisk volatilitet"]},e.exports["Hull Moving Average_study"]={en:["Hull Moving Average"],sv:["Hull Moving Average"]},e.exports["Keltner Channels_study"]={en:["Keltner Channels"],sv:["Keltner Channels"]},e.exports["Klinger Oscillator_study"]={en:["Klinger Oscillator"],sv:["Klinger Oscillator"]},e.exports["Know Sure Thing_study"]={en:["Know Sure Thing"],sv:["Know Sure Thing (KST)"]},e.exports["Least Squares Moving Average_study"]={en:["Least Squares Moving Average"],sv:["Medelvärde ut minstakvadratmening"]},e.exports["Linear Regression Curve_study"]={en:["Linear Regression Curve"],sv:["Linjär regressionskurva"]},e.exports["MA Cross_study"]={en:["MA Cross"],sv:["MA-kors"]},e.exports["MA with EMA Cross_study"]={en:["MA with EMA Cross"],sv:["MA med EMA Cross"]},e.exports["MA/EMA Cross_study"]={en:["MA/EMA Cross"],sv:["MA/EMA Cross"]},e.exports.MACD_study={en:["MACD"],sv:["MACD"]},e.exports["Mass Index_study"]={en:["Mass Index"],sv:["Massindex"]},e.exports["McGinley Dynamic_study"]={en:["McGinley Dynamic"],sv:["McGinley Dynamic"]},e.exports.Median_study={en:["Median"],sv:["Median"]},e.exports.Momentum_study={en:["Momentum"],sv:["Momentum"]},e.exports["Money Flow_study"]={en:["Money Flow"],sv:["Pengaflöde"]},e.exports["Moving Average Channel_study"]={en:["Moving Average Channel"],sv:["Moving Average Channel"]},e.exports["Moving Average Exponential_study"]={en:["Moving Average Exponential"],sv:["Glidande medelvärde exponentiell"]},e.exports["Moving Average Weighted_study"]={en:["Moving Average Weighted"],sv:["Glidande medelvärde viktat"]},e.exports["Moving Average Simple_study"]={en:["Moving Average Simple"],sv:["Moving Average Simple"]},e.exports["Net Volume_study"]={en:["Net Volume"],sv:["nettovolym"]},e.exports["On Balance Volume_study"]={en:["On Balance Volume"],sv:["På balansvolym"]},e.exports["Parabolic SAR_study"]={en:["Parabolic SAR"],sv:["Parabolisk SAR"]},e.exports["Pivot Points Standard_study"]={en:["Pivot Points Standard"],sv:["Pivot Points Standard"]},e.exports["Periodic Volume Profile_study"]={en:["Periodic Volume Profile"],sv:["Periodisk volymprofil"]},e.exports["Price Channel_study"]={en:["Price Channel"],sv:["Price Channel"]},e.exports["Price Oscillator_study"]={en:["Price Oscillator"],sv:["Price Oscillator"]},e.exports["Price Volume Trend_study"]={en:["Price Volume Trend"],sv:["Trend för volymtrend"]},e.exports["Rate Of Change_study"]={en:["Rate Of Change"],sv:["Växlingskurs"]},e.exports["Relative Strength Index_study"]={en:["Relative Strength Index"],sv:["Relative Strength Index"]},e.exports["Relative Vigor Index_study"]={en:["Relative Vigor Index"],sv:["Relative Vigor Index"]},e.exports["Relative Volatility Index_study"]={en:["Relative Volatility Index"],sv:["Relative Volatility Index"]},e.exports["Relative Volume at Time_study"]={en:["Relative Volume at Time"],sv:["Relative Volume at Time"]},e.exports["Session Volume_study"]={en:["Session Volume"],sv:["Sessionsvolym"]},e.exports["Session Volume HD_study"]={
en:["Session Volume HD"],sv:["Sessionsvolym HD"]},e.exports["Session Volume Profile_study"]={en:["Session Volume Profile"],sv:["Sessionens volymprofil"]},e.exports["Session Volume Profile HD_study"]={en:["Session Volume Profile HD"],sv:["Sessionens volymprofil (HD)"]},e.exports["SMI Ergodic Indicator/Oscillator_study"]={en:["SMI Ergodic Indicator/Oscillator"],sv:["SMI Ergodic Indikator/Oscillator"]},e.exports["Smoothed Moving Average_study"]={en:["Smoothed Moving Average"],sv:["Smoothed Moving Average"]},e.exports["Stochastic Momentum Index_study"]={en:["Stochastic Momentum Index"],sv:["Stochastic Momentum Index"]},e.exports.Stoch_study={en:["Stoch"],sv:["Stoch"]},e.exports["Stochastic RSI_study"]={en:["Stochastic RSI"],sv:["Stochastic RSI"]},e.exports.Stochastic_study={en:["Stochastic"],sv:["Stochastic"]},e.exports["Time Weighted Average Price_study"]={en:["Time Weighted Average Price"],sv:["Time Weighted Average Price"]},e.exports["Triple EMA_study"]={en:["Triple EMA"],sv:["Triple EMA"]},e.exports.TRIX_study={en:["TRIX"],sv:["TRIX"]},e.exports["True Strength Indicator_study"]={en:["True Strength Indicator"],sv:["True Strength Indicator"]},e.exports["Ultimate Oscillator_study"]={en:["Ultimate Oscillator"],sv:["Ultimate Oscillator"]},e.exports["Visible Range_study"]={en:["Visible Range"],sv:["Synligt intervall"]},e.exports["Visible Range Volume Profile_study"]={en:["Visible Range Volume Profile"],sv:["Volymprofil för avgränsad period (VRVP)"]},e.exports["Volume Oscillator_study"]={en:["Volume Oscillator"],sv:["Volym Oscillator"]},e.exports.Volume_study={en:["Volume"],sv:["Volym"]},e.exports.Vol_study={en:["Vol"],sv:["Vol"]},e.exports["Vortex Indicator_study"]={en:["Vortex Indicator"],sv:["Virvelindikator"]},e.exports.VWAP_study={en:["VWAP"],sv:["VWAP"]},e.exports.VWMA_study={en:["VWMA"],sv:["VWMA"]},e.exports["Williams %R_study"]={en:["Williams %R"],sv:["Williams %R"]},e.exports["Williams Alligator_study"]={en:["Williams Alligator"],sv:["Williams Alligator"]},e.exports["Williams Fractal_study"]={en:["Williams Fractal"],sv:["Williams Fractal"]},e.exports["Zig Zag_study"]={en:["Zig Zag"],sv:["ZigZag-indikator"]},e.exports["24-hour Volume_study"]={en:["24-hour Volume"],sv:["24-timmars volym"]},e.exports["Ease of Movement_study"]={en:["Ease of Movement"],sv:["Rörligheten"]},e.exports["Elders Force Index_study"]={en:["Elders Force Index"],sv:["Elders Force Index"]},e.exports.Envelope_study={en:["Envelope"],sv:["Envelope"]},e.exports.Gaps_study={en:["Gaps"],sv:["Gaps"]},e.exports["Linear Regression Channel_study"]={en:["Linear Regression Channel"],sv:["Linjär regressionskanal"]},e.exports["Moving Average Ribbon_study"]={en:["Moving Average Ribbon"],sv:["Glidande medelvärde band"]},e.exports["Multi-Time Period Charts_study"]={en:["Multi-Time Period Charts"],sv:["Flerperiodsdiagram"]},e.exports["Open Interest_study"]={en:["Open Interest"],sv:["Öppen ränta"]},e.exports["Rob Booker - Intraday Pivot Points_study"]={en:["Rob Booker - Intraday Pivot Points"],sv:["Rob Booker – Intradag pivotpunkter"]},
e.exports["Rob Booker - Knoxville Divergence_study"]={en:["Rob Booker - Knoxville Divergence"],sv:["Rob Booker - Knoxville divergens"]},e.exports["Rob Booker - Missed Pivot Points_study"]={en:["Rob Booker - Missed Pivot Points"],sv:["Rob Booker – Missade pivotpunkter"]},e.exports["Rob Booker - Reversal_study"]={en:["Rob Booker - Reversal"],sv:["Rob Booker – vändning"]},e.exports["Rob Booker - Ziv Ghost Pivots_study"]={en:["Rob Booker - Ziv Ghost Pivots"],sv:["Rob Booker – Ziv Ghost-pivoter"]},e.exports.Supertrend_study={en:["Supertrend"],sv:["Supertrend"]},e.exports["Technical Ratings_study"]={en:["Technical Ratings"],sv:["Tekniska bedömningar"]},e.exports["True Strength Index_study"]={en:["True Strength Index"],sv:["True strength index"]},e.exports["Up/Down Volume_study"]={en:["Up/Down Volume"],sv:["Upp/Ner volym"]},e.exports["Visible Average Price_study"]={en:["Visible Average Price"],sv:["Synligt medelpris"]},e.exports["Williams Fractals_study"]={en:["Williams Fractals"],sv:["Williams Fractals"]},e.exports["Keltner Channels Strategy_study"]={en:["Keltner Channels Strategy"],sv:["Strategi för Keltner-kanalerna"]},e.exports["Rob Booker - ADX Breakout_study"]={en:["Rob Booker - ADX Breakout"],sv:["Rob Booker – ADX utbrott"]},e.exports["Supertrend Strategy_study"]={en:["Supertrend Strategy"],sv:["Supertrendsstrategi"]},e.exports["Technical Ratings Strategy_study"]={en:["Technical Ratings Strategy"],sv:["Teknisk värderingsstrategi"]},e.exports["Auto Anchored Volume Profile_study"]={en:["Auto Anchored Volume Profile"],sv:["Automatiskt förankrad volymprofil"]},e.exports["Auto Fib Extension_study"]={en:["Auto Fib Extension"],sv:["Auto Fib Extension"]},e.exports["Auto Fib Retracement_study"]={en:["Auto Fib Retracement"],sv:["Auto Fib-återgång"]},e.exports["Auto Pitchfork_study"]={en:["Auto Pitchfork"],sv:["Auto Pitchfork"]},e.exports["Bearish Flag Chart Pattern_study"]={en:["Bearish Flag Chart Pattern"],sv:["Bearish Flag Chart Pattern"]},e.exports["Bullish Flag Chart Pattern_study"]={en:["Bullish Flag Chart Pattern"],sv:["Bullish Flag Chart Pattern"]},e.exports["Bearish Pennant Chart Pattern_study"]={en:["Bearish Pennant Chart Pattern"],sv:["Bearish Pennant Chart Pattern"]},e.exports["Bullish Pennant Chart Pattern_study"]={en:["Bullish Pennant Chart Pattern"],sv:["Bullish Pennant Chart Pattern"]},e.exports["Double Bottom Chart Pattern_study"]={en:["Double Bottom Chart Pattern"],sv:["Dubbelbottnat diagrammönster"]},e.exports["Double Top Chart Pattern_study"]={en:["Double Top Chart Pattern"],sv:["Double Top Chart Pattern"]},e.exports["Elliott Wave Chart Pattern_study"]={en:["Elliott Wave Chart Pattern"],sv:["Elliott Waves diagrammönster"]},e.exports["Falling Wedge Chart Pattern_study"]={en:["Falling Wedge Chart Pattern"],sv:["Fallande kil diagrammönster"]},e.exports["Head And Shoulders Chart Pattern_study"]={sv:["Diagrammönster för huvud- och axlar"]},e.exports["Inverse Head And Shoulders Chart Pattern_study"]={sv:["Omvända diagrammönster för huvud- och axlar"]},e.exports["Rectangle Chart Pattern_study"]={
en:["Rectangle Chart Pattern"],sv:["Rectangle Chart Pattern"]},e.exports["Rising Wedge Chart Pattern_study"]={en:["Rising Wedge Chart Pattern"],sv:["Stigande kil diagrammönster"]},e.exports["Triangle Chart Pattern_study"]={en:["Triangle Chart Pattern"],sv:["Triangle Chart Pattern"]},e.exports["Triple Bottom Chart Pattern_study"]={en:["Triple Bottom Chart Pattern"],sv:["Trippelbottnat diagrammönster"]},e.exports["Triple Top Chart Pattern_study"]={en:["Triple Top Chart Pattern"],sv:["Triple Top Chart Pattern"]},e.exports["VWAP Auto Anchored_study"]={en:["VWAP Auto Anchored"],sv:["VWAP Auto Anchored"]},e.exports["*All Candlestick Patterns*_study"]={en:["*All Candlestick Patterns*"],sv:["*Alla Candlestick-mönster*"]},e.exports["Abandoned Baby - Bearish_study"]={en:["Abandoned Baby - Bearish"],sv:["Abandoned Baby - Bearish"]},e.exports["Abandoned Baby - Bullish_study"]={en:["Abandoned Baby - Bullish"],sv:["Abandoned Baby - Bullish"]},e.exports["Dark Cloud Cover - Bearish_study"]={en:["Dark Cloud Cover - Bearish"],sv:["Dark Cloud Cover - Bearish"]},e.exports["Doji Star - Bearish_study"]={en:["Doji Star - Bearish"],sv:["Doji Star - Bearish"]},e.exports["Doji Star - Bullish_study"]={en:["Doji Star - Bullish"],sv:["Doji Star - Bullish"]},e.exports["Downside Tasuki Gap - Bearish_study"]={en:["Downside Tasuki Gap - Bearish"],sv:["Downside Tasuki Gap - Bearish"]},e.exports["Dragonfly Doji - Bullish_study"]={en:["Dragonfly Doji - Bullish"],sv:["Dragonfly Doji - Bullish"]},e.exports["Engulfing - Bearish_study"]={en:["Engulfing - Bearish"],sv:["Engulfing - Bearish"]},e.exports["Engulfing - Bullish_study"]={en:["Engulfing - Bullish"],sv:["Engulfing - Bullish"]},e.exports["Evening Doji Star - Bearish_study"]={en:["Evening Doji Star - Bearish"],sv:["Evening Doji Star - Bearish"]},e.exports["Evening Star - Bearish_study"]={en:["Evening Star - Bearish"],sv:["Evening Star - Bearish"]},e.exports["Falling Three Methods - Bearish_study"]={en:["Falling Three Methods - Bearish"],sv:["Falling Three Methods - Bearish"]},e.exports["Falling Window - Bearish_study"]={en:["Falling Window - Bearish"],sv:["Falling Window - Bearish"]},e.exports["Gravestone Doji - Bearish_study"]={en:["Gravestone Doji - Bearish"],sv:["Gravestone Doji - Bearish"]},e.exports["Hammer - Bullish_study"]={en:["Hammer - Bullish"],sv:["Hammer - Bullish"]},e.exports["Hanging Man - Bearish_study"]={en:["Hanging Man - Bearish"],sv:["Hanging Man - Bearish"]},e.exports["Harami - Bearish_study"]={en:["Harami - Bearish"],sv:["Harami - Bearish"]},e.exports["Harami - Bullish_study"]={en:["Harami - Bullish"],sv:["Harami - Bullish"]},e.exports["Harami Cross - Bearish_study"]={en:["Harami Cross - Bearish"],sv:["Harami Cross - Bearish"]},e.exports["Harami Cross - Bullish_study"]={en:["Harami Cross - Bullish"],sv:["Harami Cross - Bullish"]},e.exports["Inverted Hammer - Bullish_study"]={en:["Inverted Hammer - Bullish"],sv:["Inverted Hammer - Bullish"]},e.exports["Kicking - Bearish_study"]={en:["Kicking - Bearish"],sv:["Kicking - Bearish"]},e.exports["Kicking - Bullish_study"]={
en:["Kicking - Bullish"],sv:["Kicking - Bullish"]},e.exports["Long Lower Shadow - Bullish_study"]={en:["Long Lower Shadow - Bullish"],sv:["Long Lower Shadow - Bullish"]},e.exports["Long Upper Shadow - Bearish_study"]={en:["Long Upper Shadow - Bearish"],sv:["Long Upper Shadow - Bearish"]},e.exports["Marubozu Black - Bearish_study"]={en:["Marubozu Black - Bearish"],sv:["Marubozu Black - Bearish"]},e.exports["Marubozu White - Bullish_study"]={en:["Marubozu White - Bullish"],sv:["Marubozu White - Bullish"]},e.exports["Morning Doji Star - Bullish_study"]={en:["Morning Doji Star - Bullish"],sv:["Morning Doji Star - Bullish"]},e.exports["Morning Star - Bullish_study"]={en:["Morning Star - Bullish"],sv:["Morning Star - Bullish"]},e.exports["On Neck - Bearish_study"]={en:["On Neck - Bearish"],sv:["On Neck - Bearish"]},e.exports["Piercing - Bullish_study"]={en:["Piercing - Bullish"],sv:["Piercing - Bullish"]},e.exports["Rising Three Methods - Bullish_study"]={en:["Rising Three Methods - Bullish"],sv:["Rising Three Methods - Bullish"]},e.exports["Rising Window - Bullish_study"]={en:["Rising Window - Bullish"],sv:["Rising Window - Bullish"]},e.exports["Shooting Star - Bearish_study"]={en:["Shooting Star - Bearish"],sv:["Shooting Star - Bearish"]},e.exports["Three Black Crows - Bearish_study"]={en:["Three Black Crows - Bearish"],sv:["Three Black Crows - Bearish"]},e.exports["Three White Soldiers - Bullish_study"]={en:["Three White Soldiers - Bullish"],sv:["Three White Soldiers - Bullish"]},e.exports["Tri-Star - Bearish_study"]={en:["Tri-Star - Bearish"],sv:["Tri-Star - Bearish"]},e.exports["Tri-Star - Bullish_study"]={en:["Tri-Star - Bullish"],sv:["Tri-Star - Bullish"]},e.exports["Tweezer Top - Bearish_study"]={en:["Tweezer Top - Bearish"],sv:["Tweezer Top - Bearish"]},e.exports["Upside Tasuki Gap - Bullish_study"]={en:["Upside Tasuki Gap - Bullish"],sv:["Upside Tasuki Gap - Bullish"]},e.exports.SuperTrend_study={en:["SuperTrend"],sv:["SuperTrend"]},e.exports["Average Price_study"]={en:["Average Price"],sv:["Genomsnittspris"]},e.exports["Typical Price_study"]={en:["Typical Price"],sv:["Typiskt pris"]},e.exports["Median Price_study"]={en:["Median Price"],sv:["Medianpris"]},e.exports["Money Flow Index_study"]={en:["Money Flow Index"],sv:["Pengaflödesindex"]},e.exports["Moving Average Double_study"]={en:["Moving Average Double"],sv:["Dubbelt glidande medelvärde"]},e.exports["Moving Average Triple_study"]={en:["Moving Average Triple"],sv:["Trippelt glidande medelvärde"]},e.exports["Moving Average Adaptive_study"]={en:["Moving Average Adaptive"],sv:["Adaptivt glidande medelvärde"]},e.exports["Moving Average Hamming_study"]={en:["Moving Average Hamming"],sv:["Extremt glidande medelvärde"]},e.exports["Moving Average Modified_study"]={en:["Moving Average Modified"],sv:["Modifierat glidande medelvärde"]},e.exports["Moving Average Multiple_study"]={en:["Moving Average Multiple"],sv:["Multipelt glidande medelvärde"]},e.exports["Linear Regression Slope_study"]={en:["Linear Regression Slope"],sv:["Linjär regressionskurva"]},
e.exports["Standard Error_study"]={en:["Standard Error"],sv:["Standardfel"]},e.exports["Standard Error Bands_study"]={en:["Standard Error Bands"],sv:["Värdeområden för standardfel"]},e.exports["Correlation - Log_study"]={en:["Correlation - Log"],sv:["Korrelation ‒ logg"]},e.exports["Standard Deviation_study"]={en:["Standard Deviation"],sv:["Standardavvikelse"]},e.exports["Chaikin Volatility_study"]={en:["Chaikin Volatility"],sv:["Chaikinvolatilitet"]},e.exports["Volatility Close-to-Close_study"]={en:["Volatility Close-to-Close"],sv:["Volatilitet stängning till stängning"]},e.exports["Volatility Zero Trend Close-to-Close_study"]={en:["Volatility Zero Trend Close-to-Close"],sv:["Volatilitet nollinje stängning till stängning"]},e.exports["Volatility O-H-L-C_study"]={en:["Volatility O-H-L-C"],sv:["Volatilitet OHLC"]},e.exports["Volatility Index_study"]={en:["Volatility Index"],sv:["Volatilitetsindex"]},e.exports["Trend Strength Index_study"]={en:["Trend Strength Index"],sv:["Trendstyrkaindex"]},e.exports["Majority Rule_study"]={en:["Majority Rule"],sv:["Majoritetsregel"]},e.exports["Advance Decline Line_study"]={en:["Advance Decline Line"],sv:["Avancerad avslutslinje"]},e.exports["Advance Decline Ratio_study"]={en:["Advance Decline Ratio"],sv:["Avancerad avslutsratio"]},e.exports["Advance/Decline Ratio (Bars)_study"]={en:["Advance/Decline Ratio (Bars)"],sv:["Avancerad/Avslutsratio (staplar)"]},e.exports["BarUpDn Strategy_study"]={en:["BarUpDn Strategy"],sv:["BarUpDn Strategi"]},e.exports["Bollinger Bands Strategy directed_study"]={en:["Bollinger Bands Strategy directed"],sv:["Bollinger Bands Strategy directed"]},e.exports["Bollinger Bands Strategy_study"]={en:["Bollinger Bands Strategy"],sv:["Bollinger Bands Strategy"]},e.exports.ChannelBreakOutStrategy_study={en:["ChannelBreakOutStrategy"],sv:["ChannelBreakOutStrategy"]},e.exports.Compare_study={en:["Compare"],sv:["Jämför"]},e.exports["Conditional Expressions_study"]={en:["Conditional Expressions"],sv:["Villkorliga uttryck"]},e.exports.ConnorsRSI_study={en:["ConnorsRSI"],sv:["ConnorsRSI"]},e.exports["Consecutive Up/Down Strategy_study"]={en:["Consecutive Up/Down Strategy"],sv:["Följande upp/ner strategi"]},e.exports["Cumulative Volume Index_study"]={en:["Cumulative Volume Index"],sv:["Cumulative Volume Index"]},e.exports["Divergence Indicator_study"]={en:["Divergence Indicator"],sv:["Divergenceindicator"]},e.exports["Greedy Strategy_study"]={en:["Greedy Strategy"],sv:["Girig strategi"]},e.exports["InSide Bar Strategy_study"]={en:["InSide Bar Strategy"],sv:["InSide Bar Strategy"]},e.exports["Keltner Channel Strategy_study"]={en:["Keltner Channel Strategy"],sv:["Keltner Channel-strategi"]},e.exports["Linear Regression_study"]={en:["Linear Regression"],sv:["Linjär regression"]},e.exports["MACD Strategy_study"]={en:["MACD Strategy"],sv:["MACD-strategi"]},e.exports["Momentum Strategy_study"]={en:["Momentum Strategy"],sv:["Momentumstrategi"]},e.exports["Moon Phases_study"]={en:["Moon Phases"],sv:["Månfaser"]},e.exports["Moving Average Convergence/Divergence_study"]={
en:["Moving Average Convergence/Divergence"],sv:["Moving Average Convergence/Divergence"]},e.exports["MovingAvg Cross_study"]={en:["MovingAvg Cross"],sv:["MovingAvg Cross"]},e.exports["MovingAvg2Line Cross_study"]={en:["MovingAvg2Line Cross"],sv:["MovingAvg2Line Cross"]},e.exports["OutSide Bar Strategy_study"]={en:["OutSide Bar Strategy"],sv:["OutSide stapel strategi"]},e.exports.Overlay_study={en:["Overlay"],sv:["Lägg över"]},e.exports["Parabolic SAR Strategy_study"]={en:["Parabolic SAR Strategy"],sv:["Parabolisk SAR-strategi"]},e.exports["Pivot Extension Strategy_study"]={en:["Pivot Extension Strategy"],sv:["Pivot Extension-strategi"]},e.exports["Pivot Points High Low_study"]={en:["Pivot Points High Low"],sv:["Pivot Points High Low"]},e.exports["Pivot Reversal Strategy_study"]={en:["Pivot Reversal Strategy"],sv:["Pivot Reversal strategi"]},e.exports["Price Channel Strategy_study"]={en:["Price Channel Strategy"],sv:["Priskanalsstrategi"]},e.exports["RSI Strategy_study"]={en:["RSI Strategy"],sv:["RSI-strategi"]},e.exports["SMI Ergodic Indicator_study"]={en:["SMI Ergodic Indicator"],sv:["SMI Ergodic Indicator"]},e.exports["SMI Ergodic Oscillator_study"]={en:["SMI Ergodic Oscillator"],sv:["SMI Ergodic Oscillator"]},e.exports["Stochastic Slow Strategy_study"]={en:["Stochastic Slow Strategy"],sv:["Stochastic Slow Strategy"]},e.exports["Volatility Stop_study"]={en:["Volatility Stop"],sv:["Volatilitetsstopp"]},e.exports["Volty Expan Close Strategy_study"]={en:["Volty Expan Close Strategy"],sv:["Volty Expan Close strategi"]},e.exports["Woodies CCI_study"]={en:["Woodies CCI"],sv:["Woodies CCI"]},e.exports["Anchored Volume Profile_study"]={en:["Anchored Volume Profile"],sv:["Anchored Volume Profile"]},e.exports["Trading Sessions_study"]={en:["Trading Sessions"]},e.exports["Cup and Handle Chart Pattern_study"]={en:["Cup and Handle Chart Pattern"]},e.exports["Inverted Cup and Handle Chart Pattern_study"]={en:["Inverted Cup and Handle Chart Pattern"]},e.exports["Head and Shoulders Chart Pattern_study"]={en:["Head and Shoulders Chart Pattern"]},e.exports["Inverted Head and Shoulders Chart Pattern_study"]={en:["Inverted Head and Shoulders Chart Pattern"]}},89633:e=>{e.exports={en:["Anchored Volume Profile"],sv:["Anchored Volume Profile"]}},25705:e=>{e.exports={en:["Fixed Range Volume Profile"],sv:["Volymprofil med fast intervall"]}},24261:e=>{e.exports={en:["Vol"],sv:["Vol"]}},51077:e=>{e.exports={en:["Minor"],sv:["Mindre"]}},922:e=>{e.exports={en:["Minute"],sv:["Minut"]}},91405:e=>{e.exports={en:["Text"],sv:["Text"]}},78972:e=>{e.exports={en:["Couldn't copy"],sv:["Kunde inte kopiera"]}},10615:e=>{e.exports={en:["Couldn't cut"],sv:["Kunde inte klippa ut"]}},81518:e=>{e.exports={en:["Couldn't paste"],sv:["Kunde inte klistra in"]}},83140:e=>{e.exports={en:["Countdown to bar close"],sv:["Nedräkning till stapelns stängning"]}},10871:e=>{e.exports={en:["Colombo"],sv:["Colombo"]}},55761:e=>{e.exports={en:["Columns"],sv:["Kolumner"]}},9818:e=>{e.exports={en:["Comment"],sv:["Kommentera"]}},53942:e=>{e.exports={
en:["Compare or Add Symbol"],sv:["Jämför eller lägg till en symbol"]}},12086:e=>{e.exports={en:["Compilation error"]}},48141:e=>{e.exports={en:["Confirm Inputs"],sv:["Bekräfta inputs"]}},38917:e=>{e.exports={en:["Copenhagen"],sv:["Köpenhamn"]}},49680:e=>{e.exports={en:["Copy"],sv:["Kopierad"]}},66134:e=>{e.exports={en:["Copy Chart Layout"],sv:["Kopiera diagramlayout"]}},63553:e=>{e.exports={en:["Copy price"],sv:["Kopiera pris"]}},65736:e=>{e.exports={en:["Cairo"],sv:["Kairo"]}},25381:e=>{e.exports={en:["Callout"],sv:["Callout"]}},45054:e=>{e.exports={en:["Candles"],sv:["Candlesticks"]}},30948:e=>{e.exports={en:["Caracas"],sv:["Caracas"]}},70409:e=>{e.exports={en:["Casablanca"],sv:["Casablanca"]}},37276:e=>{e.exports={en:["Change"],sv:["Förändring"]}},85124:e=>{e.exports={en:["Change Symbol"],sv:["Ändra tickersymbol"]}},2569:e=>{e.exports={en:["Change interval"],sv:["Ändra intervall"]}},9687:e=>{e.exports={en:["Change interval. Press number or comma"],sv:["Change interval. Press number or comma"]}},36332:e=>{e.exports={en:["Change symbol. Start typing symbol name"],sv:["Change symbol. Start typing symbol name"]}},48566:e=>{e.exports={en:["Change scale currency"]}},85110:e=>{e.exports={en:["Change scale unit"]}},56275:e=>{e.exports={en:["Chart #{index}"],sv:["Chart #{index}"]}},39950:e=>{e.exports={en:["Chart Properties"],sv:["Diagramegenskaper"]}},98856:e=>{e.exports={en:["Chart by TradingView"],sv:["Diagram av TradingView"]}},1136:e=>{e.exports={en:["Chart for {symbol}, {interval}"]}},69804:e=>{e.exports={en:["Chart image copied to clipboard {emoji}"],sv:["Diagrambilden kopierades till urklipp {emoji}"]}},6655:e=>{e.exports={en:["Chart image embed code copied to clipboard {emoji}"],sv:["Chart image embed code copied to clipboard {emoji}"]}},36549:e=>{e.exports={en:["Chatham Islands"],sv:["Chatham Islands"]}},72452:e=>{e.exports={en:["Chicago"],sv:["Chicago"]}},50349:e=>{e.exports={en:["Chongqing"],sv:["Chongqing"]}},91944:e=>{e.exports={en:["Circle"],sv:["Cirkel"]}},14985:e=>{e.exports={en:["Click to set a point"],sv:["Klicka för att sätta en markering."]}},12537:e=>{e.exports={en:["Clone"],sv:["Klona"]}},62578:e=>{e.exports={en:["Close"],sv:["Stäng"]}},264:e=>{e.exports={en:["Create limit order"],sv:["Skapa limiterad order"]}},6969:e=>{e.exports={en:["Cross"],sv:["Kryss"]}},74334:e=>{e.exports={en:["Cross Line"],sv:["Korsa linje"]}},59396:e=>{e.exports={en:["Currencies"],sv:["Valutor"]}},20177:e=>{e.exports={en:["Current interval and above"],sv:["Nuvarande intervall och uppåt"]}},494:e=>{e.exports={en:["Current interval and below"],sv:["Nuvarande intervall och nedåt"]}},60668:e=>{e.exports={en:["Current interval only"],sv:["Endast det här intervallet"]}},78609:e=>{e.exports={en:["Curve"],sv:["Kurva"]}},87380:e=>{e.exports={en:["Cycle"],sv:["Cykel"]}},84031:e=>{e.exports={en:["Cyclic Lines"],sv:["Cykliska linjer"]}},93191:e=>{e.exports={en:["Cypher Pattern"],sv:["Cypher Pattern-formation"]}},7219:e=>{e.exports={en:["A layout with that name already exists"],sv:["Det finns redan en layout med det namnet"]}},67635:e=>{
e.exports={en:["A layout with that name already exists. Do you want to overwrite it?"],sv:["En layout med det namnet finns redan. Vill du skriva över den?"]}},46712:e=>{e.exports={en:["ABCD Pattern"],sv:["ABCD-mönster"]}},36485:e=>{e.exports={en:["Amsterdam"],sv:["Amsterdam"]}},42630:e=>{e.exports={en:["Anchorage"],sv:["Ankring"]}},63209:e=>{e.exports={en:["Anchored Note"],sv:["Förankrad notering"]}},42669:e=>{e.exports={en:["Anchored Text"],sv:["Förankrad text"]}},84541:e=>{e.exports={en:["Anchored VWAP"],sv:["Ankrat VWAP"]}},77401:e=>{e.exports={en:["Access error"]}},46501:e=>{e.exports={en:["Add Symbol"],sv:["Lägg till symbol"]}},69709:e=>{e.exports={en:["Add alert on {title}"],sv:["Lägg till alarm för {title}"]}},89295:e=>{e.exports={en:["Add alert on {title} at {price}"],sv:["Add alert on {title} at {price}"]}},2439:e=>{e.exports={en:["Add financial metric for {instrumentName}"],sv:["Ändra finansiellt mätetal för {instrumentName}"]}},35088:e=>{e.exports={en:["Add indicator/strategy on {studyTitle}"],sv:["Lägg till indikator/strategi på {studyTitle}"]}},39873:e=>{e.exports={en:["Add text note for {symbol}"],sv:["Lägg till notering för {symbol}"]}},35679:e=>{e.exports={en:["Add this financial metric to entire layout"],sv:["Lägg till detta finansiella mått i hela layouten"]}},10996:e=>{e.exports={en:["Add this financial metric to favorites"]}},26090:e=>{e.exports={en:["Add this indicator to entire layout"],sv:["Lägg till den här indikatorn i hela layouten"]}},92957:e=>{e.exports={en:["Add this indicator to favorites"]}},95754:e=>{e.exports={en:["Add this strategy to entire layout"],sv:["Lägg till den här strategin till hela layouten"]}},39010:e=>{e.exports={en:["Add this symbol to entire layout"],sv:["Lägg till den här symbolen i hela layouten"]}},426:e=>{e.exports={en:["Adelaide"],sv:["Adelaide"]}},40452:e=>{e.exports={en:["Always invisible"],sv:["Alltid osynlig"]}},36299:e=>{e.exports={en:["Always visible"],sv:["Alltid synlig"]}},81442:e=>{e.exports={en:["All indicators and drawing tools"],sv:["Alla indikatorer och ritverktyg"]}},58026:e=>{e.exports={en:["All intervals"],sv:["Alla intervaller"]}},78358:e=>{e.exports={en:["Apply default"],sv:["Tillämpa standard"]}},22437:e=>{e.exports={en:["Apply these indicators to entire layout"],sv:["Använd dessa indikatorer till hela layouten"]}},27072:e=>{e.exports={en:["Apr"],sv:["Apr"]}},59324:e=>{e.exports={en:["Arc"],sv:["Båge"]}},34456:e=>{e.exports={en:["Area"],sv:["Area"]}},11858:e=>{e.exports={en:["Arrow"],sv:["Pil"]}},34247:e=>{e.exports={en:["Arrow Down"],sv:["Pil ned"]}},36352:e=>{e.exports={en:["Arrow Marker"],sv:["Pilmarkör"]}},73193:e=>{e.exports={en:["Arrow Mark Down"],sv:["Pil ned"]}},1949:e=>{e.exports={en:["Arrow Mark Left"],sv:["Pil åt vänster"]}},86275:e=>{e.exports={en:["Arrow Mark Right"],sv:["Pil åt höger"]}},62453:e=>{e.exports={en:["Arrow Mark Up"],sv:["Pil upp"]}},77231:e=>{e.exports={en:["Arrow Up"],sv:["Pil upp"]}},98128:e=>{e.exports={en:["Astana"]}},63627:e=>{e.exports={en:["Ashgabat"],sv:["Ashkhabad"]}},72445:e=>{e.exports={en:["At close"],
sv:["Vid stängning"]}},73702:e=>{e.exports={en:["Athens"],sv:["Aten"]}},21469:e=>{e.exports={en:["Auto"],sv:["Automatiskt"]}},24157:e=>{e.exports={en:["Auto (fits data to screen)"],sv:["Auto (anpassar datan till skärmen)"]}},46450:e=>{e.exports={en:["Aug"],sv:["Aug"]}},21841:e=>{e.exports={en:["Average close price label"],sv:["Medel stäng pris linje"]}},16138:e=>{e.exports={en:["Average close price line"],sv:["Genomsnittlig stängningsprislinje"]}},73025:e=>{e.exports={en:["Avg"],sv:["GAV"]}},73905:e=>{e.exports={en:["Bogota"],sv:["Bogota"]}},90594:e=>{e.exports={en:["Bahrain"],sv:["Bahrain"]}},70540:e=>{e.exports={en:["Balloon"],sv:["Ballong"]}},47045:e=>{e.exports={en:["Bangkok"],sv:["Bangkok"]}},76651:e=>{e.exports={en:["Bar Replay isn't available for this chart type. Do you want to exit Bar Replay?"],sv:["Bar Replay är inte tillgänglig för denna diagramtyp. Vill du gå ut från Bar Replay?"]}},68054:e=>{e.exports={en:["Bar Replay isn't available for this time interval. Do you want to exit Bar Replay?"],sv:["Återspelning av staplar är inte tillgängligt för detta tidsintervall. Vill du avsluta återspelning av staplar?"]}},27377:e=>{e.exports={en:["Bars"],sv:["Bars"]}},81994:e=>{e.exports={en:["Bars Pattern"],sv:["Stapelmönster"]}},59213:e=>{e.exports={en:["Baseline"],sv:["Baslinje"]}},71797:e=>{e.exports={en:["Belgrade"],sv:["Belgrad"]}},64313:e=>{e.exports={en:["Berlin"],sv:["Berlin"]}},43539:e=>{e.exports={en:["Brush"],sv:["Pensel"]}},91499:e=>{e.exports={en:["Brussels"],sv:["Brussels"]}},70876:e=>{e.exports={en:["Bratislava"],sv:["Bratislava"]}},55481:e=>{e.exports={en:["Bring forward"],sv:["Ta fram"]}},17293:e=>{e.exports={en:["Bring to front"],sv:["Lägg överst"]}},79336:e=>{e.exports={en:["Brisbane"],sv:["Brisbane"]}},33672:e=>{e.exports={en:["Bucharest"],sv:["Bukarest"]}},20313:e=>{e.exports={en:["Budapest"],sv:["Budapest"]}},25282:e=>{e.exports={en:["Buenos Aires"],sv:["Buenos Aires"]}},46768:e=>{e.exports={en:["By TradingView"],sv:["Av Tradingview"]}},54280:e=>{e.exports={en:["Go to date"],sv:["Gå till datum"]}},74975:e=>{e.exports={en:["Go to {lineToolName}"],sv:["Gå till {lineToolName}"]}},15462:e=>{e.exports={en:["Got it"],sv:["Jag förstår"]}},47460:e=>{e.exports={en:["Gann Box"],sv:["Gann Box"]}},48683:e=>{e.exports={en:["Gann Fan"],sv:["Gann Fan"]}},44763:e=>{e.exports={en:["Gann Square"],sv:["Gann Square"]}},60707:e=>{e.exports={en:["Gann Square Fixed"],sv:["Gann Square fixerad"]}},46808:e=>{e.exports={en:["Ghost Feed"],sv:["Spökdata"]}},57726:e=>{e.exports={en:["Grand supercycle"],sv:["Grand Supercycle"]}},34914:e=>{e.exports={en:["Do you really want to delete Study Template '{name}' ?"],sv:["Vill du verkligen ta bort studiemall '{name}' ?"]}},77125:e=>{e.exports={en:["Double Curve"],sv:["Dubbel kurva"]}},9430:e=>{e.exports={en:["Double-click any edge to reset layout grid"],sv:["Dubbelklicka på en kant för att återställa layoutnätet"]}},75296:e=>{e.exports={en:["Double-click to finish Path"],sv:["Dubbelklicka för att avsluta Väg"]}},17409:e=>{e.exports={en:["Double-click to finish Polyline"],
sv:["Dubbelklicka för att avsluta Polyline"]}},57131:e=>{e.exports={en:["Data Provided by"],sv:["Data tillhandahållen av"]}},62154:e=>{e.exports={en:["Date"],sv:["Datum"]}},85444:e=>{e.exports={en:["Date Range"],sv:["Datumintervall"]}},47017:e=>{e.exports={en:["Date and Price Range"],sv:["Datum- och prisintervall"]}},32084:e=>{e.exports={en:["Dec"],sv:["Dec"]}},23403:e=>{e.exports={en:["Degree"],sv:["Grad"]}},27358:e=>{e.exports={en:["Denver"],sv:["Denver"]}},24959:e=>{e.exports={en:["Dhaka"],sv:["Dhaka"]}},15179:e=>{e.exports={en:["Diamond"],sv:["Diamant"]}},91544:e=>{e.exports={en:["Disjoint Channel"],sv:["Ta isär kanal"]}},70132:e=>{e.exports={en:["Displacement"],sv:["Förskjutning"]}},93864:e=>{e.exports={en:["Drawings toolbar"],sv:["Ritverktygfält"]}},96890:e=>{e.exports={en:["Draw Horizontal Line at"],sv:["Rita horisontallinje på"]}},23650:e=>{e.exports={en:["Dubai"],sv:["Dubai"]}},79716:e=>{e.exports={en:["Dublin"],sv:["Dublin"]}},73456:e=>{e.exports={en:["Emoji"],sv:["Emoji"]}},9541:e=>{e.exports={en:["Enter a new chart layout name"],sv:["Ange ett nytt layoutnamn för diagrammet"]}},80943:e=>{e.exports={en:["Elliott Correction Wave (ABC)"],sv:["Elliott Correction Wave (ABC)"]}},75112:e=>{e.exports={en:["Elliott Double Combo Wave (WXY)"],sv:["Elliott Double Combo Wave (WXY)"]}},61114:e=>{e.exports={en:["Elliott Impulse Wave (12345)"],sv:["Elliott Impulse Wave (12345)"]}},72359:e=>{e.exports={en:["Elliott Triangle Wave (ABCDE)"],sv:["Elliott Triangle Wave (ABCDE)"]}},76129:e=>{e.exports={en:["Elliott Triple Combo Wave (WXYXZ)"],sv:["Elliott Triple Combo Wave (WXYXZ)"]}},78996:e=>{e.exports={en:["Ellipse"],sv:["Ellips"]}},52788:e=>{e.exports={en:["Extended Line"],sv:["Förlängd linje"]}},86905:e=>{e.exports={en:["Exchange"],sv:["Börs"]}},19271:e=>{e.exports={en:["Existing pane above"],sv:["Befintlig panel ovan"]}},46545:e=>{e.exports={en:["Existing pane below"],sv:["Befintlig panel under"]}},20138:e=>{e.exports={en:["Forecast"],sv:["Prognos"]}},2507:e=>{e.exports={en:["Feb"],sv:["Feb"]}},59005:e=>{e.exports={en:["Fib Channel"],sv:["Fibonaccikanaler"]}},82330:e=>{e.exports={en:["Fib Circles"],sv:["Fibonaccicirklar"]}},55986:e=>{e.exports={en:["Fib Retracement"],sv:["Fibonaccirekryl"]}},33880:e=>{e.exports={en:["Fib Speed Resistance Arcs"],sv:["Fib Speed Resistance Arcs"]}},2395:e=>{e.exports={en:["Fib Speed Resistance Fan"],sv:["Fib Speed Resistance Fan"]}},39014:e=>{e.exports={en:["Fib Spiral"],sv:["Fibonaccispiral"]}},30622:e=>{e.exports={en:["Fib Time Zone"],sv:["Fibonacci tidszoner"]}},85042:e=>{e.exports={en:["Fib Wedge"],sv:["Fib Wedge"]}},33885:e=>{e.exports={en:["Flag"],sv:["Flagga"]}},14600:e=>{e.exports={en:["Flag Mark"],sv:["Flaggmarkering"]}},45051:e=>{e.exports={en:["Flat Top/Bottom"],sv:["Platt topp/botten"]}},63271:e=>{e.exports={en:["Flipped"],sv:["Omvänd"]}},39643:e=>{e.exports={en:["Fraction part is invalid."],sv:["Fraktionsdelen är ogiltig"]}},24077:e=>{e.exports={en:["Fundamental studies are no longer available on charts"],sv:["Fundamentala studier är inte längre tillgängliga på diagram"]}},
31561:e=>{e.exports={en:["Kolkata"],sv:["Calcutta"]}},54533:e=>{e.exports={en:["Kathmandu"],sv:["Kathmandu"]}},83490:e=>{e.exports={en:["Kagi"],sv:["Kagi"]}},70913:e=>{e.exports={en:["Karachi"],sv:["Karachi"]}},76614:e=>{e.exports={en:["Kuwait"],sv:["Kuwait"]}},38561:e=>{e.exports={en:["Kuala Lumpur"]}},99906:e=>{e.exports={en:["HLC area"],sv:["HLC-område"]}},34491:e=>{e.exports={en:["Ho Chi Minh"],sv:["Ho Chi Minh"]}},13459:e=>{e.exports={en:["Hollow candles"],sv:["Ihåliga candlesticks"]}},48861:e=>{e.exports={en:["Hong Kong"],sv:["Hongkong"]}},79668:e=>{e.exports={en:["Honolulu"],sv:["Honolulu"]}},21795:e=>{e.exports={en:["Horizontal Line"],sv:["Horisontell linje"]}},25487:e=>{e.exports={en:["Horizontal Ray"],sv:["Horisontell stråle"]}},21928:e=>{e.exports={en:["Head and Shoulders"],sv:["Huvudskuldraformation"]}},63876:e=>{e.exports={en:["Heikin Ashi"],sv:["Heikin Ashi"]}},48203:e=>{e.exports={en:["Helsinki"],sv:["Helsingfors"]}},27298:e=>{e.exports={en:["Hide"],sv:["Dölj"]}},47074:e=>{e.exports={en:["Hide all"],sv:["Dölj alla"]}},52563:e=>{e.exports={en:["Hide all drawings"],sv:["Göm alla ritverktyg"]}},90763:e=>{e.exports={en:["Hide all drawings and indicators"],sv:["Dölj alla ritningar och indikatorer"]}},18216:e=>{e.exports={en:["Hide all drawings, indicators, positions & orders"],sv:["Dölj alla ritningar, indikatorer, positioner och ordrar"]}},78525:e=>{e.exports={en:["Hide all indicators"],sv:["Dölj alla indikatorer"]}},42164:e=>{e.exports={en:["Hide all positions & orders"],sv:["Dölj alla ordrar och positioner"]}},3217:e=>{e.exports={en:["Hide drawings"],sv:["Dölj ritningar"]}},97878:e=>{e.exports={en:["Hide events on chart"],sv:["Dölj händelser på diagrammet"]}},72351:e=>{e.exports={en:["Hide indicators"],sv:["Dölj indikatorer"]}},28345:e=>{e.exports={en:["Hide marks on bars"],sv:["Dölj markeringar på staplar"]}},92226:e=>{e.exports={en:["Hide positions & orders"],sv:["Dölj ordrar och positioner"]}},78254:e=>{e.exports={en:["High"],sv:["Högsta"]}},98236:e=>{e.exports={en:["High-low"],sv:["Högsta-lägsta"]}},99479:e=>{e.exports={en:["High and low price labels"],sv:["Hög- och lågprisetiketter"]}},33766:e=>{e.exports={en:["High and low price lines"],sv:["Hög- och lågprislinjer"]}},69476:e=>{e.exports={en:["Highlighter"],sv:["Highlighter"]}},72819:e=>{e.exports={en:["Histogram is too large, please increase {boldHighlightStart}Row Size{boldHighlightEnd} in the settings window."]}},94966:e=>{e.exports={en:["Histogram is too large, please increase {boldHighlightStart}Ticks Per Row{boldHighlightEnd} in the settings window."]}},66751:e=>{e.exports={en:["Histogram is too large, please reduce {boldHighlightStart}Row Size{boldHighlightEnd} in the settings window."]}},68065:e=>{e.exports={en:["Image"],sv:["Bild"]}},80185:e=>{e.exports={en:["Intervals less than {resolution} are not supported for {ticker}."]}},10268:e=>{e.exports={en:["Intermediate"],sv:["Mellan"]}},14285:e=>{e.exports={en:["Invalid Symbol"],sv:["Ogiltig symbol"]}},52969:e=>{e.exports={en:["Invalid symbol"],sv:["Ogiltig tickersymbol"]}},37189:e=>{
e.exports={en:["Invert scale"],sv:["Invertera skala"]}},89999:e=>{e.exports={en:["Indexed to 100"],sv:["Indexeras till 100"]}},46850:e=>{e.exports={en:["Indicators value labels"],sv:["Indikatorer och värdeetiketter"]}},54418:e=>{e.exports={en:["Indicators name labels"],sv:["Indikatorers namnetiketter"]}},40490:e=>{e.exports={en:["Indicators, Metrics and Strategies. Press slash"],sv:["Indicators, Metrics and Strategies. Press slash"]}},15992:e=>{e.exports={en:["Info Line"],sv:["Informationslinje"]}},87829:e=>{e.exports={en:["Insert indicator"],sv:["Lägg till indikator"]}},91612:e=>{e.exports={en:["Inside"],sv:["Inuti"]}},41686:e=>{e.exports={en:["Inside Pitchfork"],sv:["Inuti Pitchfork"]}},37913:e=>{e.exports={en:["Icon"],sv:["Ikon"]}},78326:e=>{e.exports={en:["Istanbul"],sv:["Istanbul"]}},39585:e=>{e.exports={en:["Johannesburg"],sv:["Johannesburg"]}},14995:e=>{e.exports={en:["Jakarta"],sv:["Jakarta"]}},62310:e=>{e.exports={en:["Jan"],sv:["Jan"]}},36057:e=>{e.exports={en:["Jerusalem"],sv:["Jerusalem"]}},53786:e=>{e.exports={en:["Jul"],sv:["Jul"]}},429:e=>{e.exports={en:["Jun"],sv:["Jun"]}},67560:e=>{e.exports={en:["Juneau"],sv:["Juneau"]}},62329:e=>{e.exports={en:["On the left"],sv:["Till vänster"]}},55813:e=>{e.exports={en:["On the right"],sv:["Till höger"]}},64818:e=>{e.exports={en:["Only {availableResolutions} intervals are supported for {ticker}."]}},21064:e=>{e.exports={en:["Oops!"],sv:["Oj då!"]}},51221:e=>{e.exports={en:["Object Tree"],sv:["Objektträd"]}},12179:e=>{e.exports={en:["Oct"],sv:["Okt"]}},16610:e=>{e.exports={en:["Open"],sv:["Öppning"]}},46005:e=>{e.exports={en:["Original"],sv:["Original"]}},75722:e=>{e.exports={en:["Oslo"],sv:["Oslo"]}},65318:e=>{e.exports={en:["Low"],sv:["Lägsta"]}},55382:e=>{e.exports={en:["Load layout. Press period"],sv:["Load layout. Press period"]}},5837:e=>{e.exports={en:["Lock"],sv:["Lås"]}},79777:e=>{e.exports={en:["Lock/unlock"],sv:["Lås/lås upp"]}},66005:e=>{e.exports={en:["Lock vertical cursor line by time"],sv:["Lås vertikal markörlinje utifrån tid"]}},14017:e=>{e.exports={en:["Lock price to bar ratio"],sv:["Lås pris till stepelförhållande"]}},16170:e=>{e.exports={en:["Logarithmic"],sv:["Logaritmisk"]}},19439:e=>{e.exports={en:["London"],sv:["London"]}},74832:e=>{e.exports={en:["Long Position"],sv:["Lång position"]}},28733:e=>{e.exports={en:["Los Angeles"],sv:["Los Angeles"]}},85924:e=>{e.exports={en:["Label Down"],sv:["Etikett ned"]}},52402:e=>{e.exports={en:["Label Up"],sv:["Etikett upp"]}},5119:e=>{e.exports={en:["Labels"],sv:["Etiketter"]}},19931:e=>{e.exports={en:["Lagos"],sv:["Lagos"]}},63815:e=>{e.exports={en:["Last day change"]}},59444:e=>{e.exports={en:["Lima"],sv:["Lima"]}},3554:e=>{e.exports={en:["Line"],sv:["Linje"]}},9394:e=>{e.exports={en:["Line with markers"],sv:["Linje med markörer"]}},43588:e=>{e.exports={en:["Line break"],sv:["Radbrytning"]}},56982:e=>{e.exports={en:["Lines"],sv:["Linjer"]}},37367:e=>{e.exports={en:["Link to the chart image copied to clipboard {emoji}"],sv:["Länk till den diagrambild som kopierats till urklipp {emoji}"]}},53375:e=>{
e.exports={en:["Lisbon"],sv:["Lisbon"]}},81038:e=>{e.exports={en:["Luxembourg"],sv:["Luxemburg"]}},60663:e=>{e.exports={en:["Move the point to position the anchor then tap to place"],sv:["Flytta punkten för att positionera ankaret, och tryck sedan för att placera det"]}},35049:e=>{e.exports={en:["Move to"],sv:["Flytta till"]}},26493:e=>{e.exports={en:["Move scale to left"],sv:["Flytta skalan åt vänster"]}},40789:e=>{e.exports={en:["Move scale to right"],sv:["Flytta skalan åt höger"]}},70382:e=>{e.exports={en:["Modified Schiff"],sv:["Modified Schiff"]}},57681:e=>{e.exports={en:["Modified Schiff Pitchfork"],sv:["Modified Schiff Pitchfork"]}},93907:e=>{e.exports={en:["More settings"]}},64039:e=>{e.exports={en:["Moscow"],sv:["Moskva"]}},52066:e=>{e.exports={en:["Madrid"],sv:["Madrid"]}},38365:e=>{e.exports={en:["Malta"],sv:["Malta"]}},48991:e=>{e.exports={en:["Manila"],sv:["Manilla"]}},92767:e=>{e.exports={en:["Mar"],sv:["Mar"]}},73332:e=>{e.exports={en:["Mexico City"],sv:["Mexico City"]}},88314:e=>{e.exports={en:["Merge all scales into one"],sv:["Slå samman alla skalor till en"]}},54215:e=>{e.exports={en:["Mixed"],sv:["Blandad"]}},24866:e=>{e.exports={en:["Micro"],sv:["Mikro"]}},87957:e=>{e.exports={en:["Millennium"],sv:["Millennium"]}},14724:e=>{e.exports={en:["Minuette"],sv:["Minuette"]}},78273:e=>{e.exports={en:["Minuscule"],sv:["Liten bokstav"]}},28941:e=>{e.exports={en:["Mirrored"],sv:["Speglad"]}},9865:e=>{e.exports={en:["Muscat"],sv:["Muscat"]}},96935:e=>{e.exports={en:["N/A"],sv:["Ej tillgänglig"]}},36252:e=>{e.exports={en:["No data here"],sv:["Ingen data här"]}},11254:e=>{e.exports={en:["No scale (fullscreen)"],sv:["Ingen skala (helskärm)"]}},9140:e=>{e.exports={en:["No sync"],sv:["Ingen synk"]}},50910:e=>{e.exports={en:["No volume data"],sv:["Ingen volymdata"]}},94389:e=>{e.exports={en:["Note"],sv:["Notering"]}},26899:e=>{e.exports={en:["Nov"],sv:["Nov"]}},67891:e=>{e.exports={en:["Norfolk Island"],sv:["Norfolk Island"]}},40977:e=>{e.exports={en:["Nairobi"],sv:["Nairobi"]}},40544:e=>{e.exports={en:["New York"],sv:["New York"]}},66103:e=>{e.exports={en:["New Zealand"],sv:["Nya Zeeland"]}},15512:e=>{e.exports={en:["New pane above"],sv:["Ny ruta ovanför"]}},52160:e=>{e.exports={en:["New pane below"],sv:["Ny ruta under"]}},94600:e=>{e.exports={en:["Nicosia"],sv:["Nicosia"]}},73013:e=>{e.exports={en:["Something went wrong"],sv:["Något gick fel"]}},4509:e=>{e.exports={en:["Something went wrong. Please try again later."],sv:["Något blev fel. Vänligen försök senare."]}},43047:e=>{e.exports={en:["Save New Chart Layout"],sv:["Spara ny diagramlayout"]}},76266:e=>{e.exports={en:["Save as"],sv:["Spara som"]}},55502:e=>{e.exports={en:["San Salvador"],sv:["San Salvador"]}},30231:e=>{e.exports={en:["Santiago"],sv:["Santiago"]}},91912:e=>{e.exports={en:["Sao Paulo"],sv:["Sao Paulo"]}},43931:e=>{e.exports={en:["Scale currency"]}},43758:e=>{e.exports={en:["Scale price chart only"],sv:["Skalpris endast för diagram"]}},40012:e=>{e.exports={en:["Scale unit"]}},69904:e=>{e.exports={en:["Schiff"],sv:["Schiff"]}},42608:e=>{e.exports={
en:["Schiff Pitchfork"],sv:["Schiff Pitchfork"]}},76078:e=>{e.exports={en:["Script may be not updated if you leave the page."],sv:["Det finns risk att skript inte sparas om du lämnar sidan."]}},32514:e=>{e.exports={en:["Settings"],sv:["Inställningar"]}},70784:e=>{e.exports={en:["Second fraction part is invalid."],sv:["Andra fraktionsdelen är ogiltig"]}},75594:e=>{e.exports={en:["Security info"]}},21973:e=>{e.exports={en:["Send to back"],sv:["Skicka tillbaka"]}},71179:e=>{e.exports={en:["Send backward"],sv:["Skicka bakåt"]}},26820:e=>{e.exports={en:["Seoul"],sv:["Seoul"]}},6816:e=>{e.exports={en:["Sep"],sv:["Sep"]}},94031:e=>{e.exports={en:["Session"],sv:["Session"]}},83298:e=>{e.exports={en:["Session volume profile"]}},66707:e=>{e.exports={en:["Session breaks"],sv:["Sessionsindelare"]}},1852:e=>{e.exports={en:["Shanghai"],sv:["Shanghai"]}},8075:e=>{e.exports={en:["Short Position"],sv:["Kort position"]}},98334:e=>{e.exports={en:["Show"],sv:["Visa"]}},85891:e=>{e.exports={en:["Show all drawings"],sv:["Visa alla ritningar"]}},25881:e=>{e.exports={en:["Show all drawings and indicators"],sv:["Visa alla ritningar och indikatorer"]}},86738:e=>{e.exports={en:["Show all drawings, indicators, positions & orders"],sv:["Visa alla ritningar, indikatorer, positioner och ordrar"]}},98753:e=>{e.exports={en:["Show all indicators"],sv:["Visa alla indikatorer"]}},55418:e=>{e.exports={en:["Show all ideas"],sv:["Visa alla idéer"]}},20506:e=>{e.exports={en:["Show all positions & orders"],sv:["Visa alla ordrar och positioner"]}},33158:e=>{e.exports={en:["Show continuous contract switch"],sv:["Visa kontinuerligt kontraktsöverföring"]}},81465:e=>{e.exports={en:["Show contract expiration"],sv:["Visa att kontraktet löper ut"]}},29449:e=>{e.exports={en:["Show dividends"],sv:["Visa utdelningar"]}},37113:e=>{e.exports={en:["Show earnings"],sv:["Visa intäkter"]}},10261:e=>{e.exports={en:["Show ideas of followed users"],sv:["Visa idéer från följande användare"]}},68112:e=>{e.exports={en:["Show latest news and Minds"],sv:["Visa senaste nyheter och Tankar"]}},44020:e=>{e.exports={en:["Show my ideas only"],sv:["Visa endast mina idéer"]}},50849:e=>{e.exports={en:["Show splits"],sv:["Visa aktiesplits"]}},67751:e=>{e.exports={en:["Signpost"],sv:["Vägvisare"]}},77377:e=>{e.exports={en:["Singapore"],sv:["Singapore"]}},39090:e=>{e.exports={en:["Sine Line"],sv:["Sinuslinje"]}},66205:e=>{e.exports={en:["Square"],sv:["Ruta"]}},86146:e=>{e.exports={en:["Studies limit exceeded: {number} studies per layout.\nPlease, remove some studies."],sv:["Studiegräns överskridits: {number} studier per layout.\nVänligen ta bort några studier."]}},92516:e=>{e.exports={en:["Style"],sv:["Stil"]}},61507:e=>{e.exports={en:["Stack on the left"],sv:["Stapel till vänster"]}},97800:e=>{e.exports={en:["Stack on the right"],sv:["Stapel till höger"]}},85166:e=>{e.exports={en:["Start using keyboard navigation mode. Press {shortcut}"],sv:["Start using keyboard navigation mode. Press {shortcut}"]}},4035:e=>{e.exports={en:["Stay in drawing mode"],sv:["Stanna kvar i ritläge"]}},69217:e=>{
e.exports={en:["Step line"],sv:["Steglinje"]}},43114:e=>{e.exports={en:["Sticker"],sv:["Klistermärke"]}},86716:e=>{e.exports={en:["Stockholm"],sv:["Stockholm"]}},1145:e=>{e.exports={en:["Submicro"],sv:["Submicro"]}},63375:e=>{e.exports={en:["Submillennium"],sv:["Submillennium"]}},30585:e=>{e.exports={en:["Subminuette"],sv:["Subminuette"]}},67948:e=>{e.exports={en:["Supercycle"],sv:["Supercykel"]}},3348:e=>{e.exports={en:["Supermillennium"],sv:["Supermillennium"]}},18905:e=>{e.exports={en:["Switch to {resolution}"]}},31622:e=>{e.exports={en:["Sydney"],sv:["Sydney"]}},70963:e=>{e.exports={en:["Symbol Error"],sv:["Symbolfel"]}},32390:e=>{e.exports={en:["Symbol name label"],sv:["Etikett för symbolen namn"]}},10127:e=>{e.exports={en:["Symbol last price label"],sv:["Etikett för symbolen senaste värdet"]}},39079:e=>{e.exports={en:["Sync globally"],sv:["Synk globalt"]}},46607:e=>{e.exports={en:["Sync in layout"],sv:["Synkronisera till alla diagram"]}},76519:e=>{e.exports={en:["Point & figure"],sv:["Point & Figure"]}},39949:e=>{e.exports={en:["Polyline"],sv:["Polyline"]}},371:e=>{e.exports={en:["Path"],sv:["Väg"]}},59256:e=>{e.exports={en:["Parallel Channel"],sv:["Parallell kanal"]}},61879:e=>{e.exports={en:["Paris"],sv:["Paris"]}},35140:e=>{e.exports={en:["Paste"],sv:["Klistra in"]}},6919:e=>{e.exports={en:["Percent"],sv:["Procent"]}},24436:e=>{e.exports={en:["Perth"],sv:["Perth"]}},14055:e=>{e.exports={en:["Phoenix"],sv:["Phoenix"]}},34156:e=>{e.exports={en:["Pitchfan"],sv:["Pitchfan"]}},19634:e=>{e.exports={en:["Pitchfork"],sv:["Pitchfork"]}},33110:e=>{e.exports={en:["Pin to new left scale"],sv:["Fäst vid ny vänster skala"]}},28280:e=>{e.exports={en:["Pin to new right scale"],sv:["Fäst vid ny höger skala"]}},14115:e=>{e.exports={en:["Pin to left scale"],sv:["Fäst vid vänster skala"]}},72046:e=>{e.exports={en:["Pin to left scale (hidden)"],sv:["Fäst vid vänster skala (dold)"]}},81054:e=>{e.exports={en:["Pin to right scale"],sv:["Fäst på höger skala"]}},16986:e=>{e.exports={en:["Pin to right scale (hidden)"],sv:["Fäst vid höger skala (dold)"]}},60035:e=>{e.exports={en:["Pin to scale (now left)"],sv:["Fäst till skala (nu vänster)"]}},94210:e=>{e.exports={en:["Pin to scale (now no scale)"],sv:["Fäst vid skala (nu ingen skala)"]}},10761:e=>{e.exports={en:["Pin to scale (now right)"],sv:["Fäst till skala (nu höger)"]}},76150:e=>{e.exports={en:["Pin to scale (now {label})"],sv:["Fäst vid skala (nu {label})"]}},29436:e=>{e.exports={en:["Pin to scale {label}"],sv:["Fäst vid skalan {label}"]}},2165:e=>{e.exports={en:["Pin to scale {label} (hidden)"],sv:["Fäst vid skalan {label} (dold)"]}},90095:e=>{e.exports={en:["Pinned to left scale"],sv:["Fäst vid vänster skala"]}},32538:e=>{e.exports={en:["Pinned to left scale (hidden)"],sv:["Fäst vid vänster skala (dold)"]}},44579:e=>{e.exports={en:["Pinned to right scale"],sv:["Fäst vid höger skala"]}},94559:e=>{e.exports={en:["Pinned to right scale (hidden)"],sv:["Fäst vid höger skala (dold)"]}},12645:e=>{e.exports={en:["Pinned to scale {label}"],sv:["Fäst vid skalan {label}"]}},3564:e=>{
e.exports={en:["Pinned to scale {label} (hidden)"],sv:["Fäst vid skalan {label} (dold)"]}},71566:e=>{e.exports={en:["Plus button"],sv:["Plusknapp"]}},28298:e=>{e.exports={en:["Please give us a clipboard writing permission in your browser or press {keystroke}"],sv:["Var god tillåt urklipp i din webbläsare eller tryck på {keystroke}"]}},81248:e=>{e.exports={en:["Prague"],sv:["Prague"]}},81712:e=>{e.exports={en:["Press and hold {key} while zooming to maintain the chart position"],sv:["Tryck och håll {key}, medan du zoomar för att behålla positionen på diagrammet"]}},91282:e=>{e.exports={en:["Price Label"],sv:["Prisetikett"]}},97512:e=>{e.exports={en:["Price Note"],sv:["Pris anmärkning"]}},68941:e=>{e.exports={en:["Price Range"],sv:["Prisintervall"]}},66123:e=>{e.exports={en:["Price format is invalid."],sv:["Prisformat är ogiltigt"]}},72926:e=>{e.exports={en:["Price line"],sv:["Prislinje"]}},59189:e=>{e.exports={en:["Primary"],sv:["Primär"]}},75747:e=>{e.exports={en:["Projection"],sv:["Projektion"]}},55801:e=>{e.exports={en:["Published on {customer}, {date}"],sv:["Publicerad på {customer}, {date}"]}},28756:e=>{e.exports={en:["Qatar"],sv:["Qatar"]}},57959:e=>{e.exports={en:["Quick search. Press {shortcut}"],sv:["Quick search. Press {shortcut}"]}},56820:e=>{e.exports={en:["Rotated Rectangle"],sv:["Roterad rektangel"]}},52961:e=>{e.exports={en:["Rome"],sv:["Rom"]}},50318:e=>{e.exports={en:["Ray"],sv:["Utdragen linje"]}},55169:e=>{e.exports={en:["Range"],sv:["Intervall"]}},13386:e=>{e.exports={en:["Reykjavik"],sv:["Reykjavik"]}},26001:e=>{e.exports={en:["Rectangle"],sv:["Rektangel"]}},48236:e=>{e.exports={en:["Redo"],sv:["Gör om"]}},2460:e=>{e.exports={en:["Regression Trend"],sv:["Regressionstrend"]}},67410:e=>{e.exports={en:["Remove"],sv:["Ta bort"]}},96374:e=>{e.exports={en:["Remove drawings"],sv:["Ta bort ritningar"]}},99984:e=>{e.exports={en:["Remove indicators"],sv:["Ta bort indikatorer"]}},3061:e=>{e.exports={en:["Remove this financial metric from favorites"]}},58764:e=>{e.exports={en:["Remove this indicator from favorites"]}},22584:e=>{e.exports={en:["Rename Chart Layout"],sv:["Byt namn på diagramlayout"]}},88130:e=>{e.exports={en:["Renko"],sv:["Renko"]}},75246:e=>{e.exports={en:["Reset chart view"],sv:["Återställ diagramvy"]}},88853:e=>{e.exports={en:["Reset points"],sv:["Återställ punkter"]}},15332:e=>{e.exports={en:["Reset price scale"],sv:["Återställ prisskala"]}},54170:e=>{e.exports={en:["Reset time scale"],sv:["Återställ tidsskala"]}},37974:e=>{e.exports={en:["Riyadh"],sv:["Riyadh"]}},94022:e=>{e.exports={en:["Riga"],sv:["Riga"]}},60630:e=>{e.exports={en:["Runtime error"]}},66719:e=>{e.exports={en:["Warning"],sv:["Varning"]}},5959:e=>{e.exports={en:["Warsaw"],sv:["Warszawa"]}},94465:e=>{e.exports={en:["Toggle auto scale"],sv:["Toggle auto scale"]}},46992:e=>{e.exports={en:["Toggle log scale"],sv:["Toggle log scale"]}},98549:e=>{e.exports={en:["Tokelau"],sv:["Tokelau"]}},69122:e=>{e.exports={en:["Tokyo"],sv:["Tokyo"]}},10095:e=>{e.exports={en:["Toronto"],sv:["Toronto"]}},11034:e=>{e.exports={en:["Taipei"],
sv:["Taipei"]}},79995:e=>{e.exports={en:["Tallinn"],sv:["Tallinn"]}},6686:e=>{e.exports={en:["Tehran"],sv:["Teheran"]}},93553:e=>{e.exports={en:["Template"],sv:["Mall"]}},81657:e=>{e.exports={en:["The data vendor doesn't provide volume data for this symbol."],sv:["Dataleverantören tillhandahåller inte volymuppgifter för denna symbol."]}},49947:e=>{e.exports={en:["The publication preview could not be loaded. Please disable your browser extensions and try again."],sv:["Förhandsvisningen för publicering kunde inte laddas. Vänligen avaktivera dina webbläsartillägg och prova igen."]}},59519:e=>{e.exports={en:["This indicator cannot be applied to another indicator."],sv:["Denna indikator kan inte användas samtidigt som andra indikatorer används"]}},18260:e=>{e.exports={en:["This script contains an error. Please contact its author."],sv:["This script contains an error. Please contact its author."]}},76989:e=>{e.exports={en:["This script is invite-only. To request access, please contact its author."],sv:["Detta skript är endast tillgängligt för inbjudna. För att begära åtkomst, vänligen kontakta författaren."]}},47773:e=>{e.exports={en:["This symbol is only available on {linkStart}TradingView{linkEnd}."],sv:["Symbolen är tillgänglig endast på {linkStart}TradingView{linkEnd}."]}},46982:e=>{e.exports={en:["Three Drives Pattern"],sv:["Three Drives-mönstret"]}},24821:e=>{e.exports={en:["Ticks"],sv:["Bockar"]}},80254:e=>{e.exports={en:["Tick-based intervals are not available for {ticker}."]}},12806:e=>{e.exports={en:["Time"],sv:["Tid"]}},20909:e=>{e.exports={en:["Time zone"],sv:["Tidszon"]}},46852:e=>{e.exports={en:["Time Cycles"],sv:["Tidscykler"]}},17809:e=>{e.exports={en:["Time Price Opportunity"]}},66823:e=>{e.exports={en:["Trade"],sv:["Handla"]}},7697:e=>{e.exports={en:["TradingView is interactive and has commands to use with a screen reader. The following is a list of keyboard commands available to interact on the platform"],sv:["TradingView is interactive and has commands to use with a screen reader. The following is a list of keyboard commands available to interact on the platform"]}},35757:e=>{e.exports={en:["Trend Angle"],sv:["Trendvinkel"]}},97339:e=>{e.exports={en:["Trend Line"],sv:["Trendlinje"]}},80583:e=>{e.exports={en:["Trend-Based Fib Extension"],sv:["Trendbaserad Fib Extension"]}},72159:e=>{e.exports={en:["Trend-Based Fib Time"],sv:["Trendbaserad Fib Time"]}},1671:e=>{e.exports={en:["Triangle"],sv:["Triangel"]}},76152:e=>{e.exports={en:["Triangle Down"],sv:["Triangel ned"]}},90148:e=>{e.exports={en:["Triangle Pattern"],sv:["Triangelformation"]}},21236:e=>{e.exports={en:["Triangle Up"],sv:["Triangel upp"]}},21007:e=>{e.exports={en:["Tunis"],sv:["Tunisiska"]}},1833:e=>{e.exports={en:["UTC"],sv:["UTC"]}},14804:e=>{e.exports={en:["Undo"],sv:["Ångra"]}},15432:e=>{e.exports={en:["Units"],sv:["Enheter"]}},11768:e=>{e.exports={en:["Unknown error"],sv:["Okänt fel"]}},99894:e=>{e.exports={en:["Unlock"],sv:["Lås upp"]}},75546:e=>{e.exports={en:["Unsupported interval"]}},8580:e=>{e.exports={en:["User-defined error"]}},
40693:e=>{e.exports={en:["Volume Profile Fixed Range"],sv:["Volymprofil med fast intervall"]}},39903:e=>{e.exports={en:["Volume Profile indicator available only on our upgraded plans."],sv:["Volymprofil-indikator finns bara tillgängligt via våra upgraderade abonnemang."]}},93722:e=>{e.exports={en:["Volume candles"]}},69156:e=>{e.exports={en:["Volume data is not provided in BIST MIXED data plan."],sv:["Volume data is not provided in BIST MIXED data plan."]}},92763:e=>{e.exports={en:["Volume footprint"]}},32838:e=>{e.exports={en:["Vancouver"],sv:["Vancouver"]}},29535:e=>{e.exports={en:["Vertical Line"],sv:["Vertikal linje"]}},23160:e=>{e.exports={en:["Vienna"],sv:["Vienna"]}},60534:e=>{e.exports={en:["Vilnius"],sv:["Vilnius"]}},40091:e=>{e.exports={en:["Visibility"],sv:["Synlighet"]}},54853:e=>{e.exports={en:["Visibility on intervals"],sv:["Synlighet på interaller"]}},10309:e=>{e.exports={en:["Visible on mouse over"],sv:["Synligt när musen är ovanför"]}},4077:e=>{e.exports={en:["Visual order"],sv:["Synlig order"]}},11316:e=>{e.exports={en:["X Cross"],sv:["X Cross"]}},42231:e=>{e.exports={en:["XABCD Pattern"],sv:["XABCD Pattern-formation"]}},25059:e=>{e.exports={en:["You cannot see this pivot timeframe on this resolution"],sv:["Du kan inte se denna pivot-tidsram på den här upplösningen"]}},53168:e=>{e.exports={en:["Yangon"],sv:["Yangon"]}},62859:e=>{e.exports={en:["Zurich"],sv:["Zürich"]}},47977:e=>{e.exports={en:["change Elliott degree"],sv:["ändra Elliottgrader"]}},61557:e=>{e.exports={en:["change no overlapping labels"],sv:["ändra etiketter som inte överlappar varandra"]}},76852:e=>{e.exports={en:["change average close price label visibility"],sv:["ändring medel stäng pris etikett synlighet"]}},1022:e=>{e.exports={en:["change average close price line visibility"],sv:["ändring medel stäng pris linje synlighet"]}},69362:e=>{e.exports={en:["change bid and ask labels visibility"],sv:["ändra synligheten av etiketterna för köp- och säljkurs"]}},52919:e=>{e.exports={en:["change bid and ask lines visibility"],sv:["ändra synligheten av linjerna för köp- och säljkurs"]}},32302:e=>{e.exports={en:["change currency"],sv:["ändra valuta"]}},68846:e=>{e.exports={en:["change chart layout to {title}"],sv:["ändra diagramlayout till {title}"]}},18867:e=>{e.exports={en:["change continuous contract switch visibility"],sv:["ändra synligheten av kontinuerligt kontraktsbyte"]}},39383:e=>{e.exports={en:["change countdown to bar close visibility"],sv:["Ändra synligheten av nedräkningen vid stängning av baren"]}},16979:e=>{e.exports={en:["change date range"],sv:["ändra dataintervall"]}},53929:e=>{e.exports={en:["change dividends visibility"],sv:["ändra utdelningars synlighet"]}},6119:e=>{e.exports={en:["change events visibility on chart"],sv:["ändra händelsers synlighet på diagrammet"]}},6819:e=>{e.exports={en:["change earnings visibility"],sv:["ändra resultatets synlighet"]}},85532:e=>{e.exports={en:["change futures contract expiration visibility"],sv:["ändra synligheten av terminskontraktens förfall"]}},24226:e=>{e.exports={
en:["change high and low price labels visibility"],sv:["ändra synlighet för hög- och lågprisetiketter"]}},80692:e=>{e.exports={en:["change high and low price lines visibility"],sv:["ändra synlighet för hög- och lågprislinjer"]}},24893:e=>{e.exports={en:["change indicators name labels visibility"],sv:["ändra synlighet för indikatorers namnetiketter"]}},64729:e=>{e.exports={en:["change indicators value labels visibility"],sv:["ändra synlighet för indikatorers värdeetiketter"]}},50243:e=>{e.exports={en:["change latest news and Minds visibility"],sv:["ändra senaste nytt och Tankars synlighet"]}},88849:e=>{e.exports={en:["change linking group"],sv:["ändra länkgrupp"]}},14691:e=>{e.exports={en:["change pane height"],sv:["ändra höjden på rutan"]}},96379:e=>{e.exports={en:["change plus button visibility"],sv:["ändra plusknappens synlighet"]}},30870:e=>{e.exports={en:["change pre/post market price label visibility"],sv:["ändra synligheten av marknadsprisetiketterna avseende för- och eftermarknaden"]}},11718:e=>{e.exports={en:["change pre/post market price line visibility"],sv:["ändra synligheten av marknadsprislinjerna avseende för- och eftermarknaden"]}},58419:e=>{e.exports={en:["change previous close price line visibility"],sv:["ändra synligheten av stängningen för föregående prislinje"]}},8662:e=>{e.exports={en:["change price line visibility"],sv:["ändra synlighet för prislinje"]}},2509:e=>{e.exports={en:["change price to bar ratio"],sv:["ändra priser till stapelförhållanden"]}},32829:e=>{e.exports={en:["change resolution"],sv:["Ändra upplösning"]}},35400:e=>{e.exports={en:["change symbol"],sv:["Ändra tickersymbol"]}},73357:e=>{e.exports={en:["change symbol labels visibility"],sv:["ändra synlighet för symboletiketter"]}},67453:e=>{e.exports={en:["change symbol last value visibility"],sv:["ändra synligheten för det senaste symbolvärdet"]}},4729:e=>{e.exports={en:["change symbol previous close value visibility"],sv:["ändra synligheten av symbolen för det föregående stängningsvärdet"]}},87041:e=>{e.exports={en:["change session"],sv:["Byt session"]}},38413:e=>{e.exports={en:["change session breaks visibility"],sv:["ändra synlighet för sessionsavbrott"]}},49965:e=>{e.exports={en:["change series style"],sv:["byt seriestil"]}},47474:e=>{e.exports={en:["change splits visibility"],sv:["ändra synligheten för splits"]}},20137:e=>{e.exports={en:["change timezone"],sv:["Ändra tidszon"]}},85975:e=>{e.exports={en:["change unit"],sv:["ändra enhet"]}},1924:e=>{e.exports={en:["change visibility"],sv:["Ändra synlighet"]}},84331:e=>{e.exports={en:["change visibility at current interval"],sv:["ändra synlighet vid nuvarande intervall"]}},45800:e=>{e.exports={en:["change visibility at current interval and above"],sv:["ändra synlighet vid nuvarande intervall och uppåt"]}},75645:e=>{e.exports={en:["change visibility at current interval and below"],sv:["ändra synlighet vid nuvarande intervall och nedåt"]}},57916:e=>{e.exports={en:["change visibility at all intervals"],sv:["ändra synlighet för alla intervall"]}},98463:e=>{e.exports={
en:["change {title} style"],sv:["ändra stil för {title}"]}},57122:e=>{e.exports={en:["change {title} text"]}},63058:e=>{e.exports={en:["change {pointIndex} point"],sv:["ändra {pointIndex} punkt"]}},94566:e=>{e.exports={en:["charts by TradingView"],sv:["diagram från TradingView"]}},32943:e=>{e.exports={en:["clone line tools"],sv:["Klona linjeverktyg"]}},46219:e=>{e.exports={en:["create line tools group"],sv:["Skapa linjeverktygsgrupp"]}},95394:e=>{e.exports={en:["create line tools group from selection"],sv:["Skapa linjeverktygsgrupp från urval"]}},12898:e=>{e.exports={en:["create {tool}"],sv:["skapa{tool}"]}},94227:e=>{e.exports={en:["cut sources"],sv:["klipp källor"]}},11500:e=>{e.exports={en:["cut {title}"],sv:["klipp {title}"]}},12570:e=>{e.exports={en:["add line tool {lineTool} to group {name}"],sv:["Lägg till linjeverktyg {lineTool} till grupp {name}"]}},21162:e=>{e.exports={en:["add line tool(s) to group {group}"],sv:["Lägg till linjeverktyg till grupp {group}"]}},67608:e=>{e.exports={en:["add this financial metric to entire layout"],sv:["Lägg till detta finansiella mått i hela layouten"]}},96677:e=>{e.exports={en:["add this indicator to entire layout"],sv:["Lägg till den här indikatorn i hela layouten"]}},58156:e=>{e.exports={en:["add this strategy to entire layout"],sv:["Lägg till den här strategin till hela layouten"]}},79290:e=>{e.exports={en:["add this symbol to entire layout"],sv:["Lägg till den här symbolen i hela layouten"]}},68231:e=>{e.exports={en:["apply chart theme"],sv:["tillämpa diagramtema"]}},99551:e=>{e.exports={en:["apply all chart properties"],sv:["tillämpa alla diagramegenskaper"]}},89720:e=>{e.exports={en:["apply drawing template"],sv:["Tillämpa ritningsmall"]}},27851:e=>{e.exports={en:["apply factory defaults to selected sources"],sv:["tillämpa fabriksinställningar för valda källor"]}},70507:e=>{e.exports={en:["apply indicators to entire layout"],sv:["använd indikatorerna för hela layouten"]}},69604:e=>{e.exports={en:["apply study template {template}"],sv:["Använd studiemall {template}"]}},86708:e=>{e.exports={en:["apply toolbars theme"],sv:["tillämpa tema för verktygsfält"]}},1979:e=>{e.exports={en:["bring group {title} forward"],sv:["skicka fram grupp för {title}"]}},53159:e=>{e.exports={en:["bring {title} to front"],sv:["flytta fram {title}"]}},41966:e=>{e.exports={en:["bring {title} forward"],sv:["För {title} framåt"]}},44676:e=>{e.exports={en:["by TradingView"],sv:["Av TradingView"]}},58850:e=>{e.exports={en:["date range lock"],sv:["lås för datumintervall"]}},35111:e=>{e.exports={en:["erase level line"],sv:["radera nivålinje"]}},99395:e=>{e.exports={en:["exclude line tools from group {group}"],sv:["Uteslut linjeverktyg från grupp {group}"]}},82349:e=>{e.exports={en:["flip bars pattern"],sv:["flippa stapelmönster"]}},13017:e=>{e.exports={en:["hide {title}"],sv:["Dölj {title}"]}},62249:e=>{e.exports={en:["hide marks on bars"],sv:["Dölj markeringar på staplar"]}},56558:e=>{e.exports={en:["interval lock"],sv:["intervallås"]}},6830:e=>{e.exports={en:["invert scale"],sv:["Invertera skala"]}
},48818:e=>{e.exports={en:["insert {title}"],sv:["infoga {title}"]}},56307:e=>{e.exports={en:["insert {title} after {targetTitle}"],sv:["infoga {title} efter {targetTitle}"]}},32960:e=>{e.exports={en:["insert {title} after {target}"],sv:["Infoga {title} efter {target}"]}},57106:e=>{e.exports={en:["insert {title} before {target}"],sv:["Infoga {title} före {target}"]}},46229:e=>{e.exports={en:["insert {title} before {targetTitle}"],sv:["Infoga {title} före {targetTitle}"]}},43364:e=>{e.exports={en:["load default drawing template"],sv:["ladda standardmall för ritning"]}},62011:e=>{e.exports={en:["loading..."],sv:["laddar..."]}},76104:e=>{e.exports={en:["lock {title}"],sv:["Lås {title}"]}},20453:e=>{e.exports={en:["lock group {group}"],sv:["lås grupp {group}"]}},18942:e=>{e.exports={en:["lock objects"],sv:["lås objekt"]}},98277:e=>{e.exports={en:["move"],sv:["flytta"]}},58228:e=>{e.exports={en:["move {title} to new left scale"],sv:["flytta {title} till ny skala till vänster"]}},77482:e=>{e.exports={en:["move {title} to new right scale"],sv:["flytta{title} till ny högerskala"]}},64077:e=>{e.exports={en:["move all scales to left"],sv:["Flytta alla skalor åt vänster"]}},19013:e=>{e.exports={en:["move all scales to right"],sv:["Flytta alla skalor åt höger"]}},52510:e=>{e.exports={en:["move drawing(s)"],sv:["Flytta ritning(ar)"]}},79209:e=>{e.exports={en:["move left"],sv:["flytta till vänster"]}},60114:e=>{e.exports={en:["move right"],sv:["flytta till höger"]}},44854:e=>{e.exports={en:["move scale"],sv:["Flytta skala"]}},10625:e=>{e.exports={en:["make {title} no scale (Full screen)"],sv:["Gör {title} utan skala (helskärm)"]}},76709:e=>{e.exports={en:["make group {group} invisible"],sv:["Gör grupp {group} osynlig"]}},45987:e=>{e.exports={en:["make group {group} visible"],sv:["Gör grupp {group} synlig"]}},78055:e=>{e.exports={en:["merge down"],sv:["slå ihop"]}},41866:e=>{e.exports={en:["merge to pane"],sv:["sammanfoga till rutan"]}},52458:e=>{e.exports={en:["merge up"],sv:["slå ihop"]}},20965:e=>{e.exports={en:["mirror bars pattern"],sv:["spegla stapelmönster"]}},90091:e=>{e.exports={en:["n/a"],sv:["n/a"]}},94981:e=>{e.exports={en:["scale price"],sv:["prisskala"]}},63796:e=>{e.exports={en:["scale price chart only"],sv:["Skalpris endast för diagram"]}},70771:e=>{e.exports={en:["scale time"],sv:["skaltid"]}},42070:e=>{e.exports={en:["scroll"],sv:["Skrolla"]}},87840:e=>{e.exports={en:["scroll time"],sv:["skrolltid"]}},82241:e=>{e.exports={en:["set price scale selection strategy to {title}"],sv:["ställ in strategi för prisskaleselektion till {title}"]}},40962:e=>{e.exports={en:["send {title} backward"],sv:["Skicka {title} bakåt"]}},5005:e=>{e.exports={en:["send {title} to back"],sv:["skicka bak {title}"]}},69546:e=>{e.exports={en:["send group {title} backward"],sv:["skicka tillbaka grupp för {title}"]}},63934:e=>{e.exports={en:["share line tools globally"],sv:["dela linjeverktyg globalt"]}},90221:e=>{e.exports={en:["share line tools in layout"],sv:["dela linjeverktyg i layout"]}},13336:e=>{e.exports={en:["show all ideas"],
sv:["visa alla idéer"]}},91395:e=>{e.exports={en:["show ideas of followed users"],sv:["visa idéer från följda användare"]}},57460:e=>{e.exports={en:["show my ideas only"],sv:["Visa endast mina idéer"]}},4114:e=>{e.exports={en:["stay in drawing mode"],sv:["stanna kvar i designläge"]}},3350:e=>{e.exports={en:["stop syncing drawing"],sv:["avbryt synkronisering av ritning"]}},49183:e=>{e.exports={en:["stop syncing line tool(s)"],sv:["sluta synkronisera linjeverktyg"]}},53278:e=>{e.exports={en:["symbol lock"],sv:["symbollås"]}},91677:e=>{e.exports={en:["sync time"],sv:["synka tid"]}},3140:e=>{e.exports={en:["powered by"],sv:["drivs av"]}},92800:e=>{e.exports={en:["powered by TradingView"],sv:["drivs av TradingView"]}},62192:e=>{e.exports={en:["paste drawing"],sv:["klistra in ritning"]}},1064:e=>{e.exports={en:["paste indicator"],sv:["klistra in indikator"]}},57010:e=>{e.exports={en:["paste {title}"],sv:["klista in {title}"]}},78690:e=>{e.exports={en:["pin to left scale"],sv:["fäst vid vänster skala"]}},7495:e=>{e.exports={en:["pin to right scale"],sv:["Fäst vid höger skala"]}},81566:e=>{e.exports={en:["pin to scale {label}"],sv:["Fäst vid skalan {label}"]}},2618:e=>{e.exports={en:["rearrange panes"],sv:["omdisponera rutor"]}},43172:e=>{e.exports={en:["remove all studies"],sv:["Ta bort alla studier"]}},56253:e=>{e.exports={en:["remove all studies and drawing tools"],sv:["Ta bort alla studier och ritverktyg"]}},27171:e=>{e.exports={en:["remove deselected empty line tools"],sv:["ta bort avmarkerade tomma linjeverktyg"]}},30538:e=>{e.exports={en:["remove drawings"],sv:["Ta bort ritningar"]}},1193:e=>{e.exports={en:["remove drawings group"],sv:["ta bort ritningsgrupp"]}},38199:e=>{e.exports={en:["remove line data sources"],sv:["ta bort källor för linjedata"]}},93333:e=>{e.exports={en:["remove pane"],sv:["ta bort platta"]}},94543:e=>{e.exports={en:["remove {title}"],sv:["ta bort {title}"]}},41430:e=>{e.exports={en:["removing line tools group {name}"],sv:["Tar bort linjeverktygsgrupp {name}"]}},80491:e=>{e.exports={en:["rename group {group} to {newName}"],sv:["Döp om grupp {group} till {newName}"]}},85366:e=>{e.exports={en:["reset layout sizes"],sv:["återställ layoutens storlek"]}},3323:e=>{e.exports={en:["reset scales"],sv:["återställ skalor"]}},17336:e=>{e.exports={en:["reset time scale"],sv:["Återställ tidsskala"]}},47418:e=>{e.exports={en:["resize layout"],sv:["ändra layoutens storlek"]}},85815:e=>{e.exports={en:["restore defaults"],sv:["återställ standardvärdena"]}},96881:e=>{e.exports={en:["restore study defaults"]}},42240:e=>{e.exports={en:["toggle auto scale"],sv:["växla skala automatiskt"]}},46054:e=>{e.exports={en:["toggle collapsed pane state"],sv:["toggle collapsed pane state"]}},24736:e=>{e.exports={en:["toggle indexed to 100 scale"],sv:["växla indexerad till 100 skala"]}},49695:e=>{e.exports={en:["toggle lock scale"],sv:["växla låskalan"]}},49403:e=>{e.exports={en:["toggle log scale"],sv:["växla logaritmskala"]}},98994:e=>{e.exports={en:["toggle percentage scale"],sv:["Bryt procentskalan"]}},80688:e=>{e.exports={
en:["toggle regular scale"],sv:["växla normalskala"]}},46807:e=>{e.exports={en:["track time"],sv:["spåra tid"]}},8040:e=>{e.exports={en:["turn line tools sharing off"],sv:["stäng av delning av linjeverktyg"]}},23230:e=>{e.exports={en:["unlock objects"],sv:["lås upp objekt"]}},74590:e=>{e.exports={en:["unlock group {group}"],sv:["Lås upp grupp {group}"]}},12525:e=>{e.exports={en:["unlock {title}"],sv:["Lås upp {title}"]}},81576:e=>{e.exports={en:["unmerge to new bottom pane"],sv:["ta bort sammanslagning med ny bottenplatta"]}},79443:e=>{e.exports={en:["unmerge up"],sv:["ta bort övre sammanslagning"]}},46453:e=>{e.exports={en:["unmerge down"],sv:["ta bort nedre sammanslagning"]}},94656:e=>{e.exports={en:["{chartStyle} chart type isn't currently available for tick-based intervals."]}},41643:e=>{e.exports={en:["{count} bars"],sv:["{count} staplar"]}},83470:e=>{e.exports={en:["{symbol} financials by TradingView"],sv:["{symbol} finansinformation av TradingView"]}},40947:e=>{e.exports={en:["{userName} published on {customer}, {date}"],sv:["{userName} publicerade på {customer},{date}"]}},91084:e=>{e.exports={en:["zoom"],sv:["zoom"]}},49856:e=>{e.exports={en:["zoom in"],sv:["förstora"]}},73638:e=>{e.exports={en:["zoom out"],sv:["förminska"]}},41807:e=>{e.exports={en:["day","days"],sv:["dag","dagar"]}},42328:e=>{e.exports={en:["hour","hours"],sv:["timme","timmar"]}},98393:e=>{e.exports={en:["month","months"],sv:["månad","månader"]}},78318:e=>{e.exports={en:["minute","minutes"],sv:["minut","minuter"]}},33232:e=>{e.exports={en:["second","seconds"],sv:["sekund","sekunder"]}},89937:e=>{e.exports={en:["range","ranges"],sv:["Intervall","Intervall"]}},48898:e=>{e.exports={en:["week","weeks"],sv:["Vecka","Veckor"]}},11913:e=>{e.exports={en:["tick","ticks"],sv:["tick","ticks"]}},58590:e=>{e.exports={en:["{count}m","{count}m"]}},47801:e=>{e.exports={en:["{count}d","{count}d"]}},46766:e=>{e.exports={en:["{count}y","{count}y"]}},56316:e=>{e.exports=Object.create(null),e.exports["#AAPL-symbol-description"]={en:["Apple Inc"],sv:["Apple Inc."]},e.exports["#AUDCAD-symbol-description"]={en:["Australian Dollar/Canadian Dollar"],sv:["Australiensisk dollar/Kanadensisk dollar"]},e.exports["#AUDCHF-symbol-description"]={en:["Australian Dollar / Swiss Franc"],sv:["Australiensisk dollar/Schweizisk franc"]},e.exports["#AUDJPY-symbol-description"]={en:["Australian Dollar / Japanese Yen"],sv:["Australiensisk dollar/Japansk yen"]},e.exports["#AUDNZD-symbol-description"]={en:["Australian Dollar / New Zealand Dollar"],sv:["Australiensisk dollar/Nyzeeländsk dollar"]},e.exports["#AUDRUB-symbol-description"]={en:["Australian Dollar / Russian Ruble"],sv:["AUSTRALIENSISK DOLLAR / RYSK RUBEL"]},e.exports["#AUDUSD-symbol-description"]={en:["Australian Dollar / U.S. Dollar"],sv:["Australiensisk dollar/USA-dollar"]},e.exports["#BRLJPY-symbol-description"]={en:["Brazilian Real / Japanese Yen"],sv:["Brasiliansk real/Japanska yen"]},e.exports["#BTCCAD-symbol-description"]={en:["Bitcoin / Canadian Dollar"],sv:["Bitcoin / Kanadensisk dollar"]},
e.exports["#BTCCNY-symbol-description"]={en:["Bitcoin / Chinese Yuan"],sv:["Bitcoin / Kinesisk Yuan"]},e.exports["#BTCEUR-symbol-description"]={en:["Bitcoin / Euro"],sv:["Bitcoin / Euro"]},e.exports["#BTCKRW-symbol-description"]={en:["Bitcoin / South Korean Won"],sv:["Bitcoin / Sydkoreansk Won"]},e.exports["#BTCRUR-symbol-description"]={en:["Bitcoin / Ruble"],sv:["Bitcoin / Rubel"]},e.exports["#BTCUSD-symbol-description"]={en:["Bitcoin / U.S. Dollar"],sv:["Bitcoin / Dollar"]},e.exports["#BVSP-symbol-description"]={en:["Brazil Bovespa Index"],sv:["Brazil Bovespa Index"]},e.exports["#CADJPY-symbol-description"]={en:["Canadian Dollar / Japanese Yen"],sv:["Kanadensisk dollar/Japanska yen"]},e.exports["#CHFJPY-symbol-description"]={en:["Swiss Franc / Japanese Yen"],sv:["Schweizisk franc/Japanska yen"]},e.exports["#COPPER-symbol-description"]={en:["CFDs on Copper"],sv:["CFD-kontrakt för koppar"]},e.exports["#ES1-symbol-description"]={en:["S&P 500 E-Mini Futures"],sv:["S&P 500 E-mini terminskontrakt"]},e.exports["#ESP35-symbol-description"]={en:["IBEX 35 Index"],sv:["IBEX 35 index"]},e.exports["#EUBUND-symbol-description"]={en:["Euro Bund"],sv:["Euro Obligationer."]},e.exports["#EURAUD-symbol-description"]={en:["Euro / Australian Dollar"],sv:["Euro / Australiensisk dollar"]},e.exports["#EURBRL-symbol-description"]={en:["Euro / Brazilian Real"],sv:["Euro / Brasiliansk real"]},e.exports["#EURCAD-symbol-description"]={en:["Euro / Canadian Dollar"],sv:["Euro / Kanadensisk dollar"]},e.exports["#EURCHF-symbol-description"]={en:["Euro / Swiss Franc"],sv:["Euro / Schweizisk franc"]},e.exports["#EURGBP-symbol-description"]={en:["Euro / British Pound"],sv:["Euro/Brittiska pund"]},e.exports["#EURJPY-symbol-description"]={en:["Euro / Japanese Yen"],sv:["Euro/Japanska yen"]},e.exports["#EURNZD-symbol-description"]={en:["Euro / New Zealand Dollar"],sv:["Euro / Nyzeeländsk dollar"]},e.exports["#EURRUB-symbol-description"]={en:["Euro / Russian Ruble"],sv:["Euro / Rysk Rubel"]},e.exports["#EURRUB_TOM-symbol-description"]={en:["Euro / Russian Ruble TOM"],sv:["Euro / Rysk Rubel TOM"]},e.exports["#EURSEK-symbol-description"]={en:["Euro / Swedish Krona"],sv:["Euro / Svensk krona"]},e.exports["#EURTRY-symbol-description"]={en:["Euro / Turkish Lira"],sv:["Euro / Turkisk lira"]},e.exports["#EURUSD-symbol-description"]={en:["Euro / U.S. Dollar"],sv:["Euro / USA-dollar"]},e.exports["#EUSTX50-symbol-description"]={en:["Euro Stoxx 50 Index"],sv:["Euro Stoxx 50 index"]},e.exports["#FRA40-symbol-description"]={en:["CAC 40 Index"],sv:["CAC 40 index"]},e.exports["#GB10-symbol-description"]={en:["UK Government Bonds 10 yr"],sv:["UK statsobligationer 10 år"]},e.exports["#GBPAUD-symbol-description"]={en:["British Pound / Australian Dollar"],sv:["Brittiska pund/Australiensiska dollar"]},e.exports["#GBPCAD-symbol-description"]={en:["British Pound / Canadian Dollar"],sv:["Brittiska pund/Kanadensiska dollar"]},e.exports["#GBPCHF-symbol-description"]={en:["British Pound / Swiss Franc"],sv:["Brittiska pund/Schweiziska franc"]},
e.exports["#GBPEUR-symbol-description"]={en:["British Pound / Euro"],sv:["Brittiska Pund/Euro"]},e.exports["#GBPJPY-symbol-description"]={en:["British Pound / Japanese Yen"],sv:["Brittiska pund/Japanska yen"]},e.exports["#GBPNZD-symbol-description"]={en:["British Pound / New Zealand Dollar"],sv:["Brittiska pund/Nyzeeländska dollar"]},e.exports["#GBPRUB-symbol-description"]={en:["British Pound / Russian Ruble"],sv:["Brittiska pund/Ryska rubel"]},e.exports["#GBPUSD-symbol-description"]={en:["British Pound / U.S. Dollar"],sv:["Brittiska pund/Amerikanska dollar"]},e.exports["#GER30-symbol-description"]={en:["DAX Index"],sv:["DAX index"]},e.exports["#GOOGL-symbol-description"]={en:["Alphabet Inc (Google) Class A"],sv:["Alphabet Inc (Google) Klass A"]},e.exports["#ITA40-symbol-description"]={en:["FTSE MIB Index"],sv:["FTSE MIB index"]},e.exports["#JPN225-symbol-description"]={en:["Nikkei 225 Index"],sv:["Nikkei 225 index"]},e.exports["#JPYKRW-symbol-description"]={en:["Japanese Yen / South Korean Won"],sv:["Japanska YEN/Koreanska WON"]},e.exports["#JPYRUB-symbol-description"]={en:["Japanese Yen / Russian Ruble"],sv:["Japanska Yen/Ryska rubel"]},e.exports["#KA1-symbol-description"]={en:["Sugar #11 Futures"],sv:["Socker #11 terminskontrakt"]},e.exports["#KG1-symbol-description"]={en:["Cotton Futures"],sv:["Bomull terminskontrakt"]},e.exports["#KT1-symbol-description"]={en:["Key Tronic Corр."],sv:["Key Tronic Corр."]},e.exports["#LKOH-symbol-description"]={en:["LUKOIL"],sv:["LUKOIL"]},e.exports["#LTCBTC-symbol-description"]={en:["Litecoin / Bitcoin"],sv:["Litecoin / Bitcoin"]},e.exports["#MGNT-symbol-description"]={en:["Magnit"],sv:["Magnit"]},e.exports["#MICEX-symbol-description"]={en:["MICEX Index"],sv:["MICEX INDEX"]},e.exports["#MNOD_ME.EQRP-symbol-description"]={en:["ADR GMK NORILSKIYNIKEL ORD SHS [REPO]"],sv:["ADR GMK NORILSKIYNIKEL ORD SHS [REPO]"]},e.exports["#MSFT-symbol-description"]={en:["Microsoft Corp."],sv:["Microsoft Corp."]},e.exports["#NAS100-symbol-description"]={en:["US 100 Cash CFD"],sv:["NASDAQ 100 CFD Cash"]},e.exports["#NGAS-symbol-description"]={en:["Natural Gas (Henry Hub)"],sv:["Naturgas (Henry Hub)"]},e.exports["#NKY-symbol-description"]={en:["Nikkei 225 Index"],sv:["Nikkei 225 Index"]},e.exports["#NZDJPY-symbol-description"]={en:["New Zealand Dollar / Japanese Yen"],sv:["Nyzeeländska dollar/Japanska yen"]},e.exports["#NZDUSD-symbol-description"]={en:["New Zealand Dollar / U.S. Dollar"],sv:["Nyzeeländsk dollar/USA-dollar"]},e.exports["#RB1-symbol-description"]={en:["RBOB Gasoline Futures"],sv:["RBOB bensin terminskontrakt"]},e.exports["#RTS-symbol-description"]={en:["Russian RTS Index"],sv:["Russian RTS Index"]},e.exports["#SBER-symbol-description"]={en:["SBERBANK"],sv:["SBERBANK"]},e.exports["#SPX500-symbol-description"]={en:["S&P 500 Index"],sv:["S&P 500 index"]},e.exports["#TWTR-symbol-description"]={en:["Twitter Inc"],sv:["TWITTER INC"]},e.exports["#UK100-symbol-description"]={en:["FTSE 100 Index"],sv:["FTSE 100 index"]},e.exports["#USDBRL-symbol-description"]={
en:["U.S. Dollar / Brazilian Real"],sv:["USA-dollar / Brasiliansk real"]},e.exports["#USDCAD-symbol-description"]={en:["U.S. Dollar / Canadian Dollar"],sv:["USA-dollar/Kanadensisk dollar"]},e.exports["#USDCHF-symbol-description"]={en:["U.S. Dollar / Swiss Franc"],sv:["USA-dollar/Schweizisk franc"]},e.exports["#USDCNY-symbol-description"]={en:["U.S. Dollar / Chinese Yuan"],sv:["USA-dollar / Kinesisk yuan"]},e.exports["#USDDKK-symbol-description"]={en:["U.S. Dollar / Danish Krone"],sv:["USA-DOLLAR / DANSK KRONE"]},e.exports["#USDHKD-symbol-description"]={en:["U.S. Dollar / Hong Kong Dollar"],sv:["USA-dollar/Hongkongdollar"]},e.exports["#USDIDR-symbol-description"]={en:["U.S. Dollar / Rupiah"],sv:["USA-dollar / Indonesisk rupiah"]},e.exports["#USDINR-symbol-description"]={en:["U.S. Dollar / Indian Rupee"],sv:["USA-dollar / Indisk rupie"]},e.exports["#USDJPY-symbol-description"]={en:["U.S. Dollar / Japanese Yen"],sv:["USA-dollar/Japansk yen"]},e.exports["#USDKRW-symbol-description"]={en:["U.S. Dollar / South Korean"],sv:["USA-DOLLAR / SYDKOREANSK WON"]},e.exports["#USDMXN-symbol-description"]={en:["U.S. Dollar / Mexican Peso"],sv:["USA-dollar / Mexikansk peso"]},e.exports["#USDPHP-symbol-description"]={en:["U.S. Dollar / Philippine peso"],sv:["USA-dollar / Filippinsk peso"]},e.exports["#USDRUB-symbol-description"]={en:["U.S. Dollar / Russian Ruble"],sv:["USA-dollar / Rysk Rubel"]},e.exports["#USDRUB_TOM-symbol-description"]={en:["U.S. Dollar / Russian Ruble TOM"],sv:["USA-dollar / Rysk Rubel TOM"]},e.exports["#USDSEK-symbol-description"]={en:["U.S. Dollar / Swedish Krona"],sv:["USA-dollar/Svensk krona"]},e.exports["#USDSGD-symbol-description"]={en:["U.S. Dollar / Singapore Dollar"],sv:["USA-dollar / Singaporiansk dollar"]},e.exports["#USDTRY-symbol-description"]={en:["U.S. Dollar / Turkish Lira"],sv:["USA-dollar/Turkisk lira"]},e.exports["#VTBR-symbol-description"]={en:["VTB"],sv:["VTB"]},e.exports["#XAGUSD-symbol-description"]={en:["Silver / U.S. Dollar"],sv:["Silver/USA-dollar"]},e.exports["#XAUUSD-symbol-description"]={en:["Gold Spot / U.S. Dollar"],sv:["Guld / USA-dollar"]},e.exports["#XPDUSD-symbol-description"]={en:["CFDs on Palladium"],sv:["CFD:er på Palladium"]},e.exports["#XPTUSD-symbol-description"]={en:["Platinum / U.S. Dollar"],sv:["Platina/USD"]},e.exports["#ZS1-symbol-description"]={en:["Soybean Futures - ECBT"],sv:["Sojabönor terminskontrakt - ECBT"]},e.exports["#ZW1-symbol-description"]={en:["Wheat Futures - ECBT"],sv:["Vete terminskontrakt - ECBT"]},e.exports["#BTCGBP-symbol-description"]={en:["Bitcoin / British Pound"],sv:["Bitcoin/Brittiska pund"]},e.exports["#MICEXINDEXCF-symbol-description"]={en:["MOEX Russia Index"],sv:["MOEX Ryssland Index"]},e.exports["#BTCAUD-symbol-description"]={en:["Bitcoin / Australian Dollar"],sv:["Bitcoin / Australiensisk dollar"]},e.exports["#BTCJPY-symbol-description"]={en:["Bitcoin / Japanese Yen"],sv:["Bitcoin/Japanska yen"]},e.exports["#BTCBRL-symbol-description"]={en:["Bitcoin / Brazilian Real"],sv:["Bitcoin / Brasiliansk real"]},
e.exports["#PT10-symbol-description"]={en:["Portugal Government Bonds 10 yr"],sv:["Portugal statsobligationer 10 år"]},e.exports["#TXSX-symbol-description"]={en:["TSX 60 Index"],sv:["TSX 60 Index"]},e.exports["#VIXC-symbol-description"]={en:["TSX 60 VIX Index"],sv:["TSX 60 VIX index"]},e.exports["#USDPLN-symbol-description"]={en:["U.S. Dollar / Polish Zloty"],sv:["USD/PLN"]},e.exports["#EURPLN-symbol-description"]={en:["Euro / Polish Zloty"],sv:["EUR/PLN"]},e.exports["#BTCPLN-symbol-description"]={en:["Bitcoin / Polish Zloty"],sv:["Bitcoin / Polska Złoty"]},e.exports["#CAC40-symbol-description"]={en:["CAC 40 Index"],sv:["CAC 40 index"]},e.exports["#XBTCAD-symbol-description"]={en:["Bitcoin / Canadian Dollar"],sv:["Bitcoin / Kanadensisk dollar"]},e.exports["#ITI2!-symbol-description"]={en:["Iron Ore Futures"],sv:["Järnmalmsterminer"]},e.exports["#ITIF2018-symbol-description"]={en:["Iron Ore Futures"],sv:["Järnmalmsterminer"]},e.exports["#ITIF2019-symbol-description"]={en:["Iron Ore Futures"],sv:["Järnmalmsterminer"]},e.exports["#ITIF2020-symbol-description"]={en:["Iron Ore Futures"],sv:["Järnmalmsterminer"]},e.exports["#ITIG2018-symbol-description"]={en:["Iron Ore Futures"],sv:["Järnmalmsterminer"]},e.exports["#ITIG2019-symbol-description"]={en:["Iron Ore Futures"],sv:["Järnmalmsterminer"]},e.exports["#ITIG2020-symbol-description"]={en:["Iron Ore Futures"],sv:["Järnmalmsterminer"]},e.exports["#ITIH2018-symbol-description"]={en:["Iron Ore Futures"],sv:["Järnmalmsterminer"]},e.exports["#ITIH2019-symbol-description"]={en:["Iron Ore Futures"],sv:["Järnmalmsterminer"]},e.exports["#ITIH2020-symbol-description"]={en:["Iron Ore Futures"],sv:["Järnmalmsterminer"]},e.exports["#ITIJ2018-symbol-description"]={en:["Iron Ore Futures"],sv:["Järnmalmsterminer"]},e.exports["#ITIJ2019-symbol-description"]={en:["Iron Ore Futures"],sv:["Järnmalmsterminer"]},e.exports["#ITIJ2020-symbol-description"]={en:["Iron Ore Futures"],sv:["Järnmalmsterminer"]},e.exports["#ITIK2018-symbol-description"]={en:["Iron Ore Futures"],sv:["Järnmalmsterminer"]},e.exports["#ITIK2019-symbol-description"]={en:["Iron Ore Futures"],sv:["Järnmalmsterminer"]},e.exports["#ITIK2020-symbol-description"]={en:["Iron Ore Futures"],sv:["Järnmalmsterminer"]},e.exports["#ITIM2017-symbol-description"]={en:["Iron Ore Futures"],sv:["Järnmalmsterminer"]},e.exports["#ITIM2018-symbol-description"]={en:["Iron Ore Futures"],sv:["Järnmalmsterminer"]},e.exports["#ITIM2019-symbol-description"]={en:["Iron Ore Futures"],sv:["Järnmalmsterminer"]},e.exports["#ITIM2020-symbol-description"]={en:["Iron Ore Futures"],sv:["Järnmalmsterminer"]},e.exports["#ITIN2017-symbol-description"]={en:["Iron Ore Futures"],sv:["Järnmalmsterminer"]},e.exports["#ITIN2018-symbol-description"]={en:["Iron Ore Futures"],sv:["Järnmalmsterminer"]},e.exports["#ITIN2019-symbol-description"]={en:["Iron Ore Futures"],sv:["Järnmalmsterminer"]},e.exports["#ITIN2020-symbol-description"]={en:["Iron Ore Futures"],sv:["Järnmalmsterminer"]},e.exports["#ITIQ2017-symbol-description"]={en:["Iron Ore Futures"],
sv:["Järnmalmsterminer"]},e.exports["#ITIQ2018-symbol-description"]={en:["Iron Ore Futures"],sv:["Järnmalmsterminer"]},e.exports["#ITIQ2019-symbol-description"]={en:["Iron Ore Futures"],sv:["Järnmalmsterminer"]},e.exports["#ITIQ2020-symbol-description"]={en:["Iron Ore Futures"],sv:["Järnmalmsterminer"]},e.exports["#ITIU2017-symbol-description"]={en:["Iron Ore Futures"],sv:["Järnmalmsterminer"]},e.exports["#ITIU2018-symbol-description"]={en:["Iron Ore Futures"],sv:["Järnmalmsterminer"]},e.exports["#ITIU2019-symbol-description"]={en:["Iron Ore Futures"],sv:["Järnmalmsterminer"]},e.exports["#ITIU2020-symbol-description"]={en:["Iron Ore Futures"],sv:["Järnmalmsterminer"]},e.exports["#ITIV2017-symbol-description"]={en:["Iron Ore Futures"],sv:["Järnmalmsterminer"]},e.exports["#ITIV2018-symbol-description"]={en:["Iron Ore Futures"],sv:["Järnmalmsterminer"]},e.exports["#ITIV2019-symbol-description"]={en:["Iron Ore Futures"],sv:["Järnmalmsterminer"]},e.exports["#ITIV2020-symbol-description"]={en:["Iron Ore Futures"],sv:["Järnmalmsterminer"]},e.exports["#ITIX2017-symbol-description"]={en:["Iron Ore Futures"],sv:["Järnmalmsterminer"]},e.exports["#ITIX2018-symbol-description"]={en:["Iron Ore Futures"],sv:["Järnmalmsterminer"]},e.exports["#ITIX2019-symbol-description"]={en:["Iron Ore Futures"],sv:["Järnmalmsterminer"]},e.exports["#ITIX2020-symbol-description"]={en:["Iron Ore Futures"],sv:["Järnmalmsterminer"]},e.exports["#ITIZ2017-symbol-description"]={en:["Iron Ore Futures"],sv:["Järnmalmsterminer"]},e.exports["#ITIZ2018-symbol-description"]={en:["Iron Ore Futures"],sv:["Järnmalmsterminer"]},e.exports["#ITIZ2019-symbol-description"]={en:["Iron Ore Futures"],sv:["Järnmalmsterminer"]},e.exports["#ITIZ2020-symbol-description"]={en:["Iron Ore Futures"],sv:["Järnmalmsterminer"]},e.exports["#AMEX:GXF-symbol-description"]={en:["Global x FTSE Nordic Region ETF"],sv:["Global X FTSE Norden ETF"]},e.exports["#ASX:XAF-symbol-description"]={en:["S&P/ASX All Australian 50 Index"],sv:["S&P/ASX Alla australienska 50 index"]},e.exports["#ASX:XAT-symbol-description"]={en:["S&P/ASX All Australian 200 Index"],sv:["S&P/ASX Alla australienska 200 Index"]},e.exports["#BIST:XU100-symbol-description"]={en:["BIST 100 Index"],sv:["BIST 100 Index"]},e.exports["#GPW:WIG20-symbol-description"]={en:["WIG20 Index"],sv:["WIG20 Index"]},e.exports["#INDEX:JKSE-symbol-description"]={en:["Jakarta Composite Index"],sv:["Jakarta Composite Index"]},e.exports["#INDEX:KLSE-symbol-description"]={en:["Bursa Malaysia KLCI Index"],sv:["Bursa Malaysia KLCI Index"]},e.exports["#INDEX:NZD-symbol-description"]={en:["NZX 50 Index"],sv:["NZX 50 Index"]},e.exports["#INDEX:STI-symbol-description"]={en:["STI Index"],sv:["STI Index"]},e.exports["#INDEX:XLY0-symbol-description"]={en:["Shanghai Composite Index"],sv:["Shanghai Composite Index"]},e.exports["#MOEX:MICEXINDEXCF-symbol-description"]={en:["MOEX Russia Index"],sv:["MOEX Russia Index"]},e.exports["#NYMEX:KT1!-symbol-description"]={en:["Coffee Futures"],sv:["Kaffeterminer"]},e.exports["#OANDA:NATGASUSD-symbol-description"]={
en:["CFDs on Natural Gas"],sv:["CFD:er för naturgas"]},e.exports["#OANDA:USDPLN-symbol-description"]={en:["U.S. Dollar / Polish Zloty"],sv:["US Dollar / Polsk Zloty"]},e.exports["#TSX:TX60-symbol-description"]={en:["S&P/TSX 60 Index"],sv:["S&P/TSX 60 Index"]},e.exports["#TSX:VBU-symbol-description"]={en:["Vanguard U.S. Aggregate Bond Index ETF (CAD-hedged) UN"],sv:["Vanguard U.S. Aggregate Bond Index ETF (CAD-hedged) FN"]},e.exports["#TSX:VIXC-symbol-description"]={en:["S&P/TSX 60 VIX Index"],sv:["S&P/TSX 60 VIX Index"]},e.exports["#TVC:CAC40-symbol-description"]={en:["CAC 40 Index"],sv:["CAC 40 Index"]},e.exports["#TVC:ES10-symbol-description"]={en:["Spain Government Bonds 10 YR"],sv:["Spanska statsobligationer 10 år"]},e.exports["#TVC:EUBUND-symbol-description"]={en:["Euro Bund"],sv:["Euro Bund"]},e.exports["#TVC:GB02-symbol-description"]={en:["UK Government Bonds 2 YR"],sv:["Brittiska statsobligationer 2 år"]},e.exports["#TVC:GB10-symbol-description"]={en:["UK Government Bonds 10 YR"],sv:["Brittiska statsobligationer 10 år"]},e.exports["#TVC:GOLD-symbol-description"]={en:["CFDs on Gold (US$ / OZ)"],sv:["CFDs på guld (US $ / OZ)"]},e.exports["#TVC:ID03-symbol-description"]={en:["Indonesia Government Bonds 3 YR"],sv:["Indonesiska statsobligationer 3 år"]},e.exports["#TVC:ID10-symbol-description"]={en:["Indonesia Government Bonds 10 YR"],sv:["Indonesiska statsobligationer 10år"]},e.exports["#TVC:PALLADIUM-symbol-description"]={en:["CFDs on Palladium (US$ / OZ)"],sv:["CFD på Palladium (US$/OZ)"]},e.exports["#TVC:PT10-symbol-description"]={en:["Portugal Government Bonds 10 YR"],sv:["Portugal statsobligationer 10 år"]},e.exports["#TVC:SILVER-symbol-description"]={en:["CFDs on Silver (US$ / OZ)"],sv:["CFDs på silver (US$ / OZ)"]},e.exports["#TSX:TSX-symbol-description"]={en:["S&P/TSX Composite Index"],sv:["S&P/TSX Composite Index"]},e.exports["#OANDA:CH20CHF-symbol-description"]={en:["Swiss 20 Index"],sv:["Swiss 20-indexet"]},e.exports["#TVC:SHCOMP-symbol-description"]={en:["Shanghai Composite Index"],sv:["Shanghai Composite Index"]},e.exports["#NZX:ALLC-symbol-description"]={en:["S&P/NZX All Index (Capital Index)"],sv:["S&P/NZX Alla Index (Kapitalindex)"]},e.exports["#AMEX:SHYG-symbol-description"]={en:["Shares 0-5 YEAR High Yield Corporate Bond ETF"],sv:["Aktier 0-5 ÅR Hög avkastningsobligationer ETF"]},e.exports["#TVC:AU10-symbol-description"]={en:["Australia Government Bonds 10 YR"],sv:["Australien statsobligationer 10 år"]},e.exports["#TVC:CN10-symbol-description"]={en:["China Government Bonds 10 YR"],sv:["Kina statsobligationer 10 år"]},e.exports["#TVC:KR10-symbol-description"]={en:["Korea Government Bonds 10 YR"],sv:["Korea statsobligationer 10 år"]},e.exports["#NYMEX:RB1!-symbol-description"]={en:["RBOB Gasoline Futures"],sv:["RBOB bensin terminskontrakt"]},e.exports["#NYMEX:HO1!-symbol-description"]={en:["NY Harbor ULSD Futures"],sv:["NY hamn ULSD-terminer"]},e.exports["#NYMEX:AEZ1!-symbol-description"]={en:["NY Ethanol Futures"],sv:["NY ethanolterminer"]},e.exports["#OANDA:XCUUSD-symbol-description"]={
en:["CFDs on Copper (US$ / lb)"],sv:["CFDs på koppar (US$ / lb)"]},e.exports["#COMEX:ZA1!-symbol-description"]={en:["Zinc Futures"],sv:["Zinkterminer"]},e.exports["#CBOT:ZW1!-symbol-description"]={en:["Wheat Futures"],sv:["Veteterminer"]},e.exports["#NYMEX:KA1!-symbol-description"]={en:["Sugar #11 Futures"],sv:["Socker #11 terminskontrakt"]},e.exports["#CBOT:QBC1!-symbol-description"]={en:["Corn Futures"],sv:["Majsterminer"]},e.exports["#CME:E61!-symbol-description"]={en:["Euro Futures"],sv:["Terminer för euro"]},e.exports["#CME:B61!-symbol-description"]={en:["British Pound Futures"],sv:["Terminer för brittiska pund"]},e.exports["#CME:QJY1!-symbol-description"]={en:["Japanese Yen Futures"],sv:["Terminer för japanska yen"]},e.exports["#CME:A61!-symbol-description"]={en:["Australian Dollar Futures"],sv:["Terminer för australienska dollar"]},e.exports["#CME:D61!-symbol-description"]={en:["Canadian Dollar Futures"],sv:["Terminer för kanadensiska dollar"]},e.exports["#CME:SP1!-symbol-description"]={en:["S&P 500 Futures"],sv:["S&P 500-terminer"]},e.exports["#CME_MINI:NQ1!-symbol-description"]={en:["NASDAQ 100 E-mini Futures"],sv:["NASDAQ 100 E-mini terminer"]},e.exports["#CBOT_MINI:YM1!-symbol-description"]={en:["E-mini Dow Jones ($5) Futures"],sv:["E-mini Dow Jones ($5) terminer"]},e.exports["#CME:NY1!-symbol-description"]={en:["NIKKEI 225 Futures"],sv:["Nikkei 225 terminer"]},e.exports["#EUREX:DY1!-symbol-description"]={en:["DAX Index"],sv:["DAX Index"]},e.exports["#CME:IF1!-symbol-description"]={en:["IBOVESPA Index Futures-US$"],sv:["IBOVESPA indexterminer-US$"]},e.exports["#CBOT:TY1!-symbol-description"]={en:["10 Year T-Note Futures"],sv:["10 år T-Note terminer"]},e.exports["#CBOT:FV1!-symbol-description"]={en:["5 Year T-Note Futures"],sv:["5 år T-Note terminer"]},e.exports["#CBOT:ZE1!-symbol-description"]={en:["Treasury Notes - 3 Year Futures"],sv:["Statsobligationer - 3 års terminer"]},e.exports["#CBOT:TU1!-symbol-description"]={en:["2 Year T-Note Futures"],sv:["2 år T-Note terminer"]},e.exports["#CBOT:FF1!-symbol-description"]={en:["30-Day FED Funds Interest Rate Futures"],sv:["30-dagars FED-tillgångar räntesats terminer"]},e.exports["#CBOT:US1!-symbol-description"]={en:["T-Bond Futures"],sv:["T-Bond terminer"]},e.exports["#TVC:EXY-symbol-description"]={en:["Euro Currency Index"],sv:["Euro valuta index"]},e.exports["#TVC:JXY-symbol-description"]={en:["Japanese Yen Currency Index"],sv:["Japanska yen valutaindex"]},e.exports["#TVC:BXY-symbol-description"]={en:["British Pound Currency Index"],sv:["Brittisk pund valutaindex"]},e.exports["#TVC:AXY-symbol-description"]={en:["Australian Dollar Currency Index"],sv:["Australiensiska dollar valutaindex"]},e.exports["#TVC:CXY-symbol-description"]={en:["Canadian Dollar Currency Index"],sv:["Kanadensiska dollar valutaindex"]},e.exports["#FRED:GDP-symbol-description"]={en:["Gross Domestic Product, 1 Decimal"],sv:["Bruttonationalprodukt, 1 decimal"]},e.exports["#FRED:UNRATE-symbol-description"]={en:["Civilian Unemployment Rate"],sv:["Civila arbetslöshetsnivån"]},
e.exports["#FRED:POP-symbol-description"]={en:["Total Population: All Ages Including Armed Forces Overseas"],sv:["Totalbefolkning: alla åldrar inklusive väpnade styrkor utomlands"]},e.exports["#ETHUSD-symbol-description"]={en:["Ethereum / U.S. Dollar"],sv:["Ethereum / U.S. Dollar"]},e.exports["#BMFBOVESPA:IBOV-symbol-description"]={en:["Bovespa Index"],sv:["IBovespa Index"]},e.exports["#BMFBOVESPA:IBRA-symbol-description"]={en:["IBrasil Index"],sv:["IBrasil Index"]},e.exports["#BMFBOVESPA:IBXL-symbol-description"]={en:["Brazil 50 Index"],sv:["IBRX 50 Index"]},e.exports["#COMEX:HG1!-symbol-description"]={en:["Copper Futures"],sv:["Kopparterminer"]},e.exports["#INDEX:HSCE-symbol-description"]={en:["Hang Seng China Enterprises Index"],sv:["Hang Seng Kina Företagsindex"]},e.exports["#NYMEX:CL1!-symbol-description"]={en:["Light Crude Oil Futures"],sv:["Lätta råoljeterminer"]},e.exports["#OTC:IHRMF-symbol-description"]={en:["Ishares MSCI Japan SHS"],sv:["Ishares MSCI Japan SHS"]},e.exports["#TVC:DAX-symbol-description"]={en:["DAX Index"],sv:["DAX Index"]},e.exports["#TVC:DE10-symbol-description"]={en:["German Government Bonds 10 YR"],sv:["Tyska statsobligationer 10 år"]},e.exports["#TVC:DJI-symbol-description"]={en:["Dow Jones Industrial Average Index"],sv:["Dow Jones Industrial genomsnittsindex"]},e.exports["#TVC:DXY-symbol-description"]={en:["U.S. Dollar Index"],sv:["Valutaindex för U.S. dollar"]},e.exports["#TVC:FR10-symbol-description"]={en:["France Government Bonds 10 YR"],sv:["Franska statsobligationer 10 år"]},e.exports["#TVC:HSI-symbol-description"]={en:["Hang Seng Index"],sv:["Hang Seng Index"]},e.exports["#TVC:IBEX35-symbol-description"]={en:["IBEX 35 Index"],sv:["IBEX 35 Index"]},e.exports["#FX:AUS200-symbol-description"]={en:["S&P/ASX Index"],sv:["S&P/ASX Index"]},e.exports["#AMEX:SHY-symbol-description"]={en:["Ishares 1-3 Year Treasury Bond ETF"],sv:["Ishares 1-3 års statsobligationer ETF"]},e.exports["#ASX:XJO-symbol-description"]={en:["S&P/ASX 200 Index"],sv:["S&P/ASX 200 Index"]},e.exports["#BSE:SENSEX-symbol-description"]={en:["S&P BSE Sensex Index"],sv:["S&P BSE Sensex Index"]},e.exports["#INDEX:MIB-symbol-description"]={en:["MIB Index"],sv:["MIB Index"]},e.exports["#INDEX:MOY0-symbol-description"]={en:["Euro Stoxx 50 Index"],sv:["Euro Stoxx 50 Index"]},e.exports["#MOEX:RTSI-symbol-description"]={en:["RTS Index"],sv:["RTS Index"]},e.exports["#NSE:NIFTY-symbol-description"]={en:["Nifty 50 Index"],sv:["Nifty 50 Index"]},e.exports["#NYMEX:NG1!-symbol-description"]={en:["Natural Gas Futures"],sv:["Naturgasterminer"]},e.exports["#NYMEX:ZC1!-symbol-description"]={en:["Corn Futures"],sv:["Majsterminer"]},e.exports["#TVC:IN10-symbol-description"]={en:["India Government Bonds 10 YR"],sv:["Indiska statsobligationer 10 år"]},e.exports["#TVC:IT10-symbol-description"]={en:["Italy Government Bonds 10 YR"],sv:["Italienska statsobligationer 10 år"]},e.exports["#TVC:JP10-symbol-description"]={en:["Japan Government Bonds 10 YR"],sv:["Japanska statsobligationer 10 år"]},e.exports["#TVC:NDX-symbol-description"]={
en:["US 100 Index"],sv:["NASDAQ 100 Index"]},e.exports["#TVC:NI225-symbol-description"]={en:["Nikkei 225 Index"],sv:["Nikkei 225 Index"]},e.exports["#TVC:SPX-symbol-description"]={en:["S&P 500 Index"],sv:["S&P 500 Index"]},e.exports["#TVC:SX5E-symbol-description"]={en:["Euro Stoxx 50 Index"],sv:["Euro Stoxx 50 Index"]},e.exports["#TVC:TR10-symbol-description"]={en:["Turkey Government Bonds 10 YR"],sv:["Turkiska statsobligationer 10 år"]},e.exports["#TVC:UKOIL-symbol-description"]={en:["CFDs on Brent Crude Oil"],sv:["CFD:er på Brentolja"]},e.exports["#TVC:UKX-symbol-description"]={en:["UK 100 Index"],sv:["UK 100 Index"]},e.exports["#TVC:US02-symbol-description"]={en:["US Government Bonds 2 YR"],sv:["Amerikanska statsobligationer 2 år"]},e.exports["#TVC:US05-symbol-description"]={en:["US Government Bonds 5 YR"],sv:["Amerikanska statsobligationer 5 år"]},e.exports["#TVC:US10-symbol-description"]={en:["US Government Bonds 10 YR"],sv:["Amerikanska statsobligationer 10 år"]},e.exports["#TVC:USOIL-symbol-description"]={en:["CFDs on WTI Crude Oil"],sv:["CFDs på WTI råolja"]},e.exports["#NYMEX:ITI1!-symbol-description"]={en:["Iron Ore Futures"],sv:["Järnmalmsterminer"]},e.exports["#NASDAQ:SHY-symbol-description"]={en:["Ishares 1-3 Year Treasury Bond ETF"],sv:["Ishares 1-3 års statsobligationer ETF"]},e.exports["#AMEX:ALD-symbol-description"]={en:["WisdomTree Asia Local Debt ETF"],sv:["WisdomTree Asia Local Debt ETF"]},e.exports["#NASDAQ:AMD-symbol-description"]={en:["Advanced Micro Devices Inc"],sv:["Advanced Micro Devices Inc"]},e.exports["#NYSE:BABA-symbol-description"]={en:["Alibaba Group Holdings Ltd."],sv:["Alibaba Group Holdings Ltd."]},e.exports["#ICEEUR:CB-symbol-description"]={en:["Crude Oil Brent"],sv:["Råolja Brent"]},e.exports["#ICEEUR:CB1!-symbol-description"]={en:["Brent Crude Oil"],sv:["Brentolja"]},e.exports["#ICEUSA:CC-symbol-description"]={en:["Cocoa"],sv:["Kakao"]},e.exports["#NYMEX:CL-symbol-description"]={en:["Crude Oil WTI"],sv:["Råolja WTI"]},e.exports["#ICEUSA:CT-symbol-description"]={en:["Cotton #2"],sv:["Bomull #2"]},e.exports["#NASDAQ:CTRV-symbol-description"]={en:["ContraVir Pharmaceuticals Inc"],sv:["CONTRAVIR PHARMACEUTICALS INC"]},e.exports["#CME:DL-symbol-description"]={en:["Class III Milk"],sv:["Klass III Mjölk"]},e.exports["#NYSE:F-symbol-description"]={en:["FORD MTR CO DEL"],sv:["FORD MTR CO DEL"]},e.exports["#MOEX:GAZP-symbol-description"]={en:["GAZPROM"],sv:["GAZPROM"]},e.exports["#COMEX:GC-symbol-description"]={en:["Gold"],sv:["Guld"]},e.exports["#CME:GF-symbol-description"]={en:["Feeder Cattle"],sv:["Nötkreatur"]},e.exports["#CME:HE-symbol-description"]={en:["Lean Hogs"],sv:["Boskap, svin"]},e.exports["#NASDAQ:IEF-symbol-description"]={en:["Ishares 7-10 Year Treasury Bond ETF"],sv:["Ishares 7-10 års statsobligationer ETF"]},e.exports["#NASDAQ:IEI-symbol-description"]={en:["Ishares 3-7 Year Treasury Bond ETF"],sv:["Ishares 3-5 års statsobligationer ETF"]},e.exports["#NYMEX:KA1-symbol-description"]={en:["Sugar #11 Futures"],sv:["Socker #11 terminskontrakt"]},
e.exports["#ICEUSA:KC-symbol-description"]={en:["Coffee"],sv:["Kaffe"]},e.exports["#NYMEX:KG1-symbol-description"]={en:["Cotton Futures"],sv:["Bomull terminskontrakt"]},e.exports["#FWB:KT1-symbol-description"]={en:["Key Tronic Corр."],sv:["Key Tronic Corр."]},e.exports["#CME:LE-symbol-description"]={en:["Live Cattle"],sv:["Levande boskap"]},e.exports["#ICEEUR:LO-symbol-description"]={en:["ICE Heating Oil"],sv:["ICE uppvärmningsolja"]},e.exports["#CME:LS-symbol-description"]={en:["Lumber"],sv:["Timmer"]},e.exports["#MOEX:MGNT-symbol-description"]={en:["MAGNIT"],sv:["MAGNIT"]},e.exports["#LSIN:MNOD-symbol-description"]={en:["ADR GMK NORILSKIYNIKEL ORD SHS [REPO]"],sv:["ADR GMK NORILSKIYNIKEL ORD SHS [REPO]"]},e.exports["#NYMEX:NG-symbol-description"]={en:["Natural Gas"],sv:["Naturgas"]},e.exports["#ICEUSA:OJ-symbol-description"]={en:["Orange Juice"],sv:["Apelsinjuice"]},e.exports["#NYMEX:PA-symbol-description"]={en:["Palladium"],sv:["Palladium"]},e.exports["#NYSE:PBR-symbol-description"]={en:["PETROLEO BRASILEIRO SA PETROBR"],sv:["PETROLEO BRASILEIRO SA PETROBR"]},e.exports["#NYMEX:PL-symbol-description"]={en:["Platinum"],sv:["Platina"]},e.exports["#COMEX_MINI:QC-symbol-description"]={en:["E-Mini Copper"],sv:["E-Mini Koppar"]},e.exports["#NYMEX:RB-symbol-description"]={en:["Gasoline RBOB"],sv:["Bensin RBOB"]},e.exports["#NYMEX:RB1-symbol-description"]={en:["RBOB Gasoline Futures"],sv:["RBOB bensin terminskontrakt"]},e.exports["#MOEX:SBER-symbol-description"]={en:["SBERBANK"],sv:["SBERBANK"]},e.exports["#AMEX:SCHO-symbol-description"]={en:["Schwab Short-Term U.S. Treasury ETF"],sv:["Schwab Short-Term U.S. Treasury ETF"]},e.exports["#COMEX:SI-symbol-description"]={en:["Silver"],sv:["Silver"]},e.exports["#NASDAQ:TLT-symbol-description"]={en:["Ishares 20+ Year Treasury Bond ETF"],sv:["Ishares 20+ års statsobligationer ETF"]},e.exports["#TVC:VIX-symbol-description"]={en:["Volatility S&P 500 Index"],sv:["Volatilitet S&P 500 index"]},e.exports["#MOEX:VTBR-symbol-description"]={en:["VTB"],sv:["VTB"]},e.exports["#COMEX:ZA-symbol-description"]={en:["Zinc"],sv:["Zink"]},e.exports["#CBOT:ZC-symbol-description"]={en:["Corn"],sv:["Majs"]},e.exports["#CBOT:ZK-symbol-description"]={en:["Ethanol Futures"],sv:["Terminskontrakt etanol"]},e.exports["#CBOT:ZL-symbol-description"]={en:["Soybean Oil"],sv:["Sojabönsolja"]},e.exports["#CBOT:ZO-symbol-description"]={en:["Oats"],sv:["Havre"]},e.exports["#CBOT:ZR-symbol-description"]={en:["Rough Rice"],sv:["Råris"]},e.exports["#CBOT:ZS-symbol-description"]={en:["Soybeans"],sv:["Sojabönor"]},e.exports["#CBOT:ZS1-symbol-description"]={en:["Soybean Futures"],sv:["Sojabönor terminskontrakt"]},e.exports["#CBOT:ZW-symbol-description"]={en:["Wheat"],sv:["Vete"]},e.exports["#CBOT:ZW1-symbol-description"]={en:["Wheat Futures - ECBT"],sv:["Vete terminskontrakt - ECBT"]},e.exports["#NASDAQ:ITI-symbol-description"]={en:["Iteris Inc"],sv:["Iteris Inc."]},e.exports["#NYMEX:ITI2!-symbol-description"]={en:["Iron Ore Futures"],sv:["Järnmalmsterminer"]},e.exports["#CADUSD-symbol-description"]={
en:["Canadian Dollar / U.S. Dollar"],sv:["Kanadensiska dollar / amerikanska dollar"]},e.exports["#CHFUSD-symbol-description"]={en:["Swiss Franc / U.S. Dollar"],sv:["Schweiziska franc / amerikanska dollar"]},e.exports["#GPW:ACG-symbol-description"]={en:["Acautogaz"],sv:["Acautogaz"]},e.exports["#JPYUSD-symbol-description"]={en:["Japanese Yen / U.S. Dollar"],sv:["Japanska yen/amerikanska dollar"]},e.exports["#USDAUD-symbol-description"]={en:["U.S. Dollar / Australian Dollar"],sv:["Amerikanska dollar / australiensiska dollar"]},e.exports["#USDEUR-symbol-description"]={en:["U.S. Dollar / Euro"],sv:["Amerikanska dollar / Euro"]},e.exports["#USDGBP-symbol-description"]={en:["U.S. Dollar / Pound Sterling"],sv:["Amerikanska dollar / pund sterling"]},e.exports["#USDNZD-symbol-description"]={en:["U.S. Dollar / New Zealand Dollar"],sv:["Amerikanska dollar / Nya Zeeländsk dollar"]},e.exports["#UKOIL-symbol-description"]={en:["CFDs on Crude Oil (Brent)"],sv:["CFDs på råolja (Brent)"]},e.exports["#USOIL-symbol-description"]={en:["CFDs on Crude Oil (WTI)"],sv:["CFDs på råolja (WTI)"]},e.exports["#US30-symbol-description"]={en:["Dow Jones Industrial Average Index"],sv:["Dow Jones Industrial genomsnittsindex"]},e.exports["#BCHUSD-symbol-description"]={en:["Bitcoin Cash / U.S. Dollar"],sv:["Bitcoin Cash / amerikanska dollar"]},e.exports["#ETCUSD-symbol-description"]={en:["Ethereum Classic / U.S. Dollar"],sv:["Ethereum Classic / amerikanska dollar"]},e.exports["#GOOG-symbol-description"]={en:["Alphabet Inc (Google) Class C"],sv:["Alphabet Inc (Google) Klass C"]},e.exports["#LTCUSD-symbol-description"]={en:["Litecoin / U.S. Dollar"],sv:["Litecoin / amerikanska dollar"]},e.exports["#XRPUSD-symbol-description"]={en:["XRP / U.S. Dollar"],sv:["Ripple / amerikanska dollar"]},e.exports["#SP:SPX-symbol-description"]={en:["S&P 500 Index"],sv:["S&P 500 Index"]},e.exports["#ETCBTC-symbol-description"]={en:["Ethereum Classic / Bitcoin"],sv:["Ethereum Classic / Bitcoin"]},e.exports["#ETHBTC-symbol-description"]={en:["Ethereum / Bitcoin"],sv:["Ethereum / Bitcoin"]},e.exports["#XRPBTC-symbol-description"]={en:["XRP / Bitcoin"],sv:["Ripple / Bitcoin"]},e.exports["#TVC:US30-symbol-description"]={en:["US Government Bonds 30 YR"],sv:["Amerikanska statsobligationer 30 år"]},e.exports["#COMEX:SI1!-symbol-description"]={en:["Silver Futures"],sv:["Silverterminer"]},e.exports["#BTGUSD-symbol-description"]={en:["Bitcoin Gold / U.S. Dollar"],sv:["Bitcoin Gold / amerikanska dollar"]},e.exports["#IOTUSD-symbol-description"]={en:["IOTA / U.S. Dollar"],sv:["IOTA / amerikanska dollar"]},e.exports["#CME:BTC1!-symbol-description"]={en:["Bitcoin CME Futures"],sv:["Bitcoin CME terminskontrakt"]},e.exports["#COMEX:GC1!-symbol-description"]={en:["Gold Futures"],sv:["Terminskontrakt guld"]},e.exports["#CORNUSD-symbol-description"]={en:["CFDs on Corn"],sv:["CFD:er på majs"]},e.exports["#COTUSD-symbol-description"]={en:["CFDs on Cotton"],sv:["CFDs på bomull"]},e.exports["#DJ:DJA-symbol-description"]={en:["Dow Jones Composite Average Index"],
sv:["Dow Jones Composite Average Index"]},e.exports["#DJ:DJI-symbol-description"]={en:["Dow Jones Industrial Average Index"],sv:["Dow Jones Industrial genomsnittsindex"]},e.exports["#ETHEUR-symbol-description"]={en:["Ethereum / Euro"],sv:["Ethereum / Euro"]},e.exports["#ETHGBP-symbol-description"]={en:["Ethereum / British Pound"],sv:["Ethereum / Brittiska pund"]},e.exports["#ETHJPY-symbol-description"]={en:["Ethereum / Japanese Yen"],sv:["Ethereum/japanska yen"]},e.exports["#EURNOK-symbol-description"]={en:["Euro / Norwegian Krone"],sv:["Euro / Norska kronan"]},e.exports["#GBPPLN-symbol-description"]={en:["British Pound / Polish Zloty"],sv:["Brittiska pund / Polska zloty"]},e.exports["#MOEX:BR1!-symbol-description"]={en:["Brent Oil Futures"],sv:["Terminskontrakt Råolja Brent"]},e.exports["#NYMEX:KG1!-symbol-description"]={en:["Cotton Futures"],sv:["Bomull terminskontrakt"]},e.exports["#NYMEX:PL1!-symbol-description"]={en:["Platinum Futures"],sv:["Terminskontrakt platina"]},e.exports["#SOYBNUSD-symbol-description"]={en:["CFDs on Soybeans"],sv:["CFD:er på sojabönor"]},e.exports["#SUGARUSD-symbol-description"]={en:["CFDs on Sugar"],sv:["CFDs på socker"]},e.exports["#TVC:IXIC-symbol-description"]={en:["US Composite Index"],sv:["NASDAQ Composite Index"]},e.exports["#TVC:RU-symbol-description"]={en:["Russell 1000 Index"],sv:["Russell 1000 Index"]},e.exports["#USDZAR-symbol-description"]={en:["U.S. Dollar / South African Rand"],sv:["Amerikanska dollar / Sydafrikanska rand"]},e.exports["#WHEATUSD-symbol-description"]={en:["CFDs on Wheat"],sv:["CFDs på vete"]},e.exports["#XRPEUR-symbol-description"]={en:["XRP / Euro"],sv:["Ripple / Euro"]},e.exports["#CBOT:S1!-symbol-description"]={en:["Soybean Futures"],sv:["Terminskontrakt på sojabönor"]},e.exports["#SP:MID-symbol-description"]={en:["S&P 400 Index"],sv:["S&P 400 Index"]},e.exports["#TSX:XCUUSD-symbol-description"]={en:["CFDs on Copper"],sv:["CFDs på koppar"]},e.exports["#TVC:NYA-symbol-description"]={en:["NYSE Composite Index"],sv:["NYSE Composite Index"]},e.exports["#TVC:PLATINUM-symbol-description"]={en:["CFDs on Platinum (US$ / OZ)"],sv:["CFDs på platinum (US$ / OZ)"]},e.exports["#TVC:SSMI-symbol-description"]={en:["Swiss Market Index"],sv:["Schweiziska marknadsindexet"]},e.exports["#TVC:SXY-symbol-description"]={en:["Swiss Franc Currency Index"],sv:["Schweiziska Franc valutaindex"]},e.exports["#MOEX:RI1!-symbol-description"]={en:["RTS Index Futures"],sv:["RTS index terminer"]},e.exports["#MOEX:MX1!-symbol-description"]={en:["MICEX Index Futures"],sv:["MICEX index terminer"]},e.exports["#CBOE:BG1!-symbol-description"]={en:["Bitcoin CBOE Futures"],sv:["Bitcoin CBOE terminer"]},e.exports["#TVC:MY10-symbol-description"]={en:["Malaysia Government Bonds 10 YR"],sv:["Malaysias statsobligationer 10 år"]},e.exports["#CME:S61!-symbol-description"]={en:["Swiss Franc Futures"],sv:["Terminskontrakt schweizerfranc"]},e.exports["#TVC:DEU30-symbol-description"]={en:["DAX Index"],sv:["DAX-indexet"]},e.exports["#BCHEUR-symbol-description"]={en:["Bitcoin Cash / Euro"],
sv:["Bitcoin Cash / Euro"]},e.exports["#TVC:ZXY-symbol-description"]={en:["New Zealand Dollar Currency Index"],sv:["Nyzeeländsk dollar valutaindex"]},e.exports["#MIL:FTSEMIB-symbol-description"]={en:["FTSE MIB Index"],sv:["FTSE MIB-indexet"]},e.exports["#XETR:DAX-symbol-description"]={en:["DAX Index"],sv:["DAX-indexet"]},e.exports["#MOEX:IMOEX-symbol-description"]={en:["MOEX Russia Index"],sv:["MOEX Russia Index"]},e.exports["#FX:US30-symbol-description"]={en:["Dow Jones Industrial Average Index"],sv:["Dow Jones Industrial genomsnittsindex"]},e.exports["#MOEX:RUAL-symbol-description"]={en:["United Company RUSAL PLC"],sv:["United Company RUSAL PLC"]},e.exports["#MOEX:MX2!-symbol-description"]={en:["MICEX Index Futures"],sv:["MICEX index terminer"]},e.exports["#NEOUSD-symbol-description"]={en:["NEO / U.S. Dollar"],sv:["NEO / U.S. Dollar"]},e.exports["#XMRUSD-symbol-description"]={en:["Monero / U.S. Dollar"],sv:["Monero / U.S. Dollar"]},e.exports["#ZECUSD-symbol-description"]={en:["Zcash / U.S. Dollar"],sv:["Zcash / U.S. Dollar"]},e.exports["#TVC:CAC-symbol-description"]={en:["CAC 40 Index"],sv:["CAC 40 Index"]},e.exports["#NASDAQ:ZS-symbol-description"]={en:["Zscaler Inc"],sv:["Zscaler Inc"]},e.exports["#TVC:GB10Y-symbol-description"]={en:["UK Government Bonds 10 YR Yield"],sv:["Brittiska statsobligationer 10 år avkastning"]},e.exports["#TVC:AU10Y-symbol-description"]={en:["Australia Government Bonds 10 YR Yield"],sv:["Australiensiska statsobligationer 10 år avkastning"]},e.exports["#TVC:CN10Y-symbol-description"]={en:["China Government Bonds 10 YR Yield"],sv:["Kina statsobligationer 10 år avkastning"]},e.exports["#TVC:DE10Y-symbol-description"]={en:["German Government Bonds 10 YR Yield"],sv:["Tyska statsobligationer 10 år avkastning"]},e.exports["#TVC:ES10Y-symbol-description"]={en:["Spain Government Bonds 10 YR Yield"],sv:["Spanska statsobligationer 10 år avkastning"]},e.exports["#TVC:FR10Y-symbol-description"]={en:["France Government Bonds 10 YR Yield"],sv:["Franska statsobligationer 10 år avkastning"]},e.exports["#TVC:IN10Y-symbol-description"]={en:["India Government Bonds 10 YR Yield"],sv:["Indien statsobligationer 10 år avkastning"]},e.exports["#TVC:IT10Y-symbol-description"]={en:["Italy Government Bonds 10 YR Yield"],sv:["Italien statsobligationer 10 år avkastning"]},e.exports["#TVC:JP10Y-symbol-description"]={en:["Japan Government Bonds 10 YR Yield"],sv:["Japan statsobligationer 10 år avkastning"]},e.exports["#TVC:KR10Y-symbol-description"]={en:["Korea Government Bonds 10 YR Yield"],sv:["Korea statsobligationer 10 år avkastning"]},e.exports["#TVC:MY10Y-symbol-description"]={en:["Malaysia Government Bonds 10 YR Yield"],sv:["Malaysias statsobligationer 10 år avkastning"]},e.exports["#TVC:PT10Y-symbol-description"]={en:["Portugal Government Bonds 10 YR Yield"],sv:["Portugal statsobligationer 10 år avkastning"]},e.exports["#TVC:TR10Y-symbol-description"]={en:["Turkey Government Bonds 10 YR Yield"],sv:["Turkey Government Bonds 10 år avkastning"]},e.exports["#TVC:US02Y-symbol-description"]={
en:["US Government Bonds 2 YR Yield"],sv:["US statsobligationer 2 år avkastning"]},e.exports["#TVC:US05Y-symbol-description"]={en:["US Government Bonds 5 YR Yield"],sv:["US statsobligationer 5 år avkastning"]},e.exports["#TVC:US10Y-symbol-description"]={en:["US Government Bonds 10 YR Yield"],sv:["US statsobligationer 10 år avkastning"]},e.exports["#INDEX:TWII-symbol-description"]={en:["Taiwan Weighted Index"],sv:["Taiwan viktat index"]},e.exports["#CME:J61!-symbol-description"]={en:["Japanese Yen Futures"],sv:["Japanska Yen terminer"]},e.exports["#CME_MINI:J71!-symbol-description"]={en:["Japanese Yen E-mini Futures"],sv:["Japanska Yen E - mini terminer"]},e.exports["#CME_MINI:WM1!-symbol-description"]={en:["E-micro Japanese Yen / U.S. Dollar Futures"],sv:["E - micro Japanska Yen / U.S. Dollar terminer"]},e.exports["#CME:M61!-symbol-description"]={en:["Mexican Peso Futures"],sv:["Mexikanska Peso terminer"]},e.exports["#CME:T61!-symbol-description"]={en:["South African Rand Futures"],sv:["Syd Afrikanska Rand terminer"]},e.exports["#CME:SK1!-symbol-description"]={en:["Swedish Krona Futures"],sv:["Svenska Krona terminer"]},e.exports["#CME:QT1!-symbol-description"]={en:["Chinese Renminbi / U.S. Dollar Futures"],sv:["Kinesiska Renminbi / U.S. Dollar terminer"]},e.exports["#COMEX:AUP1!-symbol-description"]={en:["Aluminum MW U.S. Transaction Premium Platts (25MT) Futures"],sv:["Aluminum MW U.S. Transaction Premium Platts (25MT) terminer"]},e.exports["#CME:L61!-symbol-description"]={en:["Brazilian Real Futures"],sv:["Brasilianska Real terminer"]},e.exports["#CME:WP1!-symbol-description"]={en:["Polish Zloty Futures"],sv:["Polska Zloty terminer"]},e.exports["#CME:N61!-symbol-description"]={en:["New Zealand Dollar Futures"],sv:["Nya Zeeland Dollar terminer"]},e.exports["#CME_MINI:MG1!-symbol-description"]={en:["E-micro Australian Dollar / U.S. Dollar Futures"],sv:["E-micro Australiska Dollar / U.S. Dollar terminer"]},e.exports["#CME_MINI:WN1!-symbol-description"]={en:["E-micro Swiss Franc / U.S. Dollar Futures"],sv:["E-micro Schweiz Franc / U.S. Dollar terminer"]},e.exports["#CME_MINI:MF1!-symbol-description"]={en:["E-micro Euro / U.S. Dollar Futures"],sv:["E-micro Euro / U.S. Dollar terminer"]},e.exports["#CME_MINI:E71!-symbol-description"]={en:["Euro E-mini Futures"],sv:["Euro E-mini terminer"]},e.exports["#CBOT:ZK1!-symbol-description"]={en:["Denatured Fuel Ethanol Futures"],sv:["Denatured Fuel Ethanol terminer"]},e.exports["#CME_MINI:MB1!-symbol-description"]={en:["E-micro British Pound / U.S. Dollar Futures"],sv:["E-micro Brittiska Pund / U.S. Dollar terminer"]},e.exports["#NYMEX_MINI:QU1!-symbol-description"]={en:["E-mini Gasoline Futures"],sv:["E-mini Bensin terminer"]},e.exports["#NYMEX_MINI:QX1!-symbol-description"]={en:["E-mini Heating Oil Futures"],sv:["E-mini Olja terminer"]},e.exports["#COMEX_MINI:QC1!-symbol-description"]={en:["E-mini Copper Futures"],sv:["E-mini Koppar terminer"]},e.exports["#NYMEX_MINI:QG1!-symbol-description"]={en:["E-mini Natural Gas Futures"],sv:["E-mini Naturgas terminer"]},
e.exports["#CME:E41!-symbol-description"]={en:["U.S. Dollar / Turkish Lira Futures"],sv:["U.S. Dollar / Turkisk Lira terminer"]},e.exports["#COMEX_MINI:QI1!-symbol-description"]={en:["Silver (Mini) Futures"],sv:["Silver (Mini) terminer"]},e.exports["#CME:DL1!-symbol-description"]={en:["Milk, Class III Futures"],sv:["Milk, Class III terminer"]},e.exports["#NYMEX:UX1!-symbol-description"]={en:["Uranium Futures"],sv:["Uran terminer"]},e.exports["#CBOT:BO1!-symbol-description"]={en:["Soybean Oil Futures"],sv:["Sojabönor terminer"]},e.exports["#CME:HE1!-symbol-description"]={en:["Lean Hogs Futures"],sv:["Lean Hogs terminer"]},e.exports["#NYMEX:IAC1!-symbol-description"]={en:["Newcastle Coal Futures"],sv:["Newcastle kol terminer"]},e.exports["#NYMEX_MINI:QM1!-symbol-description"]={en:["E-mini Light Crude Oil Futures"],sv:["E-mini lätt råolja terminer"]},e.exports["#NYMEX:JMJ1!-symbol-description"]={en:["Mini Brent Financial Futures"],sv:["Mini Brent finansiella terminer"]},e.exports["#COMEX:AEP1!-symbol-description"]={en:["Aluminium European Premium Futures"],sv:["Europeiska Aluminium Premium terminer"]},e.exports["#CBOT:ZQ1!-symbol-description"]={en:["30 Day Federal Funds Interest Rate Futures"],sv:["30-dagars Federal Funds räntesats terminer"]},e.exports["#CME:LE1!-symbol-description"]={en:["Live Cattle Futures"],sv:["Levande boskap terminer"]},e.exports["#CME:UP1!-symbol-description"]={en:["Swiss Franc / Japanese Yen Futures"],sv:["Schweiz Franc / Japanska Yen terminer"]},e.exports["#CBOT:ZN1!-symbol-description"]={en:["10 Year T-Note Futures"],sv:["10 år T-Note terminer"]},e.exports["#CBOT:ZB1!-symbol-description"]={en:["T-Bond Futures"],sv:["T-Bond terminer"]},e.exports["#CME:GF1!-symbol-description"]={en:["Feeder Cattle Futures"],sv:["Nötkreatur terminer"]},e.exports["#CBOT:UD1!-symbol-description"]={en:["Ultra T-Bond Futures"],sv:["Ultra T-Bond terminer"]},e.exports["#CME:I91!-symbol-description"]={en:["CME Housing Futures — Washington DC"],sv:["CME Hus terminer - Washington DC"]},e.exports["#CBOT:ZO1!-symbol-description"]={en:["Oat Futures"],sv:["havre terminer"]},e.exports["#CBOT:ZM1!-symbol-description"]={en:["Soybean Meal Futures"],sv:["Sojabönsmjöl terminer"]},e.exports["#CBOT_MINI:XN1!-symbol-description"]={en:["Corn Mini Futures"],sv:["Majs Mini terminer"]},e.exports["#CBOT:ZC1!-symbol-description"]={en:["Corn Futures"],sv:["Majs terminer"]},e.exports["#CME:LS1!-symbol-description"]={en:["Lumber Futures"],sv:["Timmer terminer"]},e.exports["#CBOT_MINI:XW1!-symbol-description"]={en:["Wheat Mini Futures"],sv:["Vete mini terminer"]},e.exports["#CBOT_MINI:XK1!-symbol-description"]={en:["Soybean Mini Futures"],sv:["Sojabönor mini terminer"]},e.exports["#CBOT:ZS1!-symbol-description"]={en:["Soybean Futures"],sv:["Sojabönor terminer"]},e.exports["#NYMEX:PA1!-symbol-description"]={en:["Palladium Futures"],sv:["Palladium terminer"]},e.exports["#CME:FTU1!-symbol-description"]={en:["E-mini FTSE 100 Index USD Futures"],sv:["E-mini FTSE 100 Index USD terminer"]},e.exports["#CBOT:ZR1!-symbol-description"]={
en:["Rice Futures"],sv:["Ris terminer"]},e.exports["#COMEX_MINI:GR1!-symbol-description"]={en:["Gold (E-micro) Futures"],sv:["Guld (e-micro) terminer"]},e.exports["#COMEX_MINI:QO1!-symbol-description"]={en:["Gold (Mini) Futures"],sv:["Guld (mini) terminer"]},e.exports["#CME_MINI:RL1!-symbol-description"]={en:["E-mini Russell 1000 Futures"],sv:["E-mini Russell 1000 terminer"]},e.exports["#CME_MINI:EW1!-symbol-description"]={en:["S&P 400 Midcap E-mini Futures"],sv:["S&P 400 Midcap E-mini terminer"]},e.exports["#COMEX:LD1!-symbol-description"]={en:["Lead Futures"],sv:["Bly terminer"]},e.exports["#CME_MINI:ES1!-symbol-description"]={en:["S&P 500 E-mini Futures"],sv:["S&P 500 E-mini terminer"]},e.exports["#TVC:SA40-symbol-description"]={en:["South Africa Top 40 Index"],sv:["Sydafrika topp 40 Index"]},e.exports["#BMV:ME-symbol-description"]={en:["S&P/BMV IPC Index"],sv:["IPC Mexico Index"]},e.exports["#BCBA:IMV-symbol-description"]={en:["S&P MERVAL Index"],sv:["MERVAL Index"]},e.exports["#HSI:HSI-symbol-description"]={en:["Hang Seng Index"],sv:["Hang Seng Index"]},e.exports["#BVL:SPBLPGPT-symbol-description"]={en:["S&P / BVL Peru General Index (PEN)"],sv:["S&P / BVL Peru General Index (PEN)"]},e.exports["#EGX:EGX30-symbol-description"]={en:["EGX 30 Index"],sv:["EGX 30 Price Return Index"]},e.exports["#BVC:IGBC-symbol-description"]={en:["Indice General de la Bolsa de Valores de Colombia"],sv:["Indice General de la Bolsa de Valores de Colombia"]},e.exports["#TWSE:TAIEX-symbol-description"]={en:["Taiwan Capitalization Weighted Stock Index"],sv:["Taiwan Capitalization viktat aktieindex"]},e.exports["#QSE:GNRI-symbol-description"]={en:["QE Index"],sv:["QE Index"]},e.exports["#BME:IBC-symbol-description"]={en:["IBEX 35 Index"],sv:["IBEX 35 Index"]},e.exports["#NZX:NZ50G-symbol-description"]={en:["S&P / NZX 50 Index Gross"],sv:["S&P / NZX 50 brutto index"]},e.exports["#SIX:SMI-symbol-description"]={en:["Swiss Market Index"],sv:["Schweiz marknadsindex"]},e.exports["#SZSE:399001-symbol-description"]={en:["Shenzhen Component Index"],sv:["SZSE komponentindex"]},e.exports["#TADAWUL:TASI-symbol-description"]={en:["Tadawul All Shares Index"],sv:["Tadawul all aktier index"]},e.exports["#IDX:COMPOSITE-symbol-description"]={en:["IDX Composite Index"],sv:["IDX kompositindex"]},e.exports["#EURONEXT:PX1-symbol-description"]={en:["CAC 40 Index"],sv:["CAC 40 Index"]},e.exports["#OMXHEX:OMXH25-symbol-description"]={en:["OMX Helsinki 25 Index"],sv:["OMX Helsingfors 25 index"]},e.exports["#EURONEXT:BEL20-symbol-description"]={en:["BEL 20 Index"],sv:["BEL 20 Index"]},e.exports["#TVC:STI-symbol-description"]={en:["Straits Times Index"],sv:["Straits Times index"]},e.exports["#DFM:DFMGI-symbol-description"]={en:["DFM Index"],sv:["DFM Index"]},e.exports["#TVC:KOSPI-symbol-description"]={en:["Korea Composite Stock Price Index"],sv:["Korea kompositprisindex"]},e.exports["#FTSEMYX:FBMKLCI-symbol-description"]={en:["FTSE Bursa Malaysia KLCI Index"],sv:["FTSE Bursa Malaysia KLCI Index"]},e.exports["#TASE:TA35-symbol-description"]={en:["TA-35 Index"],
sv:["TA-35 index"]},e.exports["#OMXSTO:OMXS30-symbol-description"]={en:["OMX Stockholm 30 Index"],sv:["OMX Stockholm 30 Index"]},e.exports["#OMXICE:OMXI8-symbol-description"]={en:["OMX Iceland 8 Index"],sv:["OMX island 8 index"]},e.exports["#NSENG:NSE30-symbol-description"]={en:["NSE 30 Index"],sv:["NSE 30 Index"]},e.exports["#BAHRAIN:BSEX-symbol-description"]={en:["Bahrain All Share Index"],sv:["Bahran alla aktier index"]},e.exports["#OMXTSE:OMXTGI-symbol-description"]={en:["OMX Tallinn Gross Index"],sv:["OMX Tallinn GI"]},e.exports["#OMXCOP:OMXC25-symbol-description"]={en:["OMX Copenhagen 25 Index"],sv:["OMX Köpenhamn 25 index"]},e.exports["#OMXRSE:OMXRGI-symbol-description"]={en:["OMX Riga Gross Index"],sv:["OMX Riga GI"]},e.exports["#BELEX:BELEX15-symbol-description"]={en:["BELEX 15 Index"],sv:["BELEX 15 Index"]},e.exports["#OMXVSE:OMXVGI-symbol-description"]={en:["OMX Vilnius Gross Index"],sv:["OMX Vilnius GI"]},e.exports["#EURONEXT:AEX-symbol-description"]={en:["AEX Index"],sv:["AEX Index"]},e.exports["#CBOE:VIX-symbol-description"]={en:["Volatility S&P 500 Index"],sv:["Volatility S&P 500 Index"]},e.exports["#NASDAQ:XAU-symbol-description"]={en:["PHLX Gold and Silver Sector Index"],sv:["PHLX Guld och Silver sektorindex"]},e.exports["#DJ:DJUSCL-symbol-description"]={en:["Dow Jones U.S. Coal Index"],sv:["Dow Jones U.S. kol Index"]},e.exports["#DJ:DJCIKC-symbol-description"]={en:["Dow Jones Commodity Index Coffee"],sv:["Dow Jones Kaffe varuindex"]},e.exports["#DJ:DJCIEN-symbol-description"]={en:["Dow Jones Commodity Index Energy"],sv:["Dow Jones energi varuindex"]},e.exports["#NASDAQ:OSX-symbol-description"]={en:["PHLX Oil Service Sector Index"],sv:["PHLX Olja sektorindex"]},e.exports["#DJ:DJCISB-symbol-description"]={en:["Dow Jones Commodity Index Sugar"],sv:["Dow Jones socker varuindex"]},e.exports["#DJ:DJCICC-symbol-description"]={en:["Dow Jones Commodity Index Cocoa"],sv:["Dow Jones kakao varuindex"]},e.exports["#DJ:DJCIGR-symbol-description"]={en:["Dow Jones Commodity Index Grains"],sv:["Dow Jones Korn varuindex"]},e.exports["#DJ:DJCIAGC-symbol-description"]={en:["Dow Jones Commodity Index Agriculture Capped Component"],sv:["Dow Jones jordbruksindex"]},e.exports["#DJ:DJCISI-symbol-description"]={en:["Dow Jones Commodity Index Silver"],sv:["Dow Jones Silverindex"]},e.exports["#DJ:DJCIIK-symbol-description"]={en:["Dow Jones Commodity Index Nickel"],sv:["Dow Jones Nickelindex"]},e.exports["#NASDAQ:HGX-symbol-description"]={en:["PHLX Housing Sector Index"],sv:["PHLX Hus sektorindex"]},e.exports["#DJ:DJCIGC-symbol-description"]={en:["Dow Jones Commodity Index Gold"],sv:["Dow Jones guldindex"]},e.exports["#SP:SPGSCI-symbol-description"]={en:["S&P Goldman Sachs Commodity Index"],sv:["S&P Goldman Sachs råvaruindex"]},e.exports["#NASDAQ:UTY-symbol-description"]={en:["PHLX Utility Sector Index"],sv:["PHLX verktygsektorindex"]},e.exports["#DJ:DJU-symbol-description"]={en:["Dow Jones Utility Average Index"],sv:["Dow Jones Utility Average Index"]},e.exports["#SP:SVX-symbol-description"]={en:["S&P 500 Value Index"],
sv:["S&P 500 Value Index"]},e.exports["#SP:OEX-symbol-description"]={en:["S&P 100 Index"],sv:["S&P 100 Index"]},e.exports["#CBOE:OEX-symbol-description"]={en:["S&P 100 Index"],sv:["S&P 100 Index"]},e.exports["#NASDAQ:SOX-symbol-description"]={en:["Philadelphia Semiconductor Index"],sv:["Philadelphia halvledarindex"]},e.exports["#RUSSELL:RUI-symbol-description"]={en:["Russell 1000 Index"],sv:["Russell 1000 Index"]},e.exports["#RUSSELL:RUA-symbol-description"]={en:["Russell 3000 Index"],sv:["Russell 3000 Index"]},e.exports["#RUSSELL:RUT-symbol-description"]={en:["Russell 2000 Index"],sv:["Russell 2000 Index"]},e.exports["#NYSE:XMI-symbol-description"]={en:["NYSE ARCA Major Market Index"],sv:["NYSE ARCA stormarknadsindex"]},e.exports["#NYSE:XAX-symbol-description"]={en:["AMEX Composite Index"],sv:["AMEX Composite Index"]},e.exports["#NASDAQ:NDX-symbol-description"]={en:["Nasdaq 100 Index"],sv:["Nasdaq 100 Index"]},e.exports["#NASDAQ:IXIC-symbol-description"]={en:["Nasdaq Composite Index"],sv:["NASDAQ Composite Index"]},e.exports["#DJ:DJT-symbol-description"]={en:["Dow Jones Transportation Average Index"],sv:["Dow Jones Transportindex"]},e.exports["#NYSE:NYA-symbol-description"]={en:["NYSE Composite Index"],sv:["NYSE kompositindex"]},e.exports["#NYMEX:CJ1!-symbol-description"]={en:["Cocoa Futures"],sv:["Kakao terminer"]},e.exports["#USDILS-symbol-description"]={en:["U.S. Dollar / Israeli Shekel"],sv:["USD / Israel Shekel"]},e.exports["#TSXV:F-symbol-description"]={en:["Fiore Gold Inc"],sv:["Fiore Gold Inc"]},e.exports["#SIX:F-symbol-description"]={en:["Ford Motor Company"],sv:["Ford Motor Company"]},e.exports["#BMV:F-symbol-description"]={en:["Ford Motor Company"],sv:["Ford Motor Company"]},e.exports["#TWII-symbol-description"]={en:["Taiwan Weighted Index"],sv:["Taiwan viktat index"]},e.exports["#TVC:PL10Y-symbol-description"]={en:["Poland Government Bonds 10 YR Yield"],sv:["Polen statsobligationer 10 års avkastning."]},e.exports["#TVC:PL05Y-symbol-description"]={en:["Poland Government Bonds 5 YR Yield"],sv:["Polen statsobligationer 5 års-räntan"]},e.exports["#SET:GC-symbol-description"]={en:["Global Connections Public Company"],sv:["Global Connections Public Company"]},e.exports["#TSX:GC-symbol-description"]={en:["Great Canadian Gaming Corporation"],sv:["Great Canadian Gaming Corporation"]},e.exports["#TVC:FTMIB-symbol-description"]={en:["Milano Italia Borsa Index"],sv:["Milano Italia Borsa Index"]},e.exports["#OANDA:SPX500USD-symbol-description"]={en:["S&P 500 Index"],sv:["S&P 500-index"]},e.exports["#BMV:CT-symbol-description"]={en:["China SX20 RT"],sv:["China SX20 RT"]},e.exports["#TSXV:CT-symbol-description"]={en:["Centenera Mining Corporation"],sv:["Centenera Mining Corporation"]},e.exports["#BYBIT:ETHUSD-symbol-description"]={en:["ETHUSD Perpetual Contract"],sv:["ETHUSD Icke-uppsägbart terminskontrakt"]},e.exports["#BYBIT:XRPUSD-symbol-description"]={en:["XRPUSD Perpetual Contract"],sv:["XRPUSD Icke-uppsägbart terminskontrakt"]},e.exports["#BYBIT:BTCUSD-symbol-description"]={en:["BTCUSD Perpetual Contract"],
sv:["BTCUSD Icke-uppsägbart terminskontrakt"]},e.exports["#BITMEX:ETHUSD-symbol-description"]={en:["ETHUSD Perpetual Futures Contract"],sv:["ETHUSD Icke-uppsägbart terminskontrakt"]},e.exports["#DERIBIT:BTCUSD-symbol-description"]={en:["BTCUSD Perpetual Futures Contract"],sv:["BTCUSD Icke-uppsägbart terminskontrakt"]},e.exports["#DERIBIT:ETHUSD-symbol-description"]={en:["ETHUSD Perpetual Futures Contract"],sv:["ETHUSD Icke-uppsägbart terminskontrakt"]},e.exports["#USDHUF-symbol-description"]={en:["U.S. Dollar / Hungarian Forint"],sv:["Amerikansk dollar till ungersk forint"]},e.exports["#USDTHB-symbol-description"]={en:["U.S. Dollar / Thai Baht"],sv:["Dollar till baht"]},e.exports["#FOREXCOM:US2000-symbol-description"]={en:["US Small Cap 2000"],sv:["US Small Cap 2000"]},e.exports["#TSXV:PBR-symbol-description"]={en:["Para Resources Inc"],sv:["Para Resources Inc"]},e.exports["#NYSE:SI-symbol-description"]={en:["Silvergate Capital Corporation"],sv:["Silvergate Capital Corporation"]},e.exports["#NASDAQ:LE-symbol-description"]={en:["Lands' End Inc"],sv:["Lands' End Inc"]},e.exports["#CME:CB1!-symbol-description"]={en:["Butter Futures-Cash (Continuous: Current contract in front)"],sv:["Terminer på smör i kontanter (Pågående: Aktuellt kontrakt främst)"]},e.exports["#LSE:SCHO-symbol-description"]={en:["Scholium Group Plc Ord 1P"],sv:["Scholium Group Plc Ord 1P"]},e.exports["#NEO:HE-symbol-description"]={en:["Hanwei Energy Services Corp."],sv:["Hanwei Energy Services Corp."]},e.exports["#NYSE:HE-symbol-description"]={en:["Hawaiian Electric Industries"],sv:["Hawaiian Electric Industries"]},e.exports["#OMXCOP:SCHO-symbol-description"]={en:["Schouw & Co A/S"],sv:["Schouw & Co A/S"]},e.exports["#TSX:HE-symbol-description"]={en:["Hanwei Energy Services Corp."],sv:["Hanwei Energy Services Corp."]},e.exports["#BSE:ITI-symbol-description"]={en:["ITI Ltd"],sv:["ITI Ltd"]},e.exports["#NSE:ITI-symbol-description"]={en:["Indian Telephone Industries Limited"],sv:["Indian Telephone Industries Limited"]},e.exports["#TSX:LS-symbol-description"]={en:["Middlefield Healthcare & Life Sciences Dividend Fund"],sv:["Middlefield Healthcare & Life Sciences Dividend Fund"]},e.exports["#BITMEX:XBT-symbol-description"]={en:["Bitcoin / U.S. Dollar Index"],sv:["Bitcoin / USD Index"]},e.exports["#CME_MINI:RTY1!-symbol-description"]={en:["E-Mini Russell 2000 Index Futures"],sv:["E-Mini Russell 2000 Indexterminer"]},e.exports["#CRYPTOCAP:TOTAL-symbol-description"]={en:["Crypto Total Market Cap, $"],sv:["Det Totala Marknadsvärdet för Krypto, $"]},e.exports["#ICEUS:DX1!-symbol-description"]={en:["U.S. Dollar Index Futures"],sv:["Amerikanska dollar terminskontrakt"]},e.exports["#NYMEX:TT1!-symbol-description"]={en:["Cotton Futures"],sv:["Bomullsterminer"]},e.exports["#PHEMEX:BTCUSD-symbol-description"]={en:["BTC Perpetual Futures Contract"],sv:["BTC Perpetual Futures-kontrakt"]},e.exports["#PHEMEX:ETHUSD-symbol-description"]={en:["ETH Perpetual Futures Contract"],sv:["ETH evigt terminskontrakt"]},e.exports["#PHEMEX:XRPUSD-symbol-description"]={
en:["XRP Perpetual Futures Contract"],sv:["XRP  evigt terminskontrakt"]},e.exports["#PHEMEX:LTCUSD-symbol-description"]={en:["LTC Perpetual Futures Contract"],sv:["LTC  evigt terminskontrakt"]},e.exports["#BITCOKE:BCHUSD-symbol-description"]={en:["BCH Quanto Swap"],sv:["BCH Quanto Swap"]},e.exports["#BITCOKE:BTCUSD-symbol-description"]={en:["BTC Quanto Swap"],sv:["BTC Quanto Swap"]},e.exports["#BITCOKE:ETHUSD-symbol-description"]={en:["ETH Quanto Swap"],sv:["ETH Quanto Swap"]},e.exports["#BITCOKE:LTCUSD-symbol-description"]={en:["LTC Quanto Swap"],sv:["LTC Quanto Swap"]},e.exports["#TVC:CA10-symbol-description"]={en:["Canadian Government Bonds, 10 YR"],sv:["Kanadensiska statsobligationer 10 år"]},e.exports["#TVC:CA10Y-symbol-description"]={en:["Canadian Government Bonds 10 YR Yield"],sv:["Kanadensiska statsobligationer 10 års avkastning"]},e.exports["#TVC:ID10Y-symbol-description"]={en:["Indonesia Government Bonds 10 YR Yield"],sv:["Kanadindonesiska statsobligationer 10 års avkastning"]},e.exports["#TVC:NL10-symbol-description"]={en:["Netherlands Government Bonds, 10 YR"],sv:["Nederländska statsobligationer 10 år"]},e.exports["#TVC:NL10Y-symbol-description"]={en:["Netherlands Government Bonds 10 YR Yield"],sv:["Nederländska statsobligationer 10 års avkastning"]},e.exports["#TVC:NZ10-symbol-description"]={en:["New Zealand Government Bonds, 10 YR"],sv:["Nya Zeeländska statsobligationer 10 år"]},e.exports["#TVC:NZ10Y-symbol-description"]={en:["New Zealand Government Bonds 10 YR Yield"],sv:["Nya Zeeländska statsobligationer 10 års avkastning"]},e.exports["#SOLUSD-symbol-description"]={en:["Solana / U.S. Dollar"],sv:["Solana / Amerikanska dollar"]},e.exports["#LUNAUSD-symbol-description"]={en:["Luna / U.S. Dollar"],sv:["Luna / Amerikanska dollar"]},e.exports["#UNIUSD-symbol-description"]={en:["Uniswap / U.S. Dollar"],sv:["Uniswap / Amerikanska dollar"]},e.exports["#LTCBRL-symbol-description"]={en:["Litecoin / Brazilian Real"],sv:["Litecoin / Brasilianska real"]},e.exports["#ETCEUR-symbol-description"]={en:["Ethereum Classic / Euro"],sv:["Ethereum Classic/Euro"]},e.exports["#ETHKRW-symbol-description"]={en:["Ethereum / South Korean Won"],sv:["Ethereum / Sydkoreanska won"]},e.exports["#BTCRUB-symbol-description"]={en:["Bitcoin / Russian Ruble"],sv:["Bitcoin / Ryska rubel"]},e.exports["#BTCTHB-symbol-description"]={en:["Bitcoin / Thai Baht"],sv:["Bitcoin/Thailändska baht"]},e.exports["#ETHTHB-symbol-description"]={en:["Ethereum / Thai Baht"],sv:["Ethereum/Thailändska bath"]},e.exports["#TVC:EU10YY-symbol-description"]={en:["Euro Government Bonds 10 YR Yield"],sv:["EU statsobligationer 10 års avkastning"]},e.exports["#NASDAQ:LCID-symbol-description"]={en:["Lucid Group, Inc."]},e.exports["#TADAWUL:2370-symbol-description"]={en:["Middle East Specialized Cables Co."]},e.exports["#NASDAQ:GOOG-symbol-description"]={en:["#NASDAQ:GOOG-symbol-description"]},e.exports["#NASDAQ:GOOGL-symbol-description"]={en:["#NASDAQ:GOOGL-symbol-description"]}}}]);