'use client';
import { DeleteIcon, UploadIcon } from '@/assets/icons';
import { errorMsg, successMsg } from '@/libs/toast';
import RequestFactory from '@/services/RequestFactory';
import Image from 'next/image';
import { useCallback, useState } from 'react';
import { FileRejection, useDropzone } from 'react-dropzone';

interface UploadMediaProps {
  preview: any;
  setPreview: (preview: any) => void;
  setImageUrl: (imageUrl: string | null) => void;
}

const UploadMedia = ({
  preview,
  setPreview,
  setImageUrl,
}: UploadMediaProps) => {
  const [isUploading, setIsUploading] = useState(false);

  const uploadImageToPinata = useCallback(
    async (file: File) => {
      try {
        setIsUploading(true);

        const imageRequest = RequestFactory.getRequest('ImageRequest');
        const response = await imageRequest.uploadImage(file);

        if (response && response.imageUrl) {
          setImageUrl(response.imageUrl);
          successMsg('Image uploaded successfully');
        }
      } catch (error: any) {
        console.error('Error uploading image:', error);

        if (error.response?.status === 401) {
          errorMsg(
            'Authentication required. Please connect your wallet and try again.',
          );
        } else {
          errorMsg(error?.message || 'Failed to upload image');
        }
        setImageUrl(null);
      } finally {
        setIsUploading(false);
      }
    },
    [setImageUrl],
  );

  const onDrop = useCallback(
    (acceptedFiles: Array<File>, fileRejections: FileRejection[]) => {
      if (fileRejections.length > 0) {
        errorMsg('Upload failed');
        return;
      }

      const selectedFile = acceptedFiles[0];

      const fileReader = new FileReader();
      fileReader.onload = function () {
        setPreview(fileReader.result);
      };
      fileReader.readAsDataURL(selectedFile);

      uploadImageToPinata(selectedFile);
    },
    [],
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: {
      'image/*': [],
      'video/*': [],
    },
    maxFiles: 1,
    onDrop,
  });

  return (
    <>
      <div {...getRootProps()} className="cursor-pointer">
        <input
          {...getInputProps()}
          multiple={false}
          accept="video/*, image/*"
        />

        <label className="block text-xs mb-2 font-semibold text-white-700">
          Image or video <span className="text-red-500 text-[10px]">*</span>
        </label>
        {!!preview ? (
          <div className="w-fit relative mx-auto">
            <Image
              src={preview}
              alt="preview"
              width={400}
              height={180}
              className="w-full h-[180px] rounded-[4px]"
            />
            {isUploading && (
              <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded-[4px]">
                <div className="animate-spin h-8 w-8 border-4 border-white border-t-transparent rounded-full"></div>
              </div>
            )}
            <DeleteIcon
              className="w-8 h-8 absolute top-2.5 right-2.5 cursor-pointer"
              onClick={(e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
                e.stopPropagation();
                setPreview(undefined);
                setImageUrl(null);
              }}
            />
          </div>
        ) : (
          <div className="p-1 md:p-6 flex items-center justify-center flex-col gap-3 bg-white-100 border border-white-100 rounded-[4px] min-h-[180px]">
            <div className="text-center text-[12px] text-white-500">
              {isDragActive
                ? 'Drop the file here ...'
                : 'Drag and drop an image or video here'}
            </div>
            <div className="text-center text-[12px] text-white-0">or</div>

            <button
              type="button"
              className="p-[8px] text-[12px] bg-white-0 text-black-900 rounded-sm flex items-center gap-2"
            >
              <UploadIcon /> Select file
            </button>
          </div>
        )}
      </div>
    </>
  );
};

export default UploadMedia;
