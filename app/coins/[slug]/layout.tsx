import { PageProvider } from './provider';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Print.meme',
  description:
    'Raise money, trade AI. The best hedge fund manager on SUI by raidenx.io',
  openGraph: {
    url: '/opengraph-image.png',
    images: ['/opengraph-image.png'],
  },
};

export default function CoinDetailLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <PageProvider>{children}</PageProvider>;
}
