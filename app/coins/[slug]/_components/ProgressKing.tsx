import AppProgressBar from '@/components/AppProgressBar';
import AppTooltipInformation from '@/components/AppTooltipInformation';
import { useCoinPageContext } from '../provider';

export const ProgressKing = () => {
  const { kingTheHillPercent } = useCoinPageContext();
  return (
    <div className="p-4 pt-3 border-b border-white-50">
      <div className="flex justify-between items-center">
        <div className="body-md-regular-14">
          King of the hill: {kingTheHillPercent}%
        </div>
        <AppTooltipInformation
          content={
            <div>
              When the market cap reaches $0 (~0 sol), all the liquidity in the
              bonding curve will be deposited to raydium and burned. Progression
              increases as more tokens are bought. <br /> <br />
              The bonding curve still has 0 tokens available for sale.
            </div>
          }
        />
      </div>
      <AppProgressBar percent={kingTheHillPercent} />
      <div className="text-white-500 body-xs-light-10">
        Dethrone the current king at $39,010 market cap
      </div>
    </div>
  );
};
