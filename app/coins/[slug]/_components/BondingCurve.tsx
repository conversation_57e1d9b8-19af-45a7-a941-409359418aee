import AppProgressBar from '@/components/AppProgressBar';
import AppTooltipInformation from '@/components/AppTooltipInformation';
import { convertMistToDec } from '@/utils/helper';
import { useCoinPageContext } from '../provider';
import { formatEther } from 'ethers';

export const BondingCurve = () => {
  const { coin } = useCoinPageContext();

  return (
    <div className="p-4 pt-3 border-b border-white-50">
      <div className="flex justify-between items-center">
        <div className="body-md-regular-14">
          Bonding curve progress: {coin?.bondingCurve}%
        </div>
        <AppTooltipInformation
          content={
            <div>
              When the market cap reaches $0 (~0 HYPE), all the liquidity in the
              bonding curve will be deposited to raydium and burned. Progression
              increases as more tokens are bought. <br /> <br />
              The bonding curve still has 0 tokens available for sale.
            </div>
          }
        />
      </div>
      <AppProgressBar percent={coin?.bondingCurve} />

      <div className="text-white-500 body-xs-light-10">
        Graduate this coin to Raydium at $80,4841 market cap there is{' '}
        {formatEther(coin?.virtualHypeReserves)} HYPE in the bounding curve
      </div>
    </div>
  );
};
