'use client';

import Image from 'next/image';
import React, { useState, useRef } from 'react';
import { formatShortAddress } from '@/utils/format';
import AppFallbackImage from '@/components/AppFallbackImage';
import { AppNumber } from '@/components/AppNumber';
import dynamic from 'next/dynamic';
import { AppDataTable } from '@/components/AppDataTable';
import { DefaultCoin } from '@/assets/images';
import AppCopy from '@/components/AppCopy';

const Treasury = () => {
  const [netAssetValue, setNetAssetValue] = useState('0');
  const [treasuryAddress, setTreasuryAddress] = useState('');
  const dataTableRef = useRef<HTMLDivElement | null>(null);

  const getTreasury = async () => {
    const fakeData = Array(10).fill({
      tokenName: 'SAVIOR',
      tokenImage: '/images/TokenLogo.png',
      amount: 232312231,
      marketValue: 323,
      tokenAddress: '0x34dsd...4242',
    });

    setNetAssetValue('31221241233');
    setTreasuryAddress('0x32ds...print');

    return {
      data: fakeData,
      totalPages: 1,
    };
  };

  return (
    <div className="md:bg-[url('../assets/images/banner.png')] bg-[url('../assets/images/banner-mobile.png')] mx-auto bg-cover bg-top bg-no-repeat aspect-[1440/640] bg-black-900 w-full">
      <div className="max-w-[1440px] h-full flex flex-col mx-auto px-4 md:px-[56px] pt-[60px] md:pt-[80px]">
        <div className="text-white-1000">
          <div className="flex items-center gap-2">
            <Image
              src={DefaultCoin}
              width={24}
              height={24}
              alt="Avatar"
              className="w-6 h-6 rounded-full"
            />
            <h1 className="font-semibold text-[24px] leading-[120%]">
              SHRO Treasury
            </h1>
          </div>
          <h2 className="font-normal text-[14px] leading-5 text-white-500 my-2">
            The SHR0 Treasury functions as a reserve, funded by sell-side fees
            collected in various traded tokens post-DEX migration. Holders can
            burn $SHR0 to access and redeem tokens held within this underlying
            SHR0 Fund. Consequently, $SHR0 holders gain a mechanism to invest in
            and benefit from the growth of each new token introduced
          </h2>
        </div>

        <div>
          <div className="flex flex-col md:flex-row md:items-center gap-0 md:gap-1 mb-2">
            <div className="font-normal text-[14px] leading-5 text-white-500 mb-1 md:mb-0 md:mt-2 text-left md:text-right">
              Net Asset Value
            </div>
            <div className="font-semibold text-[24px] leading-[120%] text-white-1000">
              <AppNumber value={netAssetValue} isForUSD />
            </div>
          </div>
          <div className="items-center gap-2 flex mb-4 text-white-1000">
            <div className="truncate font-light text-[14px] leading-5">
              {formatShortAddress(treasuryAddress, 7, 5)}
            </div>
            <div onClick={(e) => e.preventDefault()}>
              <AppCopy message={treasuryAddress} className="" />
            </div>
          </div>
        </div>

        <div className="rounded-2xl border border-white-50 px-2 pb-4">
          <AppDataTable
            ref={dataTableRef}
            getData={getTreasury}
            minWidth={800}
            height="calc(100vh - 450px)"
            renderHeader={() => {
              return (
                <div className="flex items-center font-light text-[12px] leading-[18px] text-white-500 py-[6px] px-2 gap-2 min-h-[36px] w-full">
                  <div className="w-[25%]">Asset</div>
                  <div className="w-[25%]">Amount</div>
                  <div className="w-[25%] ">Market Value</div>
                  <div className="w-[25%]">Address</div>
                </div>
              );
            }}
            renderRow={(item: any) => {
              return (
                <div className="w-full font-normal text-[14px] leading-5 text-white-1000 flex items-center h-[40px] cursor-pointer">
                  <div className="w-[25%] flex items-center gap-2 py-[10px] px-2">
                    <AppFallbackImage
                      src={item.tokenImage || ''}
                      alt="SUI"
                      width={24}
                      height={24}
                      fallbackSrc={DefaultCoin}
                    />
                    <div>{item?.tokenName}</div>
                  </div>
                  <div className="w-[25%] py-[10px] px-2">
                    <AppNumber value={item?.amount} />
                  </div>
                  <div className="w-[25%] py-[10px] px-2">
                    <AppNumber value={item?.marketValue} isForUSD />
                  </div>
                  <div className="w-[25%] flex items-center justify-between py-[10px] px-2">
                    <div className="truncate">
                      {formatShortAddress(item?.tokenAddress, 8, 8)}
                    </div>
                    <div onClick={(e) => e.preventDefault()}>
                      <AppCopy
                        message={item?.tokenAddress}
                        className="w-[16px] h-[16px]"
                      />
                    </div>
                  </div>
                </div>
              );
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default dynamic(() => Promise.resolve(Treasury), { ssr: false });
