'use client';
import React, { useEffect, useState } from 'react';
import { ArrowLeftIcon, ChevronRightIcon, CopyIcon } from '@/assets/icons';
import Link from 'next/link';
import { AppButton } from '@/components/AppButton';
import { copyToClipboard } from '@/utils/helper';
import { useParams, useRouter } from 'next/navigation';
import { AppNumber } from '@/components/AppNumber';
import AppAddress from '@/components/AppAddress';
import { formatUnixTimestamp } from '@/utils/format';
import moment from 'moment';
import Image from 'next/image';
import { CoinLockDto } from '@/services/CoinLockRequest';
import RequestFactory from '@/services/RequestFactory';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { useTokenLock } from '@/hooks/useTokenLock';

const TokenLockPageDetail = () => {
  const { slug }: { slug: string } = useParams();
  const router = useRouter();
  const contractId = decodeURIComponent(slug);
  const [tokenLock, setTokenLock] = useState<CoinLockDto | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const { accessToken } = useSelector((state: RootState) => state.user);
  const coinLockRequest = RequestFactory.getRequest('CoinLockRequest');

  const { withdrawLock, isWithdrawing, clearError } = useTokenLock();

  const getTokenLock = async () => {
    if (!contractId || !accessToken) {
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      const response = await coinLockRequest.getCoinLockDetail(contractId);
      setTokenLock(response);
    } catch (error) {
      console.error('Error fetching token lock detail:', error);
      setError('Failed to load token lock details. Please try again.');
      setTokenLock(null);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    getTokenLock();
  }, [contractId, accessToken]);

  const isInvalidWithdraw =
    !tokenLock ||
    moment(tokenLock.endTime).isAfter(moment()) ||
    tokenLock.closed;

  const handleWithdraw = async () => {
    if (!tokenLock || isInvalidWithdraw) {
      return;
    }

    try {
      clearError();

      await withdrawLock(contractId);

      setTokenLock((prev) => (prev ? { ...prev, closed: true } : null));
    } catch (error) {
      console.error('Error during withdrawal process:', error);
      setError('Failed to withdraw tokens. Please try again later.');
    }
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="mx-auto md:px-[56px] p-4 md:py-[24px]">
        <div className="flex flex-col md:mt-[55px] mt-[14px]">
          <div className="flex items-center gap-2 font-light text-[14px] leading-5">
            <Link
              href="/token-lock"
              className="cursor-pointer flex items-center gap-2"
            >
              <ArrowLeftIcon />
              <h1 className="text-white-1000">Token Lock</h1>
            </Link>
            <ChevronRightIcon />
            <h2 className="text-brand-500">Detail</h2>
          </div>
          <div className="max-w-[800px] mx-auto md:mt-[69px] mt-4 w-full">
            <div className="flex justify-center items-center min-h-[400px]">
              <div className="text-white-500 font-normal text-[14px] leading-5">
                Loading token lock details...
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error || !accessToken) {
    return (
      <div className="mx-auto md:px-[56px] p-4 md:py-[24px]">
        <div className="flex flex-col md:mt-[55px] mt-[14px]">
          <div className="flex items-center gap-2 font-light text-[14px] leading-5">
            <Link
              href="/token-lock"
              className="cursor-pointer flex items-center gap-2"
            >
              <ArrowLeftIcon />
              <h1 className="text-white-1000">Token Lock</h1>
            </Link>
            <ChevronRightIcon />
            <h2 className="text-brand-500">Detail</h2>
          </div>
          <div className="max-w-[800px] mx-auto md:mt-[69px] mt-4 w-full">
            <div className="flex flex-col justify-center items-center min-h-[400px]">
              <div className="text-red-500 font-normal text-[16px] leading-6 mb-4">
                {!accessToken
                  ? 'Please connect your wallet to view token lock details.'
                  : error || 'Token lock not found.'}
              </div>
              <AppButton onClick={() => router.push('/token-lock')}>
                Go Back to Token Lock List
              </AppButton>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!tokenLock) {
    return (
      <div className="mx-auto md:px-[56px] p-4 md:py-[24px]">
        <div className="flex flex-col md:mt-[55px] mt-[14px]">
          <div className="flex items-center gap-2 font-light text-[14px] leading-5">
            <Link
              href="/token-lock"
              className="cursor-pointer flex items-center gap-2"
            >
              <ArrowLeftIcon />
              <h1 className="text-white-1000">Token Lock</h1>
            </Link>
            <ChevronRightIcon />
            <h2 className="text-brand-500">Detail</h2>
          </div>
          <div className="max-w-[800px] mx-auto md:mt-[69px] mt-4 w-full">
            <div className="flex flex-col justify-center items-center min-h-[400px]">
              <div className="text-white-500 font-normal text-[16px] leading-6 mb-4">
                Token lock details not available.
              </div>
              <AppButton onClick={() => router.push('/token-lock')}>
                Go Back to Token Lock List
              </AppButton>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="mx-auto md:px-[56px] p-4 md:py-[24px]">
      <div className="flex flex-col md:mt-[55px] mt-[14px]">
        <div className="flex items-center gap-2 font-light text-[14px] leading-5">
          <Link
            href="/token-lock"
            className="cursor-pointer flex items-center gap-2"
          >
            <ArrowLeftIcon />
            <h1 className="text-white-1000 ">Token Lock</h1>
          </Link>

          <ChevronRightIcon />
          <h2 className="text-brand-500">Detail</h2>
        </div>

        <div className="max-w-[800px] mx-auto md:mt-[69px] mt-4 w-full">
          <div className="flex items-center gap-2 mb-4">
            <div className=" flex gap-2">
              <AppNumber
                value={tokenLock?.amount}
                className="font-semibold text-[18px] leading-[120%]"
              ></AppNumber>
              {tokenLock?.tokenInfo?.symbol}
              <span className="text-white-500 font-normal text-[14px] leading-5">
                Token Lock
              </span>
            </div>
            {tokenLock?.closed ? (
              <div className="font-normal text-[10px] leading-[16px] px-1 h-[18px] rounded-[4px] bg-red-900 text-red-500">
                Unlocked
              </div>
            ) : (
              <div className="font-normal text-[10px] leading-[16px] px-1 h-[18px] rounded-[4px] bg-green-900 text-green-500">
                Locked
              </div>
            )}
          </div>

          <div className="flex items-center gap-2">
            <span className="text-white-500 font-normal text-[14px] leading-5">
              Contract ID:
            </span>
            <AppAddress
              address={tokenLock?.tokenInfo?.tokenAddress}
              className="font-light text-[14px] leading-5"
            ></AppAddress>
            <div
              className="cursor-pointer"
              onClick={() =>
                copyToClipboard(tokenLock?.tokenInfo?.tokenAddress)
              }
            >
              <CopyIcon />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-2 my-[30px]">
            <div
              className="p-[12px] rounded-[2px] border-white-100 border bg-white-100"
              style={{
                backdropFilter: 'blur(calc(var(--16, 16px) / 2))',
              }}
            >
              <div className="font-normal text-[14px] leading-5 mb-2 text-white-500">
                Locked
              </div>
              <div className="flex gap-2 items-center">
                {tokenLock?.tokenInfo?.logoUri && (
                  <Image
                    src={tokenLock?.tokenInfo?.logoUri as string}
                    alt={tokenLock?.tokenInfo?.symbol}
                    width={16}
                    height={16}
                    className="w-[16px] h-[16px] object-cover aspect-[1] rounded-full"
                  />
                )}
                <div className="heading-md-medium-18 text-brand-500">
                  <AppNumber
                    value={tokenLock?.closed ? '0' : tokenLock?.amount}
                  />
                </div>
                <div className="heading-md-medium-18 text-white-500">
                  {tokenLock?.tokenInfo?.symbol}
                </div>
              </div>
            </div>
            <div
              className="p-[12px] rounded-[2px] border-white-100 border bg-white-100"
              style={{
                backdropFilter: 'blur(calc(var(--16, 16px) / 2))',
              }}
            >
              <div className="font-normal text-[14px] leading-5 mb-2 text-white-500">
                Claimed
              </div>
              <div className="flex gap-2 items-center">
                {tokenLock?.tokenInfo?.logoUri && (
                  <Image
                    src={tokenLock?.tokenInfo?.logoUri as string}
                    alt={tokenLock?.tokenInfo?.symbol}
                    width={16}
                    height={16}
                    className="w-[16px] h-[16px] object-cover aspect-[1] rounded-full"
                  />
                )}
                <div className="heading-md-medium-18 text-brand-500">
                  <AppNumber
                    value={tokenLock?.closed ? tokenLock?.amount : '0'}
                  />
                </div>
                <div className="heading-md-medium-18 text-white-500">
                  {tokenLock?.tokenInfo?.symbol}
                </div>
              </div>
            </div>
          </div>

          <div className="mb-[30px]">
            <div className="flex justify-between items-center mb-4">
              <div className="font-normal text-[14px] leading-5 text-white-500">
                Unlock Time
              </div>
              <div className="font-semibold text-[16px] leading-6">
                {formatUnixTimestamp(tokenLock?.endTime)}
              </div>
            </div>
            <div className="flex justify-between items-center">
              <div className="font-normal text-[14px] leading-5 text-white-500">
                Token Address
              </div>
              <div className="font-semibold text-[16px] leading-6 flex items-center gap-2">
                <AppAddress
                  address={tokenLock?.tokenInfo?.tokenAddress}
                  start={6}
                  end={6}
                ></AppAddress>
                <div
                  className="cursor-pointer"
                  onClick={() =>
                    copyToClipboard(tokenLock?.tokenInfo?.tokenAddress)
                  }
                >
                  <CopyIcon />
                </div>
              </div>
            </div>
          </div>

          <AppButton
            className="w-full"
            disabled={isWithdrawing || isInvalidWithdraw}
            onClick={handleWithdraw}
          >
            {isWithdrawing ? (
              <div className="flex items-center justify-center gap-2">
                <div className="w-4 h-4 border-2 border-white-300 border-t-white-1000 rounded-full animate-spin"></div>
                Withdrawing...
              </div>
            ) : isInvalidWithdraw ? (
              tokenLock?.closed ? (
                'Already Withdrawn'
              ) : moment(tokenLock?.endTime).isAfter(moment()) ? (
                'Lock Not Expired'
              ) : (
                'Withdraw'
              )
            ) : (
              'Withdraw'
            )}
          </AppButton>
        </div>
      </div>
    </div>
  );
};

export default TokenLockPageDetail;
