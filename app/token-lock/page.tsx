'use client';

import { CopyIcon, ArrowLeftIcon } from '@/assets/icons';
import AppAddress from '@/components/AppAddress';
import { AppButton } from '@/components/AppButton';
import { AppDataTable } from '@/components/AppDataTable';
import { AppNumber } from '@/components/AppNumber';
import { useWindowSize } from '@/hooks/useWindowSize';
import { formatUnixTimestamp } from '@/utils/format';
import { copyToClipboard } from '@/utils/helper';
import RequestFactory from '@/services/RequestFactory';
import { CoinLockDto } from '@/services/CoinLockRequest';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import React, { useMemo, useRef, useState, useEffect } from 'react';

const TokenLockPage = () => {
  const [hasLockedTokens, setHasLockedTokens] = useState<boolean>(false);
  const dataTableRef = useRef<HTMLDivElement | null>(null);
  const router = useRouter();
  const { windowHeight } = useWindowSize();

  const { accessToken } = useSelector((state: RootState) => state.user);
  const coinLockRequest = RequestFactory.getRequest('CoinLockRequest');

  useEffect(() => {
    if (!accessToken) {
      setHasLockedTokens(false);
    }
  }, [accessToken]);

  const getCoinsLock = async (
    params: { page?: number; limit?: number } = {},
  ) => {
    try {
      if (!accessToken) {
        console.warn('User not authenticated, showing empty state');
        setHasLockedTokens(false);
        return {
          data: [],
          total: 0,
          totalPages: 0,
        };
      }

      const response = await coinLockRequest.getCoinLocks(params);
      setHasLockedTokens(response.totalDocs > 0);

      return {
        data: response.docs,
        total: response.totalDocs,
        totalPages: response.totalPages,
      };
    } catch (error) {
      console.error('Error fetching coin locks:', error);
      setHasLockedTokens(false);
      return {
        data: [],
        total: 0,
        totalPages: 0,
      };
    }
  };

  const tableHeight = useMemo(() => {
    return windowHeight - 446;
  }, [windowHeight]);

  const renderContent = () => {
    if (!accessToken) {
      return (
        <div className="md:w-full w-[343px] h-full max-w-[618px] flex flex-col items-center justify-center">
          <div className="font-semibold md:text-[40px] text-[24px] leading-[120%] text-white-1000 mb-[12px]">
            Please Connect Wallet
          </div>
          <div className="font-normal md:text-[18px] text-[14px] leading-5 md:leading-[120%] text-white-500 mb-[12px] text-center">
            Connect your wallet to view your token locks.
          </div>
        </div>
      );
    }

    return (
      <>
        <div className="md:hidden fixed top-[90px] left-0 w-full px-4 py-2 flex items-center justify-center z-[999]">
          <span className="absolute left-4 top-1/2 -translate-y-1/2 flex items-center">
            <ArrowLeftIcon
              className="w-6 h-6"
              onClick={() => router.push('/token-lock')}
            />
          </span>
          <h1 className="font-semibold text-[20px] leading-[24px] text-center w-full select-none text-white tracking-widest">
            Token Lock
          </h1>
        </div>

        <div className="flex flex-col w-full min-h-[60vh] pt-[16px] md:pt-0">
          <div className="hidden md:flex mx-auto w-[618px] flex-col items-center mb-[24px]">
            <div
              className="font-semibold text-[40px] leading-[120%] text-white-1000 text-center mb-2"
              style={{ letterSpacing: 2 }}
            >
              Stake & Earn
            </div>
            <div className="font-normal text-[18px] leading-[120%] text-white-500 text-center">
              Unlock Rewards (upto 35%) with $SHR0 and any token on Print.meme
            </div>
          </div>

          <div className="hidden md:flex justify-between items-center mx-[56px]">
            <h1 className="font-semibold text-[16px] leading-[24px]">
              Token Lock
            </h1>
            <Link href="/token-lock/create">
              <AppButton>Create New</AppButton>
            </Link>
          </div>

          <div className="border border-white-50 mx-[16px] md:mx-[56px] overflow-x-auto mt-0 md:mt-6 flex-1">
            <AppDataTable
              ref={dataTableRef}
              getData={getCoinsLock}
              minWidth={885}
              height={tableHeight}
              shouldAutoFetchOnInit={!!accessToken}
              renderHeader={() => {
                return (
                  <div className="flex items-center font-light text-[12px] leading-[18px] text-white-500 gap-2 min-h-[36px] w-[885px] md:w-full px-2 py-[6px]">
                    <div className="w-[20%]">Token&apos;s name</div>
                    <div className="w-[20%]">Lock Time</div>
                    <div className="w-[20%]">Unlock Time</div>
                    <div className="w-[20%]">Locked Amount</div>
                    <div className="w-[20%]">Status</div>
                  </div>
                );
              }}
              renderRow={(item: CoinLockDto) => {
                return (
                  <Link
                    className="flex hover:bg-white-100 items-center gap-2 py-[10px] px-2 min-h-[36px] md:min-h-[40px] w-[885px] md:w-full cursor-pointer"
                    href={`/token-lock/${item?.contractId}`}
                  >
                    <div className="flex items-center w-[20%]">
                      <Image
                        src={
                          item?.tokenInfo?.logoUri || '/images/TokenLogo.png'
                        }
                        alt={item?.tokenInfo?.symbol || 'token'}
                        width={24}
                        height={24}
                        className="rounded-full aspect-[1/1] object-cover"
                      />

                      <div className="pl-1">
                        <div className="text-white-1000 font-normal text-[14px] leading-5">
                          {item?.tokenInfo?.symbol}
                        </div>
                        <div className="flex items-center gap-1 font-light leading-4 text-[8px] md:text-[10px] text-white-500">
                          <AppAddress
                            address={item?.tokenInfo?.tokenAddress || ''}
                            start={4}
                            end={4}
                          />

                          <div
                            className="cursor-pointer"
                            onClick={(e) => {
                              e.preventDefault();
                              copyToClipboard(
                                item?.tokenInfo?.tokenAddress || '',
                              );
                            }}
                          >
                            <CopyIcon />
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="w-[20%] font-normal text-[14px] leading-5">
                      {formatUnixTimestamp(
                        item?.startTime,
                        'MMM DD, YYYY HH:mm',
                      )}
                    </div>

                    <div className="w-[20%] font-normal text-[14px] leading-5">
                      {formatUnixTimestamp(item?.endTime, 'MMM DD, YYYY HH:mm')}
                    </div>

                    <div className="w-[20%] font-normal text-[14px] leading-5 flex items-center gap-2">
                      <AppNumber value={item?.amount || '0'}></AppNumber>
                      <div>{item?.tokenInfo?.symbol}</div>
                    </div>

                    <div className="w-[20%] font-normal text-[10px] leading-4 h-[16px] md:h-[18px]">
                      {item?.closed ? (
                        <div className="rounded inline-flex flex-col justify-center items-start bg-red-900 text-red-500 px-1">
                          Unlocked
                        </div>
                      ) : (
                        <div className="rounded inline-flex flex-col justify-center items-start bg-green-900 text-green-500 px-1">
                          Locked
                        </div>
                      )}
                    </div>
                  </Link>
                );
              }}
            />
          </div>

          <div className="block md:hidden mt-4 px-4">
            <Link href="/token-lock/create">
              <AppButton className="w-full">Create New</AppButton>
            </Link>
          </div>
        </div>
      </>
    );

    // return (
    //   <div className="md:w-full w-[343px] h-full max-w-[618px] flex flex-col items-center justify-center">
    //     <div className="font-semibold md:text-[40px] text-[24px] leading-[120%] text-white-1000 mb-[12px]">
    //       No Token Locked
    //     </div>
    //     <div className="font-normal md:text-[18px] text-[14px] leading-5 md:leading-[120%] text-white-500 mb-[12px] text-center">
    //       Lock tokens into audited smart contracts that unlock to the recipient
    //       after the lock time expires.
    //     </div>
    //     <AppButton onClick={() => router.push('/token-lock/create')}>
    //       Create New
    //     </AppButton>
    //   </div>
    // );
  };

  return (
    <div className="md:bg-[url('../assets/images/banner.png')] bg-[url('../assets/images/banner-mobile.png')] mx-auto bg-cover bg-top bg-no-repeat aspect-[1440/640] bg-black-900 w-full">
      {hasLockedTokens && accessToken ? (
        <div className="w-full h-full flex justify-center items-start pt-[24px] md:pt-[80px]">
          {renderContent()}
        </div>
      ) : (
        <div className="w-full h-full flex justify-center items-center">
          {renderContent()}
        </div>
      )}
    </div>
  );
};

export default TokenLockPage;
