'use client';
import {
  ArrowLeftIcon,
  CalendarIcon,
  ChevronRightIcon,
  CopyIcon,
  InformationCircleIcon,
  TimerIcon,
} from '@/assets/icons';
import { AppButton } from '@/components/AppButton';
import { AppNumber } from '@/components/AppNumber';
import { useTokenLock, useTokenValidation } from '@/hooks';
import { useSigner } from '@/hooks/useSigner';
import ModalContractExpired from '@/modals/ModalContractExpired';
import ModalTokenLocked from '@/modals/ModalTokenLocked';
import { NumericFormat } from '@/node_modules/react-number-format';
import { ETH_DECIMALS } from '@/utils/constants';
import {
  formatNumber,
  formatShortAddress,
  formatUnixTimestamp,
} from '@/utils/format';
import { copyToClipboard, sleep } from '@/utils/helper';
import { formatUnits } from 'ethers';
import BigNumber from 'bignumber.js';
import Link from 'next/link';
import { forwardRef, useEffect, useState } from 'react';
import DatePicker from 'react-datepicker';
import { useRouter } from 'next/navigation';

enum STEP {
  STEP_1,
  STEP_2,
}

const CustomDateInput = forwardRef<
  HTMLDivElement,
  { value?: string; onClick?: () => void }
>(({ value, onClick }, ref) => (
  <div
    onClick={onClick}
    ref={ref}
    className="p-2 flex gap-2 items-center justify-between w-full body-sm-medium-12 border border-white-100 rounded-[6px] bg-black-900"
  >
    {value ? value : <span className="text-white-300">Select date</span>}
    <CalendarIcon />
  </div>
));

CustomDateInput.displayName = 'CustomDateInput';

const CustomTimeInput = forwardRef<
  HTMLDivElement,
  { value?: string; onClick?: () => void }
>(({ value, onClick }, ref) => (
  <div
    onClick={onClick}
    ref={ref}
    className="p-2 flex gap-2 items-center justify-between w-full body-sm-medium-12 border border-white-100 rounded-[6px] bg-black-900"
  >
    {value ? value : <span className="text-white-300">Select time</span>}
    <TimerIcon />
  </div>
));

CustomTimeInput.displayName = 'CustomTimeInput';

const StepConfiguration = ({
  amount,
  time,
  token,
  isDisabledNextStep,
  setToken,
  setAmount,
  setTime,
  setStep,
}: {
  amount: string;
  token: any;
  time: any;
  isDisabledNextStep: boolean;
  setAmount: (value: string) => void;
  setTime: (value: any) => void;
  setToken: (value: any) => void;
  setStep: (step: number) => void;
}) => {
  const [tokenAddress, setTokenAddress] = useState<string>(
    token?.tokenAddress || '',
  );
  const { isValidating, tokenInfo, error } = useTokenValidation(tokenAddress);

  useEffect(() => {
    if (tokenInfo) {
      setToken({
        tokenAddress: tokenInfo.tokenAddress,
        name: tokenInfo.name,
        symbol: tokenInfo.symbol,
        decimals: tokenInfo.decimals,
        balance: formatUnits(tokenInfo.balance, tokenInfo.decimals),
      });
    } else if (!tokenAddress) {
      setToken({});
    }
  }, [tokenInfo, tokenAddress, setToken]);

  const handleTokenAddressChange = async (address: string) => {
    setTokenAddress(address);

    if (!address.trim()) {
      setToken({});
      return;
    }
  };

  const handleDateChange = (date: any) => {
    if (!date) return;
    const newDate = new Date(time || new Date());
    newDate.setFullYear(date.getFullYear());
    newDate.setMonth(date.getMonth());
    newDate.setDate(date.getDate());
    setTime(newDate);
  };

  const handleTimeChange = (time: any) => {
    if (!time) return;
    const newDate = new Date(time);
    newDate.setHours(time.getHours());
    newDate.setMinutes(time.getMinutes());
    newDate.setSeconds(time.getSeconds());
    setTime(newDate);
  };

  return (
    <div className="flex-1 flex flex-col">
      <div className="flex gap-2 flex-col">
        <div className="action-sm-regular-12 text-white-700">Token Address</div>
        <div className="relative">
          <input
            type="text"
            value={tokenAddress}
            onChange={(e) => handleTokenAddressChange(e.target.value)}
            placeholder="Enter token contract address (0x...)"
            className="p-2 w-full body-sm-medium-12 border border-white-100 rounded-[6px] bg-black-900 text-white-1000 placeholder:text-white-300 focus:outline-none focus:border-brand-500"
            disabled={isValidating}
          />
          {isValidating && (
            <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
              <div className="w-4 h-4 border-2 border-white-300 border-t-brand-500 rounded-full animate-spin"></div>
            </div>
          )}
        </div>

        {error && <div className="text-red-500 text-sm">{error}</div>}

        {tokenInfo && (
          <div className="p-3 border border-white-100 rounded-[6px] bg-white-100">
            <div className="flex items-center justify-between">
              <div className="flex flex-col gap-1">
                <div className="body-sm-medium-12 text-white-1000">
                  {tokenInfo.name}
                </div>
                <div className="flex items-center gap-2">
                  <span className="action-sm-regular-12 text-white-500">
                    {tokenInfo.symbol}
                  </span>
                  <span className="action-sm-regular-12 text-white-500">
                    • {tokenInfo.decimals} decimals
                  </span>
                </div>
              </div>
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            </div>
          </div>
        )}

        <div className="flex justify-between">
          <div className="action-sm-regular-12 text-white-1000">Balance</div>
          <div className="action-sm-regular-12 flex items-center gap-1">
            <div className="text-white-1000">
              <AppNumber value={token?.balance || '0'} />
            </div>
            <div className="text-white-500">{token?.symbol || 'TOKEN'}</div>
          </div>
        </div>
      </div>

      <div className="flex gap-2 flex-col mt-4">
        <div className="action-sm-regular-12 text-white-700">Lock Amount</div>
        <div className="p-2 border border-white-100 rounded-[6px] bg-black-900 flex justify-between gap-2">
          <NumericFormat
            value={amount}
            onValueChange={(values) => setAmount(values.value)}
            thousandSeparator=","
            decimalSeparator="."
            allowNegative={false}
            placeholder="0.00"
            className="body-sm-medium-12 placeholder:text-white-300 bg-transparent flex-1 appearance-none focus:outline-none"
            allowLeadingZeros={false}
            decimalScale={token?.decimals || ETH_DECIMALS}
            inputMode="numeric"
          />
          <div className="flex gap-2 items-center">
            <div
              className="body-md-light-14 text-white-1000 cursor-pointer hover:text-brand-500"
              onClick={() =>
                setAmount(
                  BigNumber(token?.balance || '0').toFixed(
                    token?.decimals,
                    BigNumber.ROUND_DOWN,
                  ),
                )
              }
            >
              Max
            </div>
            {token?.symbol && (
              <div className="body-sm-medium-12 text-white-500">
                {token.symbol}
              </div>
            )}
          </div>
        </div>

        {amount &&
          token?.balance &&
          new BigNumber(amount).isGreaterThan(token.balance || '0') && (
            <div className="text-red-500 text-sm">
              Insufficient balance. You have{' '}
              {formatNumber(token.balance, 8, '0')} {token.symbol}
            </div>
          )}
      </div>

      <div className="flex gap-2 flex-col mt-4">
        <div>
          <div className="action-sm-regular-12 mb-2 text-white-1000">
            Unlock Settings
          </div>
          <div className="action-sm-regular-12 text-white-500">
            Set the date and time when the token will unlocked
          </div>
        </div>

        <div className="grid grid-cols-2 gap-2">
          <div className="w-full">
            <DatePicker
              selected={time}
              onChange={handleDateChange}
              dateFormat="dd/MM/YYYY"
              customInput={<CustomDateInput />}
              minDate={new Date()}
            />
          </div>
          <div className="w-full">
            <DatePicker
              selected={time}
              onChange={handleTimeChange}
              showTimeSelectOnly
              showTimeInput
              dateFormat="HH:mm"
              customInput={<CustomTimeInput />}
            />
          </div>
        </div>
      </div>

      <AppButton
        className="w-full mt-8"
        onClick={() => setStep(STEP.STEP_2)}
        disabled={isDisabledNextStep}
      >
        Next Step
      </AppButton>
    </div>
  );
};

const StepReviews = ({
  amount,
  token,
  lockTime,
  setStep,
  isCreatingLock,
  createTokenLock,
}: {
  amount: string;
  token: any;
  lockTime: any;
  setStep: (step: number) => void;
  isCreatingLock: boolean;
  createTokenLock: () => Promise<void>;
}) => {
  const signer = useSigner();

  return (
    <div className="flex-1 flex flex-col">
      <div className="grid grid-cols-2 gap-[12px]">
        <div
          className="p-[12px] rounded-[2px] border-white-100 border bg-white-100"
          style={{
            backdropFilter: 'blur(calc(var(--16, 16px) / 2))',
          }}
        >
          <div className="body-md-regular-14 mb-2 text-white-500">
            Unlock Amount
          </div>
          <div className="flex gap-2 items-center">
            <div className="font-semibold text-[18px] leading-[120%] text-brand-500">
              {formatNumber(amount)}
            </div>
          </div>
        </div>
        <div
          className="p-[12px] rounded-[2px] border-white-100 border bg-white-100"
          style={{
            backdropFilter: 'blur(calc(var(--16, 16px) / 2))',
          }}
        >
          <div className="body-md-regular-14 mb-2 text-white-500">
            Unlock Time
          </div>
          <div className="flex gap-2 items-center">
            <div className="font-semibold text-[18px] leading-[120%] text-brand-500">
              {formatUnixTimestamp(lockTime, 'MMM DD, YYYY HH:mm')}
            </div>
          </div>
        </div>
      </div>

      <div className="my-[30px]">
        <div className="body-md-regular-14 text-white-500 mb-2">
          Recipient’s Wallet Address after unlocked:
        </div>
        <div className="flex gap-2 items-center body-md-light-14">
          {formatShortAddress(signer.signer?.address || '')}
          <div
            className="cursor-pointer"
            onClick={() => copyToClipboard(signer.signer?.address || '')}
          >
            <CopyIcon />
          </div>
        </div>
      </div>

      <div className="p-[12px] flex gap-2 border-dashed border-white-50 bg-[#769CD64D] rounded-[2px]">
        <div>
          <InformationCircleIcon className="text-blue-500" />
        </div>
        <div>
          <div className="font-light text-[14px] leading-[20px] text-blue-500 ">
            Token locks are irreversible
          </div>
          <div className="font-light text-[12px] leading-[18px] text-[#769CD699]">
            Please note that locked tokens cannot be moved, accessed, or
            unlocked under any circumstances before the unlock date. This
            ensures system integrity and trust. By proceeding, you acknowledge
            and accept this condition. Contact us with any questions before
            locking.
          </div>
        </div>
      </div>

      <div className="flex justify-between items-center gap-[10px] mt-[30px]">
        <div className="md:w-full w-[223px] max-w-[416px]">
          <div className="font-normal text-[12px] leading-[18px] text-white-1000 mb-1">
            Token Lock Fees
          </div>
          <div className="font-normal text-[12px] leading-[18px] text-white-500">
            The service fee is added on top of the total contract amount
          </div>
        </div>

        <div className="text-brand-500 font-semibold text-[18px] leading-[120%]">
          0.19%
        </div>
      </div>

      <div className="grid grid-cols-2 gap-2 mt-[10px]">
        <div>
          <AppButton
            onClick={() => setStep(STEP.STEP_1)}
            className="w-full font-normal !p-2 !rounded text-[12px] md:leading-[18px] leading-4"
            buttonType="outlined-black"
          >
            Back
          </AppButton>
        </div>
        <div>
          <AppButton
            className="w-full font-normal !p-2 !rounded text-[12px] md:leading-[18px] leading-4 whitespace-nowrap"
            onClick={createTokenLock}
            disabled={isCreatingLock}
          >
            {isCreatingLock ? (
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-white-300 border-t-white-1000 rounded-full animate-spin"></div>
                Creating Lock...
              </div>
            ) : (
              'Create Lock'
            )}
          </AppButton>
        </div>
      </div>
    </div>
  );
};

const TokenLockCreate = () => {
  const [step, setStep] = useState<number>(STEP.STEP_1);
  const [lockAmount, setLockAmount] = useState<string>('');
  const [lockTime, setLockTime] = useState<any>();
  const [token, setToken] = useState<any>({});
  const [isShowModalTokenLocked, setIsShowModalTokenLocked] =
    useState<boolean>(false);
  const [isShowModalExpiredTime, setIsShowModalExpiredTime] =
    useState<boolean>(false);
  const router = useRouter();

  const { createLock, isCreatingLock } = useTokenLock();

  const createTokenLock = async () => {
    if (!token?.tokenAddress || !lockAmount || !lockTime) {
      return;
    }

    try {
      const currentTime = new Date();
      const lockDurationMs = lockTime.getTime() - currentTime.getTime();

      const lockDurationSeconds = Math.ceil(lockDurationMs / 1000);

      if (lockDurationSeconds <= 0) {
        throw new Error('Lock time must be in the future');
      }

      const lockId = await createLock({
        tokenAddress: token.tokenAddress,
        amount: lockAmount,
        lockDurationSeconds,
      });

      if (lockId) {
        console.log('Token lock created successfully with ID:', lockId);
        setIsShowModalTokenLocked(true);
        await sleep(2000);
        router.push(`/token-lock/${lockId}`);
      } else {
        console.error('Failed to create token lock');
      }
    } catch (error: any) {
      console.error('Error creating token lock:', error);
    }
  };

  const isDisabledNextStep =
    +lockAmount <= 0 ||
    !token?.tokenAddress ||
    !lockTime ||
    (lockTime && lockTime.getTime() <= new Date().getTime()) ||
    (lockAmount &&
      token?.balance &&
      new BigNumber(lockAmount).isGreaterThan(token.balance || '0'));

  return (
    <div className="mx-auto md:px-[56px] p-4 md:py-[24px]">
      <div className="flex flex-col md:mt-[55px] mt-[14px]">
        <div className="flex items-center gap-2 font-normal text-[14px] leading-5">
          <Link
            href="/token-lock"
            className="cursor-pointer flex items-center gap-2"
          >
            <ArrowLeftIcon />
            <h1 className="text-white-1000">Token Lock</h1>
          </Link>

          <ChevronRightIcon />
          <h2 className="text-brand-500">Create New</h2>
        </div>

        <div className="mx-auto max-w-[800px] tablet:mt-[69px] mt-4 w-full flex tablet:flex-row flex-col tablet:gap-8 gap-4">
          <div className="md:pr-8 pr-4 tablet:border-r border-white-100 tablet:items-start items-center flex tablet:flex-col flex-row">
            <div className="flex gap-2 items-center">
              <div
                className={`${
                  step > STEP.STEP_1
                    ? 'bg-brand-500 text-black-900 font-semibold text-[16px] leading-6'
                    : 'bg-white-1000 text-black-900'
                } rounded-full md:w-[42px] md:h-[42px] w-8 h-8 flex items-center justify-center`}
              >
                {step > STEP.STEP_1 ? '✓' : '1'}
              </div>
              <div
                className={`font-semibold md:text-[18px] text-[16px] leading-6 md:leading-[120%] ${
                  step > STEP.STEP_1 ? 'text-brand-500' : ''
                }`}
              >
                Configuration
              </div>
            </div>

            <div className="md:h-[80px] w-[1px] h-[33px] md:w-[1px] bg-white-100 mx-[21px] my-2 md:rotate-0 rotate-90"></div>

            <div className="flex gap-2 items-center">
              <div
                className={` border border-white-100 ${
                  step < STEP.STEP_2
                    ? 'text-black-900 bg-white-500'
                    : 'bg-white-1000 text-black-900'
                } rounded-full md:w-[42px] md:h-[42px] w-8 h-8 flex items-center justify-center`}
              >
                2
              </div>
              <div
                className={`font-semibold md:text-[18px] text-[16px] leading-6 md:leading-[120%] ${
                  step < STEP.STEP_2 ? 'text-white-500' : ''
                }`}
              >
                Review
              </div>
            </div>
          </div>

          {step === STEP.STEP_1 ? (
            <StepConfiguration
              setStep={setStep}
              setAmount={setLockAmount}
              amount={lockAmount}
              setTime={setLockTime}
              time={lockTime}
              setToken={setToken}
              token={token}
              isDisabledNextStep={isDisabledNextStep}
            />
          ) : (
            <StepReviews
              setStep={setStep}
              token={token}
              amount={lockAmount}
              lockTime={lockTime}
              isCreatingLock={isCreatingLock}
              createTokenLock={createTokenLock}
            />
          )}
        </div>
      </div>

      {isShowModalTokenLocked && (
        <ModalTokenLocked
          isOpen={isShowModalTokenLocked}
          onClose={() => setIsShowModalTokenLocked(false)}
        />
      )}

      {isShowModalExpiredTime && (
        <ModalContractExpired
          isOpen={isShowModalExpiredTime}
          onClose={() => setIsShowModalExpiredTime(false)}
        />
      )}
    </div>
  );
};

export default TokenLockCreate;
