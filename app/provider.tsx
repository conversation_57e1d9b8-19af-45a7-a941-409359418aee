'use client';

import config, { hypeMain<PERSON><PERSON>hain } from '@/config';
import { closeSocketInstance, createSocketInstance } from '@/libs/socket';
import { store } from '@/store';
import { TokenInitializer } from '@/components/TokenInitializer';
import { SuiClientProvider, WalletProvider } from '@mysten/dapp-kit';
import { getFullnodeUrl } from '@mysten/sui/client';
import { PrivyProvider } from '@privy-io/react-auth';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useEffect } from 'react';
import { Provider } from 'react-redux';

const queryClient = new QueryClient();

const networks = {
  testnet: { url: getFullnodeUrl('testnet') },
  mainnet: { url: getFullnodeUrl('mainnet') },
  localnet: { url: getFullnodeUrl('localnet') },
  devnet: { url: getFullnodeUrl('devnet') },
};

export default function RootProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const appId = config.privyAppId;

  if (!appId) {
    console.error('Privy app ID is not set');
  }

  useEffect(() => {
    createSocketInstance({ network: 'hyperliquid' });
    return () => {
      closeSocketInstance('hyperliquid');
    };
  }, []);

  return (
    <Provider store={store}>
      <TokenInitializer />
      <PrivyProvider
        appId={appId}
        config={{
          loginMethods: ['wallet', 'email', 'google', 'twitter'],
          embeddedWallets: {
            createOnLogin: 'users-without-wallets',
          },
          appearance: {
            walletChainType: 'ethereum-only',
            theme: 'dark',
            accentColor: '#676FFF',
          },
          defaultChain: hypeMainnetChain,
          supportedChains: [hypeMainnetChain],
        }}
      >
        <QueryClientProvider client={queryClient}>
          <SuiClientProvider networks={networks} network="devnet">
            <WalletProvider autoConnect>{children}</WalletProvider>
          </SuiClientProvider>
        </QueryClientProvider>
      </PrivyProvider>
    </Provider>
  );
}
