import { Footer } from '@/components/layout/footer';
import { Header } from '@/components/layout/header';
import { Trending } from '@/components/layout/trending';
import RootProvider from './provider';
import { GoogleAnalytics } from '@next/third-parties/google';
import { Metadata } from 'next';
import 'rc-tooltip/assets/bootstrap.css';
import React from 'react';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import './globals.css';
import 'react-datepicker/dist/react-datepicker.css';

export const metadata: Metadata = {
  metadataBase: new URL('https://dev-printmeme.vercel.app'),
  openGraph: {
    title: 'Print.meme',
    description: 'Print.meme',
    siteName: 'Print.meme',
    images: [
      {
        url: '/opengraph-image.png',
        alt: 'Print.meme',
      },
    ],
  },
  title: 'Print.meme',
  description: 'Print.meme',
  icons: {
    icon: '/logo-favicon.png',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body>
        <RootProvider>
          <ToastContainer
            autoClose={2000}
            position="top-right"
            icon={false}
            pauseOnHover
            closeButton={false}
            hideProgressBar
            toastStyle={{
              position: 'relative',
              overflow: 'visible',
            }}
          />
          <div>
            <Trending />
            <Header />
            <div className="pt-[44px]">{children}</div>
            <Footer />
          </div>
        </RootProvider>
      </body>
      <GoogleAnalytics gaId="G-E8VWZLW24D" />
    </html>
  );
}
