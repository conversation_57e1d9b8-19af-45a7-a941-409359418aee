import config from '@/config';

import LaunchpadAB<PERSON> from '@/abi/Launchpad.json';
import TokenLockABI from '@/abi/TokenLock.json';
import SwapRouterAB<PERSON> from '@/abi/SwapRouter.json';
import UniswapV3PoolABI from '@/abi/UniswapV3Pool.json';
import CreatorRewardABI from '@/abi/CreatorReward.json';
export const CONTRACT_ADDRESSES = {
  LAUNCHPAD:
    config.contracts?.launchpadAddress ||
    '******************************************',

  MAIN_TOKEN:
    config.contracts?.mainTokenAddress ||
    '******************************************',

  TOKEN_LOCK:
    config.contracts?.tokenLockAddress ||
    '******************************************',

  FEE_RECIPIENT:
    config.feeRecipient || '******************************************',

  SWAP_ROUTER_V3:
    config.contracts?.swapRouterV3Address ||
    '******************************************',

  WETH:
    config.contracts?.wethAddress ||
    '******************************************',

  MULTICALL:
    config.contracts?.multicallAddress ||
    '******************************************',

  CREATOR_REWARD:
    config.contracts?.creatorReward ||
    '******************************************',
};

// Basic ERC-20 ABI for token interactions
export const ERC20_ABI = [
  'function name() view returns (string)',
  'function symbol() view returns (string)',
  'function decimals() view returns (uint8)',
  'function totalSupply() view returns (uint256)',
  'function balanceOf(address) view returns (uint256)',
  'function transfer(address to, uint256 amount) returns (bool)',
  'function allowance(address owner, address spender) view returns (uint256)',
  'function approve(address spender, uint256 amount) returns (bool)',
  'function transferFrom(address from, address to, uint256 amount) returns (bool)',
];

export const MULTICALL_ABI = [
  'function aggregate(tuple(address target, bytes callData)[] calls) view returns (uint256 blockNumber, bytes[] returnData)',
];

export const LAUNCHPAD_ABI = LaunchpadABI;

export const TOKEN_LOCK_ABI = TokenLockABI;

export const SWAP_ROUTER_ABI = SwapRouterABI;

export const UNISWAP_V3_POOL_ABI = UniswapV3PoolABI;

export const CREATOR_REWARD_ABI = CreatorRewardABI;
