import config from '@/config';

// Import ABI files
import LaunchpadABI from '@/abi/Launchpad.json';
import TokenLockABI from '@/abi/TokenLock.json';
import SwapRouterAB<PERSON> from '@/abi/SwapRouter.json';
import UniswapV3PoolABI from '@/abi/UniswapV3Pool.json';
import CreatorRewardABI from '@/abi/CreatorReward.json';

// Contract addresses for Hyperliquid EVM
export const CONTRACT_ADDRESSES = {
  // Main launchpad contract (from config)
  LAUNCHPAD:
    config.contracts?.launchpadAddress ||
    '0xda6fCCed61457224e65927b373f57Dae98bF50a7',

  // Main token address (from config)
  MAIN_TOKEN:
    config.contracts?.mainTokenAddress ||
    '******************************************',

  // Token lock contract (from config)
  TOKEN_LOCK:
    config.contracts?.tokenLockAddress ||
    '******************************************',

  // Fee recipient
  FEE_RECIPIENT:
    config.feeRecipient || '******************************************',

  // Swap Router V3 address
  SWAP_ROUTER_V3:
    config.contracts?.swapRouterV3Address ||
    '******************************************',

  // WETH address
  WETH:
    config.contracts?.wethAddress ||
    '******************************************',

  MULTICALL:
    config.contracts?.multicallAddress ||
    '******************************************',

  // Creator Reward contract
  CREATOR_REWARD:
    config.contracts?.creatorReward ||
    '******************************************',
};

// Basic ERC-20 ABI for token interactions
export const ERC20_ABI = [
  'function name() view returns (string)',
  'function symbol() view returns (string)',
  'function decimals() view returns (uint8)',
  'function totalSupply() view returns (uint256)',
  'function balanceOf(address) view returns (uint256)',
  'function transfer(address to, uint256 amount) returns (bool)',
  'function allowance(address owner, address spender) view returns (uint256)',
  'function approve(address spender, uint256 amount) returns (bool)',
  'function transferFrom(address from, address to, uint256 amount) returns (bool)',
];

export const MULTICALL_ABI = [
  'function aggregate(tuple(address target, bytes callData)[] calls) view returns (uint256 blockNumber, bytes[] returnData)',
];

// Hyperliquid Launchpad contract ABI (from ABI file)
export const LAUNCHPAD_ABI = LaunchpadABI;

// TokenLock contract ABI (from ABI file)
export const TOKEN_LOCK_ABI = TokenLockABI;

// Swap Router ABI (from ABI file)
export const SWAP_ROUTER_ABI = SwapRouterABI;

// Uniswap V3 Pool ABI (from ABI file)
export const UNISWAP_V3_POOL_ABI = UniswapV3PoolABI;

// Creator Reward ABI (from ABI file)
export const CREATOR_REWARD_ABI = CreatorRewardABI;
