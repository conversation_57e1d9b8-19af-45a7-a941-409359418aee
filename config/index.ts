import dev from './dev.json';
import local from './local.json';
import prod from './prod.json';

export interface Config {
  apiUrl: string;
  recaptchaSiteKey: string;
  explorerUrl: string;
  network: string;
  feeRecipient: string;
  endpoints: {
    ws: string;
  };
  link: {
    twitter: string;
    telegram: string;
  };
  hyperliquidRpcUrl: string;
  chainId: number;
  privyAppId: string;
  platformFeeRate: string;
  kingTheHill: string;
  bondingConfig: {
    targetRaise: string;
    feeCreatePool: string;
    swapFeeRate: string;
    initVirtualTokenReserves: string;
    remainTokenReserves: string;
  };
  contracts: {
    multicallAddress: `0x${string}`;
    mainTokenAddress: `0x${string}`;
    launchpadAddress: `0x${string}`;
    tokenLockAddress: `0x${string}`;
    creatorReward?: `0x${string}`;
    swapRouterV3Address?: `0x${string}`;
    wethAddress?: `0x${string}`;
  };
}

export const envConfig = process.env.NEXT_PUBLIC_ENV || 'dev';

export const hypeMainnetChain = {
  id: 999,
  name: 'Hype',
  network: 'hype',
  nativeCurrency: {
    name: 'Hype',
    symbol: 'HYPE',
    decimals: 18,
  },
  rpcUrls: {
    default: {
      http: ['https://rpc.hyperliquid.xyz/evm'],
    },
    public: {
      http: ['https://rpc.hyperliquid.xyz/evm'],
    },
  },
};

interface EnvConfig {
  prod: Config;
  dev: Config;
  local: Config;
}

const configs: EnvConfig = { dev, prod, local } as EnvConfig;
const config: Config = configs[envConfig as keyof typeof configs];

export default config;
